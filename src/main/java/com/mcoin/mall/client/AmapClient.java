package com.mcoin.mall.client;

import com.alibaba.csp.sentinel.slots.block.BlockException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

/**
 * <AUTHOR>
 * @date 2024/10/23
 */
@FeignClient(name = "amapClient", url = "${amap.oapi.url}")
public interface AmapClient {

    @GetMapping(value = "/v3/geocode/regeo?output=JSON&location={location}&key={key}&radius=1000&extensions=base")
    String getLocation(@PathVariable("location") String location, @PathVariable("key") String key) throws BlockException;

}