package com.mcoin.mall.client;

import com.alibaba.csp.sentinel.slots.block.BlockException;
import com.mcoin.mall.client.model.DingDingRequest;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(name = "dingDingClient", url = "${dingding.oapi.url}")
public interface DingDingClient {

    @PostMapping(value = "/robot/send?access_token=${dingding.robot.send.token}", consumes = {MediaType.APPLICATION_JSON_VALUE})
    String send(@RequestBody DingDingRequest request) throws BlockException;

    @PostMapping(value = "/robot/send?access_token=${dingding.robot.send.token}", consumes = {MediaType.APPLICATION_JSON_VALUE})
    String send(@RequestBody String data) throws BlockException;


}
