package com.mcoin.mall.client;

import com.alibaba.csp.sentinel.slots.block.BlockException;
import com.mcoin.mall.client.model.MPayQueryRefundRequest;
import com.mcoin.mall.client.model.MPayQueryStatusRequest;
import com.mcoin.mall.client.model.MPayRefundRequest;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(name = "mPayClient", url = "${macaupass.URL}")
public interface MPayClient {

    @GetMapping(value = "/oauth2/access_token?appid=${macaupass.APPID}&secret=${macaupass.SECRET}" +
            "&code={code}&grant_type=authorization_code")
    String accessToken(@PathVariable("code") String code) throws BlockException;
    
    @GetMapping(value = "/oauth2/app_token?appid=${macaupass.APPID}&secret=${macaupass.SECRET}")
    String getAppToken() throws BlockException;
    
    @PostMapping(value = "/oauth2/refresh_token?appid=${macaupass.APPID}&grant_type=refresh_token&refresh_token={refreshToken}")
    String refreshToken(@PathVariable("refreshToken") String refreshToken) throws BlockException;
    
    @GetMapping(value = "/data/getticket?app_token={appToken}&type=jsapi")
    String getTicket(@PathVariable("appToken") String appToken) throws BlockException;

    @GetMapping(value = "/data/userinfo?access_token={appToken}&openid={openid}")
    String getUserInfo(@PathVariable("appToken") String appToken, @PathVariable("openid") String openid) throws BlockException;
    
    @PostMapping(value = "/data/getuserid?openid={openid}")
    String getUserId(@PathVariable("openid") String openid) throws BlockException;

    @PostMapping(value = "/asl/bill/refund", consumes = MediaType.APPLICATION_JSON_VALUE)
    String refund(@RequestBody MPayRefundRequest request) throws BlockException;

    @PostMapping(value = "/asl/bill/queryRefund", consumes = MediaType.APPLICATION_JSON_VALUE)
    String queryRefund(@RequestBody MPayQueryRefundRequest request) throws BlockException;

    @PostMapping(value = "/asl/bill/queryMerOrd", consumes = MediaType.APPLICATION_JSON_VALUE)
    String queryStatus(@RequestBody MPayQueryStatusRequest request) throws BlockException;

}
