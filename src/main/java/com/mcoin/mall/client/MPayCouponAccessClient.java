package com.mcoin.mall.client;

import com.alibaba.csp.sentinel.slots.block.BlockException;
import com.mcoin.mall.client.model.MPayCouponUpdateCodeRequest;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(name = "mPayCouponAccessClient", url = "${macaupass.coupon.access.url}")
public interface MPayCouponAccessClient {

    @PostMapping(value = "/mcoinAccess/mcoin/coupon/update", consumes = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    String updateCode(@RequestBody MPayCouponUpdateCodeRequest data) throws BlockException;

}
