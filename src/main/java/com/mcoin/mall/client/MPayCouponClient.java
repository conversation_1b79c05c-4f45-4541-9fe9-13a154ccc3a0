package com.mcoin.mall.client;

import com.alibaba.csp.sentinel.slots.block.BlockException;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.mcoin.mall.client.model.MPayCouponCancelRequest;
import com.mcoin.mall.client.model.MPayCouponPayRequest;
import com.mcoin.mall.client.model.MPayCouponQueryRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

@FeignClient(name = "mPayCouponClient", url = "${macaupass.coupon.url}")
public interface MPayCouponClient {

    @PostMapping(value = "/coupon/app/receive/receiveCouponByMCoin", consumes = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    String pay(@RequestBody MPayCouponPayRequest data) throws BlockException;

    @PostMapping(value = "/coupon/app/couponInf/detailByOrderId", consumes = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    String query(@RequestBody MPayCouponQueryRequest data) throws BlockException;

    @PostMapping(value = "/coupon/app/couponInf/cancelCoupon", consumes = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    String cancel(@RequestBody MPayCouponCancelRequest data) throws BlockException;


    /**
     * 参考： <a href="https://aliyuque.antfin.com/mpass/wgrg7l/hpq8eqpyhbgpr6yr#dNkAI">...</a>
     */
    @Slf4j
    class MD5Utils {
        private static final String preMd5Str = "p22TPyba*G^6=q@=$6U%#DNdwznLN5SyN&L6";
        private static String saltValue = "u2@M4s*RVaSU8YR#";

        public static String toHex(byte hash[]) {
            StringBuilder buf = new StringBuilder(hash.length * 2);
            int i;
            for (i = 0; i < hash.length; i++) {
                if (((int) hash[i] & 0xff) < 0x10) {
                    buf.append("0");
                }
                buf.append(Long.toString((int) hash[i] & 0xff, 16));
            }
            return buf.toString();
        }

        private static String getMD5(String data, String saltValue) {
            if (data == null) {
                return null;
            }
            MessageDigest digest = null;
            try {
                digest = MessageDigest.getInstance("MD5");
                digest.update(data.getBytes());
            } catch (NoSuchAlgorithmException nsae) {
                throw new RuntimeException("MD5 not supported", nsae);
            }
            if (StringUtils.isNotEmpty(saltValue)) {
                return toHex(digest.digest(saltValue.getBytes()));
            }
            return toHex(digest.digest());
        }

        //默认salt
        public static String getMD5(Object object) {
            String source = JSONObject.toJSONString(object, SerializerFeature.MapSortField);
            String md5 = getMD5(preMd5Str + source, saltValue);
            log.info("MD5 source:{}, value:{}", source, md5);
            return md5;
        }
    }
}
