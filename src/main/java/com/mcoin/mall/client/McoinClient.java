package com.mcoin.mall.client;

import com.alibaba.csp.sentinel.slots.block.BlockException;
import com.mcoin.mall.client.model.McoinBalanceRequest;
import com.mcoin.mall.client.model.McoinTokenRequest;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * mCoin積分系統
 *
 * <AUTHOR>
 */
@FeignClient(name = "mCoinClient", url = "${mcoin.url}")
public interface McoinClient {

    @PostMapping(value = "/mcoin/service/balance", consumes = MediaType.APPLICATION_JSON_VALUE)
    String getBalance(@RequestBody McoinBalanceRequest request) throws BlockException;

    @PostMapping(value = "/mcoin/service/oauth", consumes = MediaType.APPLICATION_JSON_VALUE)
    String getToken(@RequestBody McoinTokenRequest request) throws BlockException;

}
