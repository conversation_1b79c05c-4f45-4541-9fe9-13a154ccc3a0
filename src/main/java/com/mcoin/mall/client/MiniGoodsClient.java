package com.mcoin.mall.client;

import com.mcoin.mall.client.model.MiniGoodsHttpRequest;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 查询零售小程序商品
 */
@FeignClient(name = "MiniGoodsClient", url = "${mini.program.URL}")
public interface MiniGoodsClient {

    @PostMapping(value = "/shopapi/goods/syncProductList", consumes = MediaType.APPLICATION_JSON_VALUE)
    String getMiniGoods(@RequestBody MiniGoodsHttpRequest request);
}
