package com.mcoin.mall.client;

import com.alibaba.csp.sentinel.slots.block.BlockException;
import com.mcoin.mall.client.model.MiniOrderIsAfterHttpRequest;
import com.mcoin.mall.client.model.MiniOrdersHttpRequest;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 查询零售小程序订单
 *
 * @author: laijinjie
 * @date: 2023/10/24
 */
@FeignClient(name = "MiniOrderClient", url = "${mini.program.URL}")
public interface MiniOrderClient {

    @PostMapping(value = "/shopapi/order/remoteMiniOrderList", consumes = MediaType.APPLICATION_JSON_VALUE)
    String getMiniOrders(@RequestBody MiniOrdersHttpRequest request) throws BlockException;

    @PostMapping(value = "/shopapi/order/getOrderIsAfter", consumes = MediaType.APPLICATION_JSON_VALUE)
    String getMiniOrderIsAfter(@RequestBody MiniOrderIsAfterHttpRequest request) throws BlockException;
}
