package com.mcoin.mall.client;

import com.alibaba.csp.sentinel.slots.block.BlockException;
import com.mcoin.mall.client.model.MiniOrdersHttpRequest;
import com.mcoin.mall.client.model.MiniUserAddressDetailHttpRequest;
import com.mcoin.mall.client.model.MiniUserAddressEditHttpRequest;
import com.mcoin.mall.client.model.MiniUserAddressLocationHttpRequest;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 用户地址
 *
 */
@FeignClient(name = "MiniUserAddressClient", url = "${mini.program.URL}")
public interface MiniUserAddressClient {

    @PostMapping(value = "/shopapi/mcoin_user_address/detail", consumes = MediaType.APPLICATION_JSON_VALUE)
    String detail(@RequestBody MiniUserAddressDetailHttpRequest request) throws BlockException;

    @PostMapping(value = "/shopapi/mcoin_user_address/add", consumes = MediaType.APPLICATION_JSON_VALUE)
    String add(@RequestBody MiniUserAddressEditHttpRequest request) throws BlockException;

    @PostMapping(value = "/shopapi/mcoin_user_address/lists", consumes = MediaType.APPLICATION_JSON_VALUE)
    String lists(@RequestBody MiniOrdersHttpRequest request) throws BlockException;

    @PostMapping(value = "/shopapi/mcoin_user_address/del", consumes = MediaType.APPLICATION_JSON_VALUE)
    String del(@RequestBody MiniUserAddressDetailHttpRequest request) throws BlockException;
    @PostMapping(value = "/shopapi/mcoin_user_address/edit", consumes = MediaType.APPLICATION_JSON_VALUE)
    String edit(@RequestBody MiniUserAddressEditHttpRequest request) throws BlockException;
    @PostMapping(value = "/shopapi/mcoin_user_address/setDefault", consumes = MediaType.APPLICATION_JSON_VALUE)
    String setDefault(@RequestBody MiniUserAddressDetailHttpRequest request) throws BlockException;
    @PostMapping(value = "/shopapi/mcoin_user_address/mcoinGeocoderCoordinate", consumes = MediaType.APPLICATION_JSON_VALUE)
    String geocoderCoordinate(@RequestBody MiniUserAddressLocationHttpRequest request) throws BlockException;
}
