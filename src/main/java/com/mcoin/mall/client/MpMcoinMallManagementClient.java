package com.mcoin.mall.client;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import com.alibaba.csp.sentinel.slots.block.BlockException;
import com.mcoin.mall.client.model.MiniGoodsHttpRequest;
import com.mcoin.mall.client.model.MiniGoodsUpdateHttpRequest;
import com.mcoin.mall.client.model.MiniOrdersHttpRequest;
import com.mcoin.mall.client.model.MiniUserAddressDetailHttpRequest;
import com.mcoin.mall.client.model.MiniUserAddressEditHttpRequest;
import com.mcoin.mall.client.model.mp.management.ActivityTimeToGoodsBuyTimeRequest;
import com.mcoin.mall.client.model.mp.management.DeleteActivityInfoRequest;
import com.mcoin.mall.client.model.mp.management.MpMcoinMallResponse;
import com.mcoin.mall.client.model.mp.management.OrderCntRequest;
import com.mcoin.mall.client.model.mp.management.OrderCntResponse;
import com.mcoin.mall.client.model.mp.management.SyncActivityInfoRequest;

@FeignClient(name = "MpMcoinMallManagementClient", url = "${mp.mcoin.mall.management.url}")
public interface MpMcoinMallManagementClient {

    @PostMapping("/api/management/snapUp/syncActivityInfo")
    MpMcoinMallResponse<Object> syncActivityInfo(SyncActivityInfoRequest request);

    @PostMapping("/api/management/snapUp/activityTimeToGoodsBuyTime")
    MpMcoinMallResponse<Object> activityTimeToGoodsBuyTime(ActivityTimeToGoodsBuyTimeRequest request);

    @PostMapping("/api/management/snapUp/deleteActivityInfo")
    MpMcoinMallResponse<Object> deleteActivityInfo(DeleteActivityInfoRequest request);

    @PostMapping(value = "/api/management/user_address/detail", consumes = MediaType.APPLICATION_JSON_VALUE)
    String detail(@RequestBody MiniUserAddressDetailHttpRequest request) throws BlockException;

    @PostMapping(value = "/api/management/user_address/add", consumes = MediaType.APPLICATION_JSON_VALUE)
    String add(@RequestBody MiniUserAddressEditHttpRequest request) throws BlockException;

    @PostMapping(value = "/api/management/user_address/lists", consumes = MediaType.APPLICATION_JSON_VALUE)
    String lists(@RequestBody MiniOrdersHttpRequest request) throws BlockException;

    @PostMapping(value = "/api/management/user_address/del", consumes = MediaType.APPLICATION_JSON_VALUE)
    String del(@RequestBody MiniUserAddressDetailHttpRequest request) throws BlockException;

    @PostMapping(value = "/api/management/user_address/edit", consumes = MediaType.APPLICATION_JSON_VALUE)
    String edit(@RequestBody MiniUserAddressEditHttpRequest request) throws BlockException;

    @PostMapping(value = "/api/management/user_address/setDefault", consumes = MediaType.APPLICATION_JSON_VALUE)
    String setDefault(@RequestBody MiniUserAddressDetailHttpRequest request) throws BlockException;


    @PostMapping(value = "/api/management/order/remoteMiniOrderList", consumes = MediaType.APPLICATION_JSON_VALUE)
    String getMiniOrders(@RequestBody MiniOrdersHttpRequest request) throws BlockException;


    @PostMapping(value = "/api/management/goods/update/status", consumes = MediaType.APPLICATION_JSON_VALUE)
    MpMcoinMallResponse<Integer> updateProductStatus(@RequestBody MiniGoodsUpdateHttpRequest request);

    @PostMapping(value = "/api/management/goods/sync/list", consumes = MediaType.APPLICATION_JSON_VALUE)
    String getMiniGoods(@RequestBody MiniGoodsHttpRequest request);

    @PostMapping(value = "/api/management/orderCnt", consumes = MediaType.APPLICATION_JSON_VALUE)
    MpMcoinMallResponse<OrderCntResponse> getOrderCnt(@RequestBody OrderCntRequest request);


}
