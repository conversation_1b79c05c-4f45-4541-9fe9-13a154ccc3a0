package com.mcoin.mall.client;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import com.alibaba.csp.sentinel.slots.block.BlockException;
import com.mcoin.mall.model.GrayRequest;
import com.mcoin.mall.model.GrayResponse;
import com.mcoin.mall.model.Response;

/**
 * 积分产品客户端
 */
@FeignClient(name = "pointProdClient", url = "${point.prod.url}")
public interface PointProdClient {

    /**
     * 灰度检查
     *
     * @param request 灰度请求
     * @return 灰度响应
     * @throws BlockException 阻塞异常
     */
    @PostMapping(value = "/api/management/gray", consumes = MediaType.APPLICATION_JSON_VALUE)
    Response<GrayResponse> gray(@RequestBody GrayRequest request) throws BlockException;

} 