package com.mcoin.mall.client;

import com.alibaba.csp.sentinel.slots.block.BlockException;
import com.mcoin.mall.client.model.SignReqDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.retry.annotation.Retryable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(name = "taskUploadClient", url = "${task.upload.url}")
public interface TaskUploadClient {

    @Retryable(value = Exception.class)
    @PostMapping(value = "/bservice/discount/gateway/transactionalTaskNotify", consumes = MediaType.APPLICATION_JSON_VALUE)
    String transactionalTaskNotify(@RequestBody SignReqDto data) throws BlockException;
    @Retryable(value = Exception.class)
    @PostMapping(value = "/bservice/discount/gateway/refundTaskNotify", consumes = MediaType.APPLICATION_JSON_VALUE)
    String refundTaskNotify(@RequestBody SignReqDto data) throws BlockException;

}
