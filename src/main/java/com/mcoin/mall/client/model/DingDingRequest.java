package com.mcoin.mall.client.model;

import com.alibaba.fastjson.JSONObject;
import com.mcoin.mall.util.JodaTimeUtil;

import java.util.Date;

public class DingDingRequest {
    private String msgtype = "text";
    private String title;
    private String content;
    private Date date = new Date();

    public DingDingRequest setTitle(String title) {
        this.title = title;
        return this;
    }

    public DingDingRequest setContent(String content) {
        this.content = content;
        return this;
    }

    public String getMsgtype() {
        return msgtype;
    }
    public String getText() {
        JSONObject jb = new JSONObject();
        jb.put("content", "來源：mCoinMall\n時間：" + JodaTimeUtil.format(date) + "\n標題：" + title + "\n内容：" + content);
        return jb.toJSONString();
    }
}
