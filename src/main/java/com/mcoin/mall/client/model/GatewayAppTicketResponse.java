package com.mcoin.mall.client.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class GatewayAppTicketResponse implements Serializable {
    private static final long serialVersionUID = 1L;

    @JsonProperty("ticket")
    private String ticket;

    @JsonProperty("expires_in")
    private Integer expiresIn;

    @JsonProperty("errcode")
    private String errcode = "00";

    @JsonProperty("errmsg")
    private String errmsg = "";

}