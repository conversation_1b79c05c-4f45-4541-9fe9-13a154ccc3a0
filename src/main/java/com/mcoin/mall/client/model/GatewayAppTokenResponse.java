package com.mcoin.mall.client.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class GatewayAppTokenResponse implements Serializable {
    private static final long serialVersionUID = 1L;

    @JsonProperty("app_token")
    private String appToken;

    @JsonProperty("expires_in")
    private Integer expiresIn;

    @JsonProperty("errcode")
    private String errcode = "00";

    @JsonProperty("errmsg")
    private String errmsg = "";

}