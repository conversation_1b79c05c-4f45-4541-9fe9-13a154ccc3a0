package com.mcoin.mall.client.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import javax.annotation.Generated;
import java.io.Serializable;

@Data
@Generated("com.robohorse.robopojogenerator")
public class MPayAppTokenResponse implements Serializable {

	private static final long serialVersionUID = 1L;

	@JsonProperty("app_token")
	private String appToken;

	@JsonProperty("expires_in")
	private Integer expiresIn;
}