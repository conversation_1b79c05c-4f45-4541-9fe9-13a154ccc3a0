package com.mcoin.mall.client.model;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class MPayCouponUpdateCodeRequest {
    private Integer storeId;
    private String storeName;
    private String storeNameEn;
    private String storeIconUrl;
    private String couponRuleId;
    private String couponName;
    private String couponNameEn;
    private String couponType;
    private String iconUrl;
    private String couponDetailUrl;
    private String userId;
    private String mobile;
    private String couponTNC;
    private String couponCode;
    private String couponStatus;
    private String couponStatusTime;
    private String mcoinOrderId;
    private Integer integral;
    private BigDecimal amount;
    private String vaildStartTime;
    private String vaildEndTime;
    private Integer couponId;
    private String signature;
}
