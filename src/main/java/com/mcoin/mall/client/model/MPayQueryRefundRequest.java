package com.mcoin.mall.client.model;

import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class MPayQueryRefundRequest extends MPayRequest<String> {

    public MPayQueryRefundRequest(String app_id) {
        super(app_id, "queryRefund", "100020004");
    }

    @Data
    public static class Content {
        private String out_trade_no;
        private String trade_no;
        private String out_request_no;
    }
}
