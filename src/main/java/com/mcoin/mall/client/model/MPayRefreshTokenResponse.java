package com.mcoin.mall.client.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import javax.annotation.Generated;
import java.io.Serializable;

@Data
@Generated("com.robohorse.robopojogenerator")
public class MPayRefreshTokenResponse implements Serializable {

	private static final long serialVersionUID = 1L;

	@JsonProperty("access_token")
	private String accessToken;

	@JsonProperty("refresh_token")
	private String refreshToken;
	
}