package com.mcoin.mall.client.model;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

@EqualsAndHashCode(callSuper = true)
@Data
public class MPayRefundRequest extends MPayRequest<String>{

    public MPayRefundRequest(String app_id) {
        super(app_id, "refund", "200020006");
    }

    @Data
    public static class Content{
        private String out_trade_no;
        private String trade_no;
        private BigDecimal refund_amount;
        private BigDecimal refund_point_amount;
        private String refund_reason;
        private String out_request_no;
        private Long refund_point;
    }
}
