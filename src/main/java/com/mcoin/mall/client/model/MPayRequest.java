package com.mcoin.mall.client.model;

import com.mcoin.mall.util.JodaTimeUtil;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class MPayRequest <T> {
    private String app_id;
    private String api_name;
    private String data_type = "JSON";
    private String charset = "utf-8";
    private String sign_type = "RSA";
    private String timestamp = JodaTimeUtil.getStandardTime();
    private String version = "1.0";
    private String biz_api_code;
    private String sign;
    private T biz_content;

    public MPayRequest(String app_id, String api_name, String biz_api_code) {
        this.app_id = app_id;
        this.api_name = api_name;
        this.biz_api_code = biz_api_code;
    }
}
