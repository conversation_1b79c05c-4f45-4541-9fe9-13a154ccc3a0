package com.mcoin.mall.client.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import javax.annotation.Generated;
import java.io.Serializable;

@Data
@Generated("com.robohorse.robopojogenerator")
public class MPayTicketResponse implements Serializable {

	private static final long serialVersionUID = 1L;
	
	@JsonProperty("errcode")
	private String errcode;
	
	@JsonProperty("errmsg")
	private String errmsg;

	@JsonProperty("ticket")
	private String ticket;

	@JsonProperty("expires_in")
	private Integer expiresIn;
}