package com.mcoin.mall.client.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import javax.annotation.Generated;
import java.io.Serializable;

@Data
@Generated("com.robohorse.robopojogenerator")
public class MPayUserInfoResponse implements Serializable {

	private static final long serialVersionUID = 1L;

	@JsonProperty("openid")
	private String openid;

	@JsonProperty("phone")
	private String phone;
	
	@JsonProperty("area")
	private String area;
	
	@JsonProperty("point")
	private Integer point;
	
	@JsonProperty("userId")
	private String userId;

	private String nickname;

	private String headimgurl;
}