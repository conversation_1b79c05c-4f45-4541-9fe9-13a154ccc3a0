package com.mcoin.mall.client.model;

import com.mcoin.mall.client.model.common.MiniProgramCommonRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;


/***
 *
 */
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Data
public class MiniGoodsHttpRequest extends MiniProgramCommonRequest {

    private Long startTime;

    private Long endTime;

}