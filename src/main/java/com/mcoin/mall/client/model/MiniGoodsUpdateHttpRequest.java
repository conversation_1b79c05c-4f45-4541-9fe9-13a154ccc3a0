package com.mcoin.mall.client.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/25
 */
@Getter
@Setter
@Accessors(chain = true)
@ApiModel(description = "商品更新-请求体")
public class MiniGoodsUpdateHttpRequest implements Serializable {
    @ApiModelProperty(value = "uuids", required = true)
    private List<String> uuids;
    @ApiModelProperty(value = "小程序商品状态", required = true)
    private Integer status;
}
