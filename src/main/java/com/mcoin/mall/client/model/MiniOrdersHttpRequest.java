package com.mcoin.mall.client.model;

import com.mcoin.mall.client.model.common.MiniProgramCommonRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;


/***
 * @author: laijinjie
 * @date 2023-10-20
 */
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Data
public class MiniOrdersHttpRequest extends MiniProgramCommonRequest {
    /**
     * 不填默认全部,(0:未付款 1:待發貨 2：待收貨 3：已完成 4：已關閉)
     */
    private Integer orderStatus;
    /**
     * 当前页码
     */
    private Integer page;
    /**
     * 分页数量
     */
    private Integer count;
}