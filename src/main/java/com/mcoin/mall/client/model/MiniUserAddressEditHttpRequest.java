package com.mcoin.mall.client.model;

import com.mcoin.mall.client.model.common.MiniProgramCommonRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;


/***
 *
 */
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Data
public class MiniUserAddressEditHttpRequest extends MiniProgramCommonRequest {

    private String id;

    private String contact;

    private String mobile;

    private Integer province_id;

    private Integer city_id;

    private Integer district_id;

    private String address;

    private String latitude;

    private String longitude;

    private Integer is_default;

    private String province;

    private String city;

    private String district;

}