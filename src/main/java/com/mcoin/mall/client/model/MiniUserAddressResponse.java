package com.mcoin.mall.client.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import javax.annotation.Generated;
import java.io.Serializable;

@ApiModel(description = "用户地址详情-响应体")
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Generated("com.robohorse.robopojogenerator")
public class MiniUserAddressResponse implements Serializable {

	private String id;

	private String contact;

	private String mobile;

	@JsonProperty("province_id")
	private Integer provinceId;

	@JsonProperty("city_id")
	private Integer cityId;

	@JsonProperty("district_id")
	private Integer districtId;

	private String address;

	private String latitude;

	private String longitude;

	private Integer isDefault;

	private String province;

	private String city;

	private String district;

}