package com.mcoin.mall.client.model;

import lombok.Data;

import java.io.Serializable;

@Data
public class PayLogRequest implements Serializable {
    private Param param;
    private ReqData data;

    @Data
    public static class Param {
        private String system_name;
        private String tran_time;
        private String tran_value;
        private String original_ordamt;
        private String cust_id;
        private String user_name;
        private String costrec_no;
        private String mer_no;
        private String mer_face_no;
        private String point_no;
        private String data_source;
    }

    @Data
    public static class ReqData {
        private String telephone_no;
        private String telephone_code;
        private String cust_id;
        private String birth_day;
        private Integer sex;
        private String register_date;
        private Integer user_level;
        private String tran_time;
        private String tran_value;
        private String tran_currency;
        private Integer order_type;
        private Integer tran_channel;
        private String pay_channel;
        private String user_tran_day_times;
        private String user_tran_day_value;
        private String mcc;
        private String mer_no;
        private String mer_face_no;
        private String activity_channel;
        private String activity_biz_type;
        private String pay_type;
        private String product_id;
        private String original_ordamt;
        private String data_source;
    }
}