package com.mcoin.mall.client.model;

import lombok.Data;

import java.io.Serializable;

/**
 * 退款成功日志上报
 */
@Data
public class RefundSuccessLogRequest implements Serializable {
    private Param param;

    @Data
    public static class Param {
        private String system_name;
        private String costrec_no;
        private String tran_time;
        private String tran_value;
        private String original_ordamt;
        private String left_tran_value;
        private String left_original_ordamt;
        private String refund_value;
        private String refund_original_ordamt;
        private String cust_id;
        private String user_name;
        private String refund_costrec_no;
        private String refund_time;
        private String is_all_refund;
        private String mer_no;
        private String mer_face_no;
        private String point_no;
        private String data_source;
    }

}