package com.mcoin.mall.client.model;

import lombok.Data;
import javax.validation.constraints.NotBlank;

import java.io.Serializable;
import java.util.Map;

@Data
public class SignReqDto implements Serializable {

    private static final long serialVersionUID = 1L;

    @NotBlank
    private String appId;

    @NotBlank
    private String token;

    @NotBlank
    private String timestamp;

    @NotBlank
    private Map<?, ?> body;

    @NotBlank
    private String nonceStr;

    @NotBlank
    private String sign;
    public SignReqDto(){}
    public SignReqDto(String appId,String token, Map<?, ?> body, String nonceStr, String timestamp){
        this.appId = appId;
        this.token = token;
        this.body = body;
        this.nonceStr = nonceStr;
        this.timestamp = timestamp;
    }
}
