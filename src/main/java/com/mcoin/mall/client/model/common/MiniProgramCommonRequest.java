package com.mcoin.mall.client.model.common;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 *
 * @author: laiji<PERSON><PERSON>
 * @date: 2023/10/24
 */
@Data
@Accessors(chain = true)
public class MiniProgramCommonRequest implements Serializable {

    /**
     * 用户的custId
     */
    private String custId;

    /**
     * 密文（作用于验签）
     */
    private String signature;
}