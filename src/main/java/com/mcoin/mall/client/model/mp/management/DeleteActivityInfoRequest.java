package com.mcoin.mall.client.model.mp.management;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

@ApiModel(description = "删除场次信息-请求体")
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Accessors(chain = true)
public class DeleteActivityInfoRequest {
    @NotNull
    @Min(1)
    @ApiModelProperty(value = "场次ID", required = true)
    private Integer sessionId;
}
