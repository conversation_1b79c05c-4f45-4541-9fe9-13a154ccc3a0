package com.mcoin.mall.client.model.mp.management;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@ApiModel(value = "通用响应头")
@JsonIgnoreProperties(ignoreUnknown = true)
public class MpMcoinMallResponse<T>{
    @ApiModelProperty(value = "返回码")
    @JsonProperty("code")
    private int code;
    @ApiModelProperty(value = "返回信息")
    @JsonProperty("msg")
    private String msg;
    @ApiModelProperty(value = "展示标识")
    @JsonProperty("show")
    private int show = 0;
    @ApiModelProperty(value = "数据体")
    @JsonProperty("data")
    private T data;


    public enum Code {
        UNAUTHORIZED(-1, "请先登录"),
        OK(1, "操作成功"),
        FAIL(0, "失败"),
        PARAM_ILLEGAL(1001, "参数异常"),
        STOP_SERVICE(1020, "停止服务");

        private final int code;
        private final String msg;
        Code(int code, String msg) {
            this.code = code;
            this.msg = msg;
        }
        public int getCode(){
            return this.code;
        }
        public String getMsg(){
            return this.msg;
        }

    }
}
