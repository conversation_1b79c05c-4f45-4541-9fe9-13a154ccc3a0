package com.mcoin.mall.client.model.mp.management;


import java.util.Date;
import java.util.List;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

@ApiModel(description = "同步场次信息-请求体")
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Accessors(chain = true)
public class SyncActivityInfoRequest {
    @NotNull
    @Min(1)
    @ApiModelProperty(value = "场次ID", required = true)
    private Integer sessionId;
    @NotNull
    @ApiModelProperty(value = "秒杀活动名称", required = true)
    private String name;
    @NotNull
    @ApiModelProperty(value = "活动开始时间，格式：yyyy-MM-dd HH:mm:ss", required = true)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;
    @NotNull
    @ApiModelProperty(value = "活动结束时间，格式：yyyy-MM-dd HH:mm:ss", required = true)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;
    @NotNull
    @ApiModelProperty(value = "活动状态[1=未开始，2=进行中, 3=已结束]", required = true)
    private Integer status;
    @NotNull
    @Size(min = 1)
    @ApiModelProperty(value = "关联商品UUID集合", required = true)
    private List<String> uuids;

    @NotNull
    @ApiModelProperty(value = "动作：EDIT_SESSION\n" +
            "EDIT_SESSION_PRODUCT\n" +
            "CLOSE_SESSION\n" +
            "OPEN_SESSION\n" +
            "DELETE_SESSION", required = true)
    private String action;

}
