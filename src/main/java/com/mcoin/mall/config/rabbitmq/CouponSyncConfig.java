package com.mcoin.mall.config.rabbitmq;

import org.springframework.amqp.core.*;
import org.springframework.amqp.rabbit.config.DirectRabbitListenerContainerFactory;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;

@Configuration
public class CouponSyncConfig {

    @Autowired
    @Qualifier("taskExecutor")
    private ThreadPoolTaskExecutor taskExecutor;
    @Autowired
    private ThreadPoolTaskScheduler taskScheduler;

    /***couponSync 配置***/
    @Value("${coupon_sync_queue_name}")
    private String couponSyncQueueName;
    @Value("${coupon_sync_exchange_name}")
    private String couponSyncExchangeName;
    @Value("${coupon_sync_routingkey_name}")
    private String couponSyncRoutingkeyName;
    @Value("${coupon_sync_delay_exchange_name}")
    private String couponSyncDelayExchangeName;
    @Value("${coupon_sync_delay_routingkey_name}")
    private String couponSyncDelayRoutingkeyName;
    @Value("${coupon_sync_max_concurrent_consumers}")
    private Integer couponSyncMaxConcurrentConsumers;


    @Bean
    public Queue couponSyncQueue() {
        return new Queue(couponSyncQueueName);
    }

    @Bean
    public Exchange couponSyncExchange() {
        return new DirectExchange(couponSyncExchangeName);
    }

    @Bean
    public Binding couponSyncBinding() {
        return BindingBuilder.bind(couponSyncQueue()).to(couponSyncExchange())
                .with(couponSyncRoutingkeyName).noargs();
    }

    @Bean
    public RabbitTemplate couponSyncTemplate(ConnectionFactory connectionFactory) {
        RabbitTemplate template = new RabbitTemplate(connectionFactory);
        template.setExchange(couponSyncExchangeName);
        template.setRoutingKey(couponSyncRoutingkeyName);
        return template;
    }
    @Bean
    public Exchange couponSyncDelayExchange() {
        DirectExchange exchange = new DirectExchange(couponSyncDelayExchangeName);
        exchange.setDelayed(true);
        return exchange;
    }

    @Bean
    public Binding couponSyncDelayBinding() {
        return BindingBuilder.bind(couponSyncQueue()).to(couponSyncDelayExchange())
                .with(couponSyncDelayRoutingkeyName).noargs();
    }

    @Bean
    public RabbitTemplate couponSyncTemplateDelay(ConnectionFactory connectionFactory) {
        RabbitTemplate template = new RabbitTemplate(connectionFactory);
        template.setExchange(couponSyncDelayExchangeName);
        template.setRoutingKey(couponSyncDelayRoutingkeyName);
        return template;
    }

    @Bean
    public DirectRabbitListenerContainerFactory couponSyncListenerContainer(ConnectionFactory connectionFactory) {
        DirectRabbitListenerContainerFactory factory = new DirectRabbitListenerContainerFactory();
        factory.setConnectionFactory(connectionFactory);
        factory.setConsumersPerQueue(couponSyncMaxConcurrentConsumers);
        factory.setAutoStartup(true);
        factory.setTaskExecutor(taskExecutor);
        factory.setTaskScheduler(taskScheduler);
        return factory;
    }
}
