package com.mcoin.mall.config.rabbitmq;

import org.springframework.amqp.core.*;
import org.springframework.amqp.rabbit.config.DirectRabbitListenerContainerFactory;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;

@Configuration
public class MiniOrderSyncConfig {

    @Autowired
    @Qualifier("taskExecutor")
    private ThreadPoolTaskExecutor taskExecutor;
    @Autowired
    private ThreadPoolTaskScheduler taskScheduler;

    /***settlement 配置***/
    @Value("${mini_order_sync_queue_name}")
    private String miniOrderSyncQueueName;
    @Value("${mini_order_sync_exchange_name}")
    private String miniOrderSyncExchangeName;
    @Value("${mini_order_sync_routingkey_name}")
    private String miniOrderSyncRoutingkeyName;
    @Value("${mini_order_sync_delay_exchange_name}")
    private String miniOrderSyncDelayExchangeName;
    @Value("${mini_order_sync_delay_routingkey_name}")
    private String miniOrderSyncDelayRoutingkeyName;
    @Value("${mini_order_sync_max_concurrent_consumers}")
    private Integer miniOrderSyncMaxConcurrentConsumers;


    @Bean
    public Queue miniOrderSyncQueue() {
        return new Queue(miniOrderSyncQueueName);
    }

    @Bean
    public Exchange miniOrderSyncExchange() {
        return new DirectExchange(miniOrderSyncExchangeName);
    }

    @Bean
    public Binding miniOrderSyncBinding() {
        return BindingBuilder.bind(miniOrderSyncQueue()).to(miniOrderSyncExchange())
                .with(miniOrderSyncRoutingkeyName).noargs();
    }

    @Bean
    public RabbitTemplate miniOrderSyncTemplate(ConnectionFactory connectionFactory) {
        RabbitTemplate template = new RabbitTemplate(connectionFactory);
        template.setExchange(miniOrderSyncExchangeName);
        template.setRoutingKey(miniOrderSyncRoutingkeyName);
        return template;
    }
    @Bean
    public Exchange miniOrderSyncDelayExchange() {
        DirectExchange exchange = new DirectExchange(miniOrderSyncDelayExchangeName);
        exchange.setDelayed(true);
        return exchange;
    }

    @Bean
    public Binding miniOrderSyncDelayBinding() {
        return BindingBuilder.bind(miniOrderSyncQueue()).to(miniOrderSyncDelayExchange())
                .with(miniOrderSyncDelayRoutingkeyName).noargs();
    }

    @Bean
    public RabbitTemplate miniOrderSyncTemplateDelay(ConnectionFactory connectionFactory) {
        RabbitTemplate template = new RabbitTemplate(connectionFactory);
        template.setExchange(miniOrderSyncDelayExchangeName);
        template.setRoutingKey(miniOrderSyncDelayRoutingkeyName);
        return template;
    }

    @Bean
    public DirectRabbitListenerContainerFactory miniOrderSyncListenerContainer(ConnectionFactory connectionFactory) {
        DirectRabbitListenerContainerFactory factory = new DirectRabbitListenerContainerFactory();
        factory.setConnectionFactory(connectionFactory);
        factory.setConsumersPerQueue(miniOrderSyncMaxConcurrentConsumers);
        factory.setAutoStartup(true);
        factory.setTaskExecutor(taskExecutor);
        factory.setTaskScheduler(taskScheduler);
        return factory;
    }
}
