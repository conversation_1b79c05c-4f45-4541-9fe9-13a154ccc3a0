package com.mcoin.mall.config.rabbitmq;

import org.springframework.amqp.core.*;
import org.springframework.amqp.rabbit.config.DirectRabbitListenerContainerFactory;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;

@Configuration
public class OrderCloseConfig {

    @Autowired
    @Qualifier("taskExecutor")
    private ThreadPoolTaskExecutor taskExecutor;
    @Autowired
    private ThreadPoolTaskScheduler taskScheduler;

    /***orderClose 配置***/
    @Value("${order_close_queue_name}")
    private String orderCloseQueueName;
    @Value("${order_close_exchange_name}")
    private String orderCloseExchangeName;
    @Value("${order_close_routingkey_name}")
    private String orderCloseRoutingkeyName;
    @Value("${order_close_delay_exchange_name}")
    private String orderCloseDelayExchangeName;
    @Value("${order_close_delay_routingkey_name}")
    private String orderCloseDelayRoutingkeyName;
    @Value("${order_close_max_concurrent_consumers}")
    private Integer orderCloseMaxConcurrentConsumers;


    @Bean
    public Queue orderCloseQueue() {
        return new Queue(orderCloseQueueName);
    }

    @Bean
    public Exchange orderCloseExchange() {
        return new DirectExchange(orderCloseExchangeName);
    }

    @Bean
    public Binding orderCloseBinding() {
        return BindingBuilder.bind(orderCloseQueue()).to(orderCloseExchange())
                .with(orderCloseRoutingkeyName).noargs();
    }

    @Bean
    public RabbitTemplate orderCloseTemplate(ConnectionFactory connectionFactory) {
        RabbitTemplate template = new RabbitTemplate(connectionFactory);
        template.setExchange(orderCloseExchangeName);
        template.setRoutingKey(orderCloseRoutingkeyName);
        return template;
    }
    @Bean
    public Exchange orderCloseDelayExchange() {
        DirectExchange exchange = new DirectExchange(orderCloseDelayExchangeName);
        exchange.setDelayed(true);
        return exchange;
    }

    @Bean
    public Binding orderCloseDelayBinding() {
        return BindingBuilder.bind(orderCloseQueue()).to(orderCloseDelayExchange())
                .with(orderCloseDelayRoutingkeyName).noargs();
    }

    @Bean
    public RabbitTemplate orderCloseTemplateDelay(ConnectionFactory connectionFactory) {
        RabbitTemplate template = new RabbitTemplate(connectionFactory);
        template.setExchange(orderCloseDelayExchangeName);
        template.setRoutingKey(orderCloseDelayRoutingkeyName);
        return template;
    }

    @Bean
    public DirectRabbitListenerContainerFactory orderCloseListenerContainer(ConnectionFactory connectionFactory) {
        DirectRabbitListenerContainerFactory factory = new DirectRabbitListenerContainerFactory();
        factory.setConnectionFactory(connectionFactory);
        factory.setConsumersPerQueue(orderCloseMaxConcurrentConsumers);
        factory.setAutoStartup(true);
        factory.setTaskExecutor(taskExecutor);
        factory.setTaskScheduler(taskScheduler);
        return factory;
    }
}
