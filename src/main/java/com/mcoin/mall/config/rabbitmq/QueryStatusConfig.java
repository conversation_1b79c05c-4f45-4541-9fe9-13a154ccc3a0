package com.mcoin.mall.config.rabbitmq;

import org.springframework.amqp.core.*;
import org.springframework.amqp.rabbit.config.DirectRabbitListenerContainerFactory;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;

@Configuration
public class QueryStatusConfig {

    @Autowired
    @Qualifier("taskExecutor")
    private ThreadPoolTaskExecutor taskExecutor;
    @Autowired
    private ThreadPoolTaskScheduler taskScheduler;

    /***queryStatus 配置***/
    @Value("${query_status_queue_name}")
    private String queryStatusQueueName;
    @Value("${query_status_exchange_name}")
    private String queryStatusExchangeName;
    @Value("${query_status_routingkey_name}")
    private String queryStatusRoutingkeyName;
    @Value("${query_status_delay_exchange_name}")
    private String queryStatusDelayExchangeName;
    @Value("${query_status_delay_routingkey_name}")
    private String queryStatusDelayRoutingkeyName;
    @Value("${query_status_max_concurrent_consumers}")
    private Integer queryStatusMaxConcurrentConsumers;


    @Bean
    public Queue queryStatusQueue() {
        return new Queue(queryStatusQueueName);
    }

    @Bean
    public Exchange queryStatusExchange() {
        return new DirectExchange(queryStatusExchangeName);
    }

    @Bean
    public Binding queryStatusBinding() {
        return BindingBuilder.bind(queryStatusQueue()).to(queryStatusExchange())
                .with(queryStatusRoutingkeyName).noargs();
    }

    @Bean
    public RabbitTemplate queryStatusTemplate(ConnectionFactory connectionFactory) {
        RabbitTemplate template = new RabbitTemplate(connectionFactory);
        template.setExchange(queryStatusExchangeName);
        template.setRoutingKey(queryStatusRoutingkeyName);
        return template;
    }
    @Bean
    public Exchange queryStatusDelayExchange() {
        DirectExchange exchange = new DirectExchange(queryStatusDelayExchangeName);
        exchange.setDelayed(true);
        return exchange;
    }

    @Bean
    public Binding queryStatusDelayBinding() {
        return BindingBuilder.bind(queryStatusQueue()).to(queryStatusDelayExchange())
                .with(queryStatusDelayRoutingkeyName).noargs();
    }

    @Bean
    public RabbitTemplate queryStatusTemplateDelay(ConnectionFactory connectionFactory) {
        RabbitTemplate template = new RabbitTemplate(connectionFactory);
        template.setExchange(queryStatusDelayExchangeName);
        template.setRoutingKey(queryStatusDelayRoutingkeyName);
        return template;
    }

    @Bean
    public DirectRabbitListenerContainerFactory queryStatusListenerContainer(ConnectionFactory connectionFactory) {
        DirectRabbitListenerContainerFactory factory = new DirectRabbitListenerContainerFactory();
        factory.setConnectionFactory(connectionFactory);
        factory.setConsumersPerQueue(queryStatusMaxConcurrentConsumers);
        factory.setAutoStartup(true);
        factory.setTaskExecutor(taskExecutor);
        factory.setTaskScheduler(taskScheduler);
        return factory;
    }
}
