package com.mcoin.mall.config.rabbitmq;

import org.springframework.amqp.core.*;
import org.springframework.amqp.rabbit.config.DirectRabbitListenerContainerFactory;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;

@Configuration
public class SettlementConfig {

    @Autowired
    @Qualifier("taskExecutor")
    private ThreadPoolTaskExecutor taskExecutor;
    @Autowired
    private ThreadPoolTaskScheduler taskScheduler;

    /***settlement 配置***/
    @Value("${settlement_queue_name}")
    private String settlementQueueName;
    @Value("${settlement_exchange_name}")
    private String settlementExchangeName;
    @Value("${settlement_routingkey_name}")
    private String settlementRoutingkeyName;
    @Value("${settlement_delay_exchange_name}")
    private String settlementDelayExchangeName;
    @Value("${settlement_delay_routingkey_name}")
    private String settlementDelayRoutingkeyName;
    @Value("${settlement_max_concurrent_consumers}")
    private Integer settlementMaxConcurrentConsumers;


    @Bean
    public Queue settlementQueue() {
        return new Queue(settlementQueueName);
    }

    @Bean
    public Exchange settlementExchange() {
        return new DirectExchange(settlementExchangeName);
    }

    @Bean
    public Binding settlementBinding() {
        return BindingBuilder.bind(settlementQueue()).to(settlementExchange())
                .with(settlementRoutingkeyName).noargs();
    }

    @Bean
    public RabbitTemplate settlementTemplate(ConnectionFactory connectionFactory) {
        RabbitTemplate template = new RabbitTemplate(connectionFactory);
        template.setExchange(settlementExchangeName);
        template.setRoutingKey(settlementRoutingkeyName);
        return template;
    }
    @Bean
    public Exchange settlementDelayExchange() {
        DirectExchange exchange = new DirectExchange(settlementDelayExchangeName);
        exchange.setDelayed(true);
        return exchange;
    }

    @Bean
    public Binding settlementDelayBinding() {
        return BindingBuilder.bind(settlementQueue()).to(settlementDelayExchange())
                .with(settlementDelayRoutingkeyName).noargs();
    }

    @Bean
    public RabbitTemplate settlementTemplateDelay(ConnectionFactory connectionFactory) {
        RabbitTemplate template = new RabbitTemplate(connectionFactory);
        template.setExchange(settlementDelayExchangeName);
        template.setRoutingKey(settlementDelayRoutingkeyName);
        return template;
    }

    @Bean
    public DirectRabbitListenerContainerFactory settlementListenerContainer(ConnectionFactory connectionFactory) {
        DirectRabbitListenerContainerFactory factory = new DirectRabbitListenerContainerFactory();
        factory.setConnectionFactory(connectionFactory);
        factory.setConsumersPerQueue(settlementMaxConcurrentConsumers);
        factory.setAutoStartup(true);
        factory.setTaskExecutor(taskExecutor);
        factory.setTaskScheduler(taskScheduler);
        return factory;
    }
}
