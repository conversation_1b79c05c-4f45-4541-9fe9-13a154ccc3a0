package com.mcoin.mall.config.rabbitmq;

import org.springframework.amqp.core.*;
import org.springframework.amqp.rabbit.config.DirectRabbitListenerContainerFactory;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;

import javax.annotation.Resource;

@Configuration
public class TaskUploadConfig {

    @Resource
    private ThreadPoolTaskExecutor taskExecutor;
    @Resource
    private ThreadPoolTaskScheduler taskScheduler;

    /***taskUpload 配置***/
    @Value("${task_upload_queue_name}")
    private String taskUploadQueueName;
    @Value("${task_upload_exchange_name}")
    private String taskUploadExchangeName;
    @Value("${task_upload_routingkey_name}")
    private String taskUploadRoutingkeyName;
    @Value("${task_upload_delay_exchange_name}")
    private String taskUploadDelayExchangeName;
    @Value("${task_upload_delay_routingkey_name}")
    private String taskUploadDelayRoutingkeyName;
    @Value("${task_upload_max_concurrent_consumers}")
    private Integer taskUploadMaxConcurrentConsumers;



    @Bean
    public Queue taskUploadQueue() {
        return new Queue(taskUploadQueueName);
    }

    @Bean
    public Exchange taskUploadExchange() {
        return new DirectExchange(taskUploadExchangeName);
    }

    @Bean
    public Binding taskUploadBinding() {
        return BindingBuilder.bind(taskUploadQueue()).to(taskUploadExchange())
                .with(taskUploadRoutingkeyName).noargs();
    }

    @Bean
    public RabbitTemplate taskUploadTemplate(ConnectionFactory connectionFactory) {
        RabbitTemplate template = new RabbitTemplate(connectionFactory);
        template.setExchange(taskUploadExchangeName);
        template.setRoutingKey(taskUploadRoutingkeyName);
        return template;
    }
    @Bean
    public Exchange taskUploadDelayExchange() {
        DirectExchange exchange = new DirectExchange(taskUploadDelayExchangeName);
        exchange.setDelayed(true);
        return exchange;
    }

    @Bean
    public Binding taskUploadDelayBinding() {
        return BindingBuilder.bind(taskUploadQueue()).to(taskUploadDelayExchange())
                .with(taskUploadDelayRoutingkeyName).noargs();
    }

    @Bean
    public RabbitTemplate taskUploadTemplateDelay(ConnectionFactory connectionFactory) {
        RabbitTemplate template = new RabbitTemplate(connectionFactory);
        template.setExchange(taskUploadDelayExchangeName);
        template.setRoutingKey(taskUploadDelayRoutingkeyName);
        return template;
    }

    @Bean
    public DirectRabbitListenerContainerFactory taskUploadListenerContainer(ConnectionFactory connectionFactory) {
        DirectRabbitListenerContainerFactory factory = new DirectRabbitListenerContainerFactory();
        factory.setConnectionFactory(connectionFactory);
        factory.setConsumersPerQueue(taskUploadMaxConcurrentConsumers);
        factory.setAutoStartup(true);
        factory.setTaskExecutor(taskExecutor);
        factory.setTaskScheduler(taskScheduler);
        return factory;
    }

}
