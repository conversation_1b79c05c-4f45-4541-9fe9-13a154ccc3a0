package com.mcoin.mall.constant;

/**
 * 客户端来源枚举
 * 
 * <AUTHOR>
 * @date 2025-01-31
 */
public enum ClientSourceEnum {
    
    /**
     * MPay服务端
     */
    MPAY_SERVER("MPAY_SERVER", "MPay服务端"),
    
    /**
     * 移动端APP
     */
    MOBILE_APP("MOBILE_APP", "移动端APP"),
    
    /**
     * Web端
     */
    WEB("WEB", "Web端"),
    
    /**
     * 小程序
     */
    MINI_PROGRAM("MINI_PROGRAM", "小程序"),
    
    /**
     * 未知来源
     */
    UNKNOWN("UNKNOWN", "未知来源");
    
    private final String code;
    private final String description;
    
    ClientSourceEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getDescription() {
        return description;
    }
    
    /**
     * 根据字符串返回枚举类型
     *
     * @param source 客户端来源字符串
     * @return 对应的枚举对象，如果没有匹配，则返回 UNKNOWN
     */
    public static ClientSourceEnum fromString(String source) {
        if (source == null) {
            return UNKNOWN;
        }
        
        for (ClientSourceEnum clientSource : ClientSourceEnum.values()) {
            if (clientSource.getCode().equals(source)) {
                return clientSource;
            }
        }
        
        return UNKNOWN;
    }
    
    /**
     * 判断是否为MPay服务端
     *
     * @param source 客户端来源字符串
     * @return 如果是MPay服务端返回true，否则返回false
     */
    public static boolean isMPayServer(String source) {
        return MPAY_SERVER.equals(fromString(source));
    }
}
