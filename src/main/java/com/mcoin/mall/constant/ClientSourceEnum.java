package com.mcoin.mall.constant;

/**
 * 客户端来源枚举
 * 
 * <AUTHOR>
 * @date 2025-01-31
 */
public enum ClientSourceEnum {

    /**
     * mPay的H5
     */
    MPAY_H5("MPAY_H5", "mPay的H5"),

    /**
     * mPay服务端
     */
    MPAY_SERVER("MPAY_SERVER", "mPay服务端");
    
    private final String code;
    private final String description;
    
    ClientSourceEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getDescription() {
        return description;
    }
    
    /**
     * 根据字符串返回枚举类型
     *
     * @param source 客户端来源字符串
     * @return 对应的枚举对象，如果没有匹配，则返回 null
     */
    public static ClientSourceEnum fromString(String source) {
        if (source == null) {
            return null;
        }

        for (ClientSourceEnum clientSource : ClientSourceEnum.values()) {
            if (clientSource.getCode().equals(source)) {
                return clientSource;
            }
        }

        return null;
    }
    
    /**
     * 判断是否为MPay服务端
     *
     * @param source 客户端来源字符串
     * @return 如果是MPay服务端返回true，否则返回false
     */
    public static boolean isMPayServer(String source) {
        return MPAY_SERVER.equals(fromString(source));
    }
}
