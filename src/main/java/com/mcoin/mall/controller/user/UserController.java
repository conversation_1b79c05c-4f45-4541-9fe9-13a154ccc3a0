package com.mcoin.mall.controller.user;

import com.mcoin.mall.component.ContextHolder;
import com.mcoin.mall.constant.HttpHeaderConstants;
import com.mcoin.mall.model.*;
import com.mcoin.mall.service.user.UserService;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

@RestController
public class UserController {

    @Resource
    private UserService userService;
    @Resource
    private ContextHolder contextHolder;


    /**
     * 获取我的其他券列表（包含已使用、已过期、退款订单、查看更多）
     *
     * @return
     */
    @GetMapping(value = "/api/auth/mycouponother", produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    @Deprecated
    public Response<MyCouponOtherResponse> myCouponOther(@Valid MyCouponOtherRequest request) {
        MyCouponOtherCtx ctx = new MyCouponOtherCtx();
        ctx.setRequest(request);
        ctx.setUserId(contextHolder.getAuthUserInfo().getUserId());
        ctx.setLanguage(contextHolder.getLocale().getLanguage());
        MyCouponOtherResponse response = userService.myCouponOther(ctx);
        return Responses.ok(response);
    }


    /**
     * 获取我的未使用券列表
     *
     * @param request HTTP请求参数
     * @param httpRequest HTTP请求对象，用于获取header信息
     * @return 我的优惠券响应
     */
    @GetMapping(value = "/api/auth/mycoupon", produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Response<MyCouponResponse> getCoupon(@Valid MyCouponRequest request, HttpServletRequest httpRequest) {
        MyCouponCtx ctx = new MyCouponCtx();
        ctx.setRequest(request);
        ctx.setUserId(contextHolder.getAuthUserInfo().getUserId());
        ctx.setLanguage(contextHolder.getLocale().getLanguage());

        // 从header中获取客户端来源
        String clientSource = httpRequest.getHeader(HttpHeaderConstants.X_CLIENT_SOURCE);
        ctx.setClientSource(clientSource);

        MyCouponResponse response = userService.myCoupon(ctx);
        return Responses.ok(response);
    }

    /**
     * 钟意某个福利/取消收藏
     *
     * @return
     */
    @GetMapping(value = "/api/auth/collection", produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Response<CollectionResponse> collection(@Valid CollectionRequest request) {
        CollectionResponse response = userService.saveCollection(request, contextHolder.getAuthUserInfo().getUserId());
        return Responses.ok(response);
    }

    @GetMapping(value = "/api/auth/coupons", produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Response<CouponsResponse> coupons(@Valid CouponsRequest request) {
        CouponsResponse response = userService.getCoupons(request);
        return Responses.ok(response);
    }

    @PostMapping(value = "/api/auth/list/collections", consumes = {
            MediaType.APPLICATION_JSON_VALUE,
            MediaType.TEXT_PLAIN_VALUE
    }, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Response<CollectionListResponse> collections(@Valid @RequestBody CollectionListRequest request) {
        CollectionListResponse response = userService.getCollectionList(request);
        return Responses.ok(response);
    }
}
