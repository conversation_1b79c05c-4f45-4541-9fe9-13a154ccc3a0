package com.mcoin.mall.model;

import javax.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


@ApiModel(description = "获取优惠活动商家列表-请求体")
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class ActiveMerchantListRequest {
    @ApiModelProperty(value = "活动ID", required = true, example = "1")
	@NotNull
    private Integer active_id;
    
    @ApiModelProperty(value = "商家名称", example = "测试商家")
    private String name;
    
    @ApiModelProperty(value = "是否参与抽奖活动(0:否,1:是)", example = "1")
    private Integer lucky_draw;
    
    @ApiModelProperty(value = "是否提供优惠券(0:否,1:是)", example = "1")
    private Integer voucher;
    
    @ApiModelProperty(value = "商家地址", example = "澳门特别行政区")
    private String address;
    
    @ApiModelProperty(value = "页码", required = true, example = "1")
	@NotNull
    private Integer page;
    
    @ApiModelProperty(value = "活动名称", example = "双十一大促")
    private String event;
}
