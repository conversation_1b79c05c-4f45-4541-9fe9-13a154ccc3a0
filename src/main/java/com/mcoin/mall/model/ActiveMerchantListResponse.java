package com.mcoin.mall.model;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.annotation.Generated;
import java.io.Serializable;

@ApiModel(description = "获取优惠活动商家列表-响应体")
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Generated("com.robohorse.robopojogenerator")
public class ActiveMerchantListResponse implements Serializable {

	@ApiModelProperty(value = "商家列表", reference = "ActiveMerchantListResponseSnatchListItem")
	@JsonProperty("snatchList")
	private List<SnatchListItem> snatchList;

	@ApiModelProperty(value = "分页信息")
	@JsonProperty("page")
	private Page page;

	@ApiModelProperty(value = "活动信息", reference = "ActiveMerchantListResponseActive")
	@JsonProperty("active")
	private Active active;

	@ApiModelProperty(value = "活动名称列表")
	@JsonProperty("event")
	private List<String> event;

	@Data
	@ApiModel(value = "ActiveMerchantListResponseSnatchListItem", description = "商家信息项")
	@Generated("com.robohorse.robopojogenerator")
	public static class SnatchListItem implements Serializable {

		@ApiModelProperty(value = "商家ID", example = "1")
		@JsonProperty("id")
		private Integer id;

		@ApiModelProperty(value = "活动ID", example = "1")
		@JsonProperty("active_id")
		private Integer activeId;

		@ApiModelProperty(value = "商家名称", example = "测试商家")
		@JsonProperty("name")
		private String name;

		@ApiModelProperty(value = "商家地址", example = "澳门特别行政区")
		@JsonProperty("address")
		private String address;

		@ApiModelProperty(value = "是否参与抽奖活动", example = "是")
		@JsonProperty("lucky_draw")
		private String luckyDraw;

		@ApiModelProperty(value = "是否提供优惠券", example = "是")
		@JsonProperty("voucher")
		private String voucher;

		@ApiModelProperty(value = "活动名称", example = "双十一大促")
		@JsonProperty("event")
		private String event;
	}

	@Data
	@ApiModel(value = "ActiveMerchantListResponseActive", description = "活动信息")
	@Generated("com.robohorse.robopojogenerator")
	public static class Active implements Serializable {

		@ApiModelProperty(value = "活动ID", example = "1")
		@JsonProperty("id")
		private Integer id;

		@ApiModelProperty(value = "活动标题", example = "双十一大促")
		@JsonProperty("title")
		private String title;

		@ApiModelProperty(value = "活动标题(英文)", example = "Double 11 Promotion")
		@JsonProperty("title_en")
		private String titleEn;

		@ApiModelProperty(value = "参与商家数量", example = "10")
		@JsonProperty("count")
		private Integer count;

		@ApiModelProperty(value = "是否支持抽奖活动", example = "1")
		@JsonProperty("lucky_draw")
		private Integer luckyDraw;

		@ApiModelProperty(value = "是否提供优惠券", example = "1")
		@JsonProperty("voucher")
		private Integer voucher;

		@ApiModelProperty(value = "活动名称", example = "双十一大促")
		@JsonProperty("event")
		private String event;

		@ApiModelProperty(value = "活动名称(英文)", example = "Double 11 Promotion")
		@JsonProperty("event_en")
		private String eventEn;
	}
}