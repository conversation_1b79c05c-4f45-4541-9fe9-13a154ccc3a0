package com.mcoin.mall.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.annotation.Generated;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

@ApiModel(description = "限时抢购登记-请求体")
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Generated("com.robohorse.robopojogenerator")
public class ApplyBuyRequest implements Serializable {
	@ApiModelProperty(value = "商品ID", required = true, example = "1")
	@NotNull
	private Integer product_id;

	@ApiModelProperty(value = "购买数量", required = true, example = "1")
	@NotNull
	@Min(1)
	private Integer number;

	@ApiModelProperty(value = "是否参与抢购(0:否,1:是)", required = true, example = "1")
	@NotNull
	private Integer snap_up;

	@ApiModelProperty(value = "场次ID", example = "1")
	@Min(1)
	private Integer sessionId;

	@ApiModelProperty(value = "下单token", example = "token123")
	private String orderToken;
}