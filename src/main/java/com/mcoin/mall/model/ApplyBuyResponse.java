package com.mcoin.mall.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.annotation.Generated;
import java.io.Serializable;
import java.util.Date;

@ApiModel(description = "限时抢购登记-响应体")
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Generated("com.robohorse.robopojogenerator")
public class ApplyBuyResponse implements Serializable {
	@ApiModelProperty(value = "订单ID", example = "12345")
	@JsonProperty("id")
	private String id;

	@ApiModelProperty(value = "商品ID", required = true, example = "1")
	@JsonProperty("product_id")
	private Integer productId;

	@ApiModelProperty(value = "购买数量", required = true, example = "2")
	@JsonProperty("number")
	private Integer number;

	@ApiModelProperty(value = "订单状态(0:待支付,1:已支付,2:已取消,3:已失效)", example = "0")
	@JsonProperty("status")
	private Integer status;

	@Deprecated
	@ApiModelProperty(value = "订单类型", example = "1")
	@JsonProperty("type")
	private Integer type;

	@ApiModelProperty(value = "创建时间", notes = "2025-03-12T09:39:50.478+00:00")
	@JsonProperty("created_at")
	private Date createdAt;

	@ApiModelProperty(value = "剩余库存", example = "100")
	@JsonProperty("stock")
	private Integer stock;
}