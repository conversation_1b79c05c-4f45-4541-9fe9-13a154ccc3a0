package com.mcoin.mall.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.annotation.Generated;
import java.io.Serializable;
import java.util.List;

@ApiModel(description = "获取适用门店列表信息-响应体")
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Generated("com.robohorse.robopojogenerator")
public class ApplyStoresResponse implements Serializable {
	@ApiModelProperty(value = "适用门店列表", reference = "ApplyStoresResponseSnatchListItem")
	@JsonProperty("snatchList")
	private List<SnatchListItem> snatchList;

    @Data
    @ApiModel(description = "适用门店列表项", reference = "ApplyStoresResponseSnatchListItem")
    @Generated("com.robohorse.robopojogenerator")
    public static class SnatchListItem implements Serializable {

        @ApiModelProperty(value = "门店ID", example = "1")
        @JsonProperty("id")
        private Integer id;

        @ApiModelProperty(value = "门店名称", example = "澳门旗舰店")
        @JsonProperty("name")
        private String name;

        @ApiModelProperty(value = "门店地址", example = "澳门特别行政区")
        @JsonProperty("address")
        private String address;

        @ApiModelProperty(value = "联系电话", example = "00853-88888888")
        @JsonProperty("phone")
        private String phone;

        @ApiModelProperty(value = "距离", example = "1.2km")
        @JsonProperty("distance")
        private String distance;

    }

}