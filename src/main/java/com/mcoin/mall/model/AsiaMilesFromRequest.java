package com.mcoin.mall.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(description = "万里通下单前校验-请求体")
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class AsiaMilesFromRequest {
    @ApiModelProperty(value = "会员姓氏", example = "张")
    private String miles_name_family;

    @ApiModelProperty(value = "会员名字", example = "三")
    private String miles_name_given;

    @ApiModelProperty(value = "会员号", example = "123456789")
    private String miles_member;

    @ApiModelProperty(value = "重复输入会员号", example = "123456789")
    private String miles_member_repeat;
}
