package com.mcoin.mall.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.annotation.Generated;
import java.io.Serializable;
import java.util.List;

@ApiModel(description = "获取万里通会员信息-响应体")
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Generated("com.robohorse.robopojogenerator")
public class AsiaMilesUserInfoListResponse implements Serializable {
	@ApiModelProperty(value = "会员信息列表", reference = "AsiaMilesUserInfoListResponseSnatchListItem")
	@JsonProperty("snatchList")
	private List<SnatchListItem> snatchList;

    @Data
    @ApiModel(description = "会员信息项", value = "AsiaMilesUserInfoListResponseSnatchListItem")
    @Generated("com.robohorse.robopojogenerator")
    public static class SnatchListItem implements Serializable {
        @ApiModelProperty(value = "会员ID", example = "12345")
        @JsonProperty("id")
        private Integer id;

        @ApiModelProperty(value = "会员姓氏", example = "张")
        @JsonProperty("miles_first")
        private String milesFirst;

        @ApiModelProperty(value = "会员全名", example = "张三")
        @JsonProperty("miles_name")
        private String milesName;

        @ApiModelProperty(value = "会员编号", example = "AM123456789")
        @JsonProperty("miles_member")
        private String milesMember;
    }

}