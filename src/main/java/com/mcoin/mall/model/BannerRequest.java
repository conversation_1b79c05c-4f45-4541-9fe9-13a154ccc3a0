package com.mcoin.mall.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@ApiModel(description = "首页广告-请求体")
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class BannerRequest implements Serializable {
    @ApiModelProperty(value = "广告样式列表，不填查全部。可选值：1-1模块, 2-1*2模块, 3-1*1模块, 4-feeds流", 
        example = "[\"1\", \"2\"]")
    private List<String> bannerStyle;
}
