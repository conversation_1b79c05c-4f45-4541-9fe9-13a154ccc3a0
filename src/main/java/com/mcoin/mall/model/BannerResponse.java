package com.mcoin.mall.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.annotation.Generated;
import java.io.Serializable;
import java.util.List;

@ApiModel(description = "廣告圖片-响应体")
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Generated("com.robohorse.robopojogenerator")
public class BannerResponse implements Serializable {
	@ApiModelProperty(value = "广告列表", reference = "BannerResponseSnatchListItem")
	@JsonProperty("snatchList")
	private List<SnatchListItem> snatchList;

	@Data
	@ApiModel(description = "广告信息项", value = "BannerResponseSnatchListItem")
	@Generated("com.robohorse.robopojogenerator")
	public static class SnatchListItem implements Serializable {

		@ApiModelProperty(value = "广告ID", example = "123")
		@JsonProperty("id")
		private String id;

		@ApiModelProperty(value = "广告链接", example = "https://example.com")
		@JsonProperty("src")
		private String src;

		@ApiModelProperty(value = "排序", example = "1")
		@JsonProperty("sort")
		private String sort;

		@ApiModelProperty(value = "广播结束时间", example = "2023-12-31 23:59:59")
		@JsonProperty("broadcast_end_time")
		private String broadcastEndTime;

		@ApiModelProperty(value = "广告图片", example = "https://example.com/image.jpg")
		@JsonProperty("img")
		private String img;

		@ApiModelProperty(value = "广播开始时间", example = "2023-01-01 00:00:00")
		@JsonProperty("broadcast_start_time")
		private String broadcastStartTime;

		@ApiModelProperty(value = "广告类型：0-無,1-福利,2-門店,3-外部鏈接,4-實物鏈路,5-專場廣告", example = "1")
		@JsonProperty("type")
		private String type;

		@ApiModelProperty(value = "广告名称", example = "新年促销")
		@JsonProperty("name")
		private String name;

		@ApiModelProperty(value = "是否广播：0-否,1-是", example = "1")
		@JsonProperty("is_broadcast")
		private String isBroadcast;

		@ApiModelProperty(value = "广告位置", example = "首页顶部")
		@JsonProperty("location")
		private String location;
	}
}