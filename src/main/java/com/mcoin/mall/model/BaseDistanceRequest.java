package com.mcoin.mall.model;


import cn.hutool.core.util.CoordinateUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * 位置基类
 * <AUTHOR>
 */
@Data
public class BaseDistanceRequest {

    @ApiModelProperty(value = "经度", example = "113.5491")
    private String lot;

    @ApiModelProperty(value = "纬度", example = "22.1989")
    private String lat;

    public void convertWsgToGsj() {
        if (StringUtils.isNoneBlank(this.lot, this.lat)) {
            double longitude = Double.parseDouble(this.lot);
            double latitude = Double.parseDouble(this.lat);
            CoordinateUtil.Coordinate coordinate = CoordinateUtil.wgs84ToGcj02(longitude, latitude);
            this.lot = Double.toString(coordinate.getLng());
            this.lat = Double.toString(coordinate.getLat());
        } else {
            this.lot = StringUtils.isBlank(this.lot)?"":this.lot;
            this.lat = StringUtils.isBlank(this.lat)?"":this.lat;
        }

    }

}
