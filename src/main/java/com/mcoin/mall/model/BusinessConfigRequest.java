package com.mcoin.mall.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


@ApiModel(value = "BusinessConfigRequest", description = "业务配置请求对象")
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class BusinessConfigRequest {
    /**
     * 是否显示图片，1-显示，0-不显示
     */
    @ApiModelProperty(value = "是否显示图片", notes = "1-显示，0-不显示", required = false)
    private Integer showImg;

}
