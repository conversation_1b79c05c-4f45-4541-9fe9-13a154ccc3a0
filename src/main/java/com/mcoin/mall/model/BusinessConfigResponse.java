package com.mcoin.mall.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "BusinessConfigResponse", description = "业务配置响应对象")
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class BusinessConfigResponse {

    /**
     * 签到配置响应对象
     */
    @ApiModelProperty(value = "签到配置响应对象")
    private SignConfigResponse signConfig;
    
    /**
     * 获取条款响应对象
     */
    @ApiModelProperty(value = "获取条款响应对象")
    private GetClauseResponse clause;
}
