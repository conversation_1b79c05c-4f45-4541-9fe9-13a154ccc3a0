package com.mcoin.mall.model;

import java.io.Serializable;
import java.util.List;

import javax.annotation.Generated;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(description = "获取商圈列表和券类型列表-响应体")
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Generated("com.robohorse.robopojogenerator")
public class BusinessInformationResponse implements Serializable {

	@ApiModelProperty(value = "商圈列表", reference = "BusinessInformationResponseSnatchListItem")
	@JsonProperty("snatchList")
	private List<SnatchListItem> snatchList;

	@ApiModelProperty(value = "券类型列表", reference = "BusinessInformationResponseTypeListItem")
	@JsonProperty("typeList")
	private List<TypeListItem> typeList;

    @Data
    @ApiModel(description = "商圈列表项", value = "BusinessInformationResponseSnatchListItem")
    @Generated("com.robohorse.robopojogenerator")
    public static class SnatchListItem implements Serializable {

        @ApiModelProperty(value = "商圈ID", example = "1")
        @JsonProperty("id")
        private Integer id;

        @ApiModelProperty(value = "商圈名称", example = "澳门商圈")
        @JsonProperty("name")
        private String name;
    }

    @Data
    @ApiModel(description = "券类型列表项", value = "BusinessInformationResponseTypeListItem")
    @Generated("com.robohorse.robopojogenerator")
    public static class TypeListItem implements Serializable {
        @ApiModelProperty(value = "券类型ID", example = "1")
        @JsonProperty("id")
        private Integer id;

        @ApiModelProperty(value = "父级ID", example = "1")
        @JsonProperty("parent_id")
        private Object parentId;

        @ApiModelProperty(value = "券类型名称", example = "澳门商圈")
        @JsonProperty("name")
        private String name;

        @ApiModelProperty(value = "英文名称", example = "Macau商圈")
        @JsonProperty("english_name")
        private String englishName;

        @ApiModelProperty(value = "排序", example = "1")
        @JsonProperty("sort")
        private Integer sort;

        @ApiModelProperty(value = "图标", example = "https://example.com/image.jpg")
        @JsonProperty("icon")
        private Object icon;

        @ApiModelProperty(value = "是否启用", example = "1")
        @JsonProperty("enable")
        private Object enable;

        @ApiModelProperty(value = "类型", example = "1")
        @JsonProperty("type")
        private Object type;

        @ApiModelProperty(value = "Mcoin平台", example = "1")
        @JsonProperty("mcoin_platform")
        private Integer mcoinPlatform;

        @ApiModelProperty(value = "Mcard平台", example = "1")
        @JsonProperty("mcard_platform")
        private Integer mcardPlatform;
    }
}