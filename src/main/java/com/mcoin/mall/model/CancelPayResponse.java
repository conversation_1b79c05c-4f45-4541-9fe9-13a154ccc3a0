package com.mcoin.mall.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(description = "取消支付-响应体")
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class CancelPayResponse {
    @ApiModelProperty(value = "返回消息", example = "success")
    private String message;
    @ApiModelProperty(value = "下单token", example = "token123")
    private String orderToken;
}
