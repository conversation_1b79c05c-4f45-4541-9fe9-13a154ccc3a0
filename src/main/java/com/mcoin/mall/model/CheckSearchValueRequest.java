package com.mcoin.mall.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(description = "检查搜索关键字-请求体")
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class CheckSearchValueRequest {
    @ApiModelProperty(value = "搜索关键字", example = "澳门")
    private String searchTerm;
}
