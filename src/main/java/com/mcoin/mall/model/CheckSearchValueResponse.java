package com.mcoin.mall.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@ApiModel(description = "检查搜索关键字-响应体")
@JsonIgnoreProperties(ignoreUnknown = true)
@EqualsAndHashCode(callSuper = true)
@Data
public class CheckSearchValueResponse extends Response<CheckSearchValueResponse>{

    @ApiModelProperty(value = "商品数量", example = "10")
    @JsonProperty("product_count")
    private Integer productCount;

    @ApiModelProperty(value = "门店数量", example = "5")
    @JsonProperty("stores_count")
    private Integer storesCount;

}
