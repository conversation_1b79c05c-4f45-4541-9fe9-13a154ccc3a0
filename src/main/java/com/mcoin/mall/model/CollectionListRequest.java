package com.mcoin.mall.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@ApiModel(description = "商品收藏列表-请求体")
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@EqualsAndHashCode(callSuper = true)
public class CollectionListRequest extends BaseDistanceRequest implements Serializable {
    @ApiModelProperty(value = "福利类型：0-全部 ,2-优惠券， 3-实物链路", required = true, example = "2")
    @NotNull
    private Integer type;

    @ApiModelProperty(value = "页码", required = true, example = "1")
    @NotNull
    private Integer page;
}
