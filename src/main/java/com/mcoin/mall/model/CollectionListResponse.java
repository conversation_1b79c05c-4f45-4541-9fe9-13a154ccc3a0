package com.mcoin.mall.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.annotation.Generated;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "商品收藏列表-响应体")
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@EqualsAndHashCode(callSuper = true)
@Generated("com.robohorse.robopojogenerator")
public class CollectionListResponse extends Page implements Serializable {
	@JsonProperty("snatchList")
	@ApiModelProperty(value = "商品列表", notes = "包含多个商品的信息", reference = "CollectionListResponseSnatchListItem")
	private List<SnatchListItem> snatchList;

	@JsonProperty("page")
	@ApiModelProperty(value = "分页信息", notes = "用于分页展示商品列表")
	private Page page;

	@Data
	@ApiModel(description = "商品列表项", value = "CollectionListResponseSnatchListItem")
	@Generated("com.robohorse.robopojogenerator")
	public static class SnatchListItem implements Serializable {

		@JsonProperty("id")
		@ApiModelProperty(value = "商品ID", notes = "商品的唯一标识")
		private Integer id;
		@JsonProperty("system_type")
		@ApiModelProperty(value = "系统类型", notes = "商品所属的系统类型")
		private Integer systemType;
		@JsonProperty("img")
		@ApiModelProperty(value = "商品图片", notes = "商品的展示图片的URL")
		private String img;
		@JsonProperty("title")
		@ApiModelProperty(value = "商品标题", notes = "商品的名称或标题")
		private String title;
		@JsonProperty("distance")
		@ApiModelProperty(value = "距离", notes = "商品与用户的距离信息")
		private String distance;
		@JsonProperty("stores_name")
		@ApiModelProperty(value = "店铺名称", notes = "商品所属店铺的名称")
		private String storesName;
		@JsonProperty("collect_count")
		@ApiModelProperty(value = "收藏数量", notes = "商品的收藏数量")
		private String collectCount;
		@JsonProperty("retail_price")
		@ApiModelProperty(value = "零售价格", notes = "商品的零售价格")
		private String retailPrice;
		@JsonProperty("pay_type")
		@ApiModelProperty(value = "支付类型", notes = "商品支持的支付类型")
		private Integer payType;
		@JsonProperty("point")
		@ApiModelProperty(value = "积分", notes = "商品对应的积分")
		private Integer point;
		@JsonProperty("preferential")
		@ApiModelProperty(value = "优惠金额", notes = "商品的优惠金额")
		private BigDecimal preferential;
		@JsonProperty("collect")
		@ApiModelProperty(value = "是否收藏", notes = "表示商品是否被用户收藏，1表示已收藏，0表示未收藏")
		private Integer collect;

		/**
		 * 福利类型
		 * @see com.mcoin.mall.constant.BusinessProductTypeEnum
		 */
		@ApiModelProperty(value = "福利类型", notes = "商品的福利类型，参考BusinessProductTypeEnum枚举", allowableValues = "参考BusinessProductTypeEnum枚举")
		private Integer type;

		/**
		 * 跳转链接
		 */
		@ApiModelProperty(value = "跳转链接", notes = "点击商品后的跳转链接")
		private String hrefUrl;

		@ApiModelProperty(value = "是否售罄", example = "true")
		@JsonProperty("sold_out")
		private boolean soldOut;
	}

}