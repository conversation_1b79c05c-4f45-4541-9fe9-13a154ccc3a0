package com.mcoin.mall.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@ApiModel(description = "钟意某个福利-请求体")
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class CollectionRequest {
    @ApiModelProperty(value = "福利ID", required = true, example = "1")
    @NotNull
    private Integer id;
    @ApiModelProperty(value = "福利类型", required = true, example = "2")
    @NotNull
    private Integer type;

    @ApiModelProperty(value = "是否收藏，不传则直接取反", required = false, example = "1")
    private Integer isCollect;

}
