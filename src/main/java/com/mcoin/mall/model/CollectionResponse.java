package com.mcoin.mall.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(description = "钟意某个福利-响应体")
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class CollectionResponse {
    @ApiModelProperty(value = "收藏数量", example = "10")
    @JsonProperty("collect_count")
    private String collectCount;

    @ApiModelProperty(value = "是否收藏：0-否,1-是", example = "1")
    private Integer collect;

}
