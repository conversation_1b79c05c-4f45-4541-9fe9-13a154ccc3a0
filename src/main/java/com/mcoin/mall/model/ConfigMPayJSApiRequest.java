package com.mcoin.mall.model;

import javax.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(description = "獲取JSAPI列表-请求体")
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class ConfigMPayJSApiRequest {
    @ApiModelProperty(value = "URL", required = true, example = "https://example.com")  
	@NotNull
    private String url;
    @Deprecated
    @ApiModelProperty(value = "状态", required = false)
    private String state;
}
