package com.mcoin.mall.model;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.annotation.Generated;
import java.io.Serializable;

@ApiModel(description = "獲取JSAPI列表-响应体")
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@EqualsAndHashCode(callSuper = true)
@Generated("com.robohorse.robopojogenerator")
public class ConfigMPayJSApiResponse extends Response<Object> implements Serializable {

	@JsonProperty("debug")
	private Integer debug;
	@JsonProperty("appId")
	private String appId;
	@JsonProperty("timestamp")
	private Long timestamp;
	@JsonProperty("nonceStr")
	private String nonceStr;
	@JsonProperty("signature")
	private String signature;
	@JsonProperty("jsApiList")
	private List<String> jsApiList;
}