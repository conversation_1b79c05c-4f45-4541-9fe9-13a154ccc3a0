package com.mcoin.mall.model;

import javax.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(description = "券详情授权登录-请求体")
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class CouponLoginRequest {
    @ApiModelProperty(value = "用户ID", required = true, example = "1")
	@NotNull
    private String customid;
	@ApiModelProperty(value = "订单号", required = true, example = "1")
	@NotNull
    private String order_no;
}
