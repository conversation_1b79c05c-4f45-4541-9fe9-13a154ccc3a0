package com.mcoin.mall.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

@ApiModel(description = "下單扣庫存-请求体")
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class CreateOrderRequest {
    @ApiModelProperty(value = "里程会员姓氏")
    private String miles_name_family;
    
    @ApiModelProperty(value = "里程会员名字")
    private String miles_name_given;
    
    @ApiModelProperty(value = "里程会员ID")
    private Long miles_member;
    
    @ApiModelProperty(value = "重复里程会员ID")
    private Long miles_member_repeat;
    
    @ApiModelProperty(value = "里程数")
    private Integer miles_milage;
    
    @ApiModelProperty(value = "记录ID")
    private String record_id;

    @NotNull
    @Min(1)
    @ApiModelProperty(value = "购买数量", required = true, example = "1")
    private Integer number;
    
    @NotNull
    @ApiModelProperty(value = "M币金额", name = "Mcurrency", required = true, example = "100")
    public Integer Mcurrency;
    
    @NotNull
    @ApiModelProperty(value = "产品ID", required = true, example = "1")
    private Integer id;

    @ApiModelProperty(value = "下单token", required = false, example = "1234567890")
    private String orderToken;

    @ApiModelProperty(value = "订单环境信息", required = false)
    private OrderEnvInfo orderEnvInfo;
}
