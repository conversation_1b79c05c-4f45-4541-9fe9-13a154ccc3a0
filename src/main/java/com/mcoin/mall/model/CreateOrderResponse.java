package com.mcoin.mall.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.annotation.Generated;
import java.io.Serializable;

@ApiModel(description = "下單扣庫存-响应体")
@JsonIgnoreProperties(ignoreUnknown = true)
@Generated("com.robohorse.robopojogenerator")
@Data
public class CreateOrderResponse implements Serializable {
	@ApiModelProperty(value = "订单ID", example = "123")
	@JsonProperty("id")
	private Integer id;
	@ApiModelProperty(value = "订单类型", example = "1")
	@JsonProperty("type")
	private String type;
}