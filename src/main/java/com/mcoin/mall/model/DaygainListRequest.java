package com.mcoin.mall.model;

import javax.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(description = "获取优惠与活动列表-请求体")
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class DaygainListRequest {
	@NotNull
    @ApiModelProperty(value = "产品ID", required = true, example = "1")
    private Integer id;
    @NotNull
    @ApiModelProperty(value = "页码", required = true, example = "1")
    private Integer page;
}
