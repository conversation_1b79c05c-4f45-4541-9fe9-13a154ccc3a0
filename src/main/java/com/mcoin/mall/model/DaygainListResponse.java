package com.mcoin.mall.model;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import javax.annotation.Generated;
import java.io.Serializable;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "获取优惠与活动列表-响应体")
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Generated("com.robohorse.robopojogenerator")
public class DaygainListResponse implements Serializable {
	@ApiModelProperty(value = "商户抢购列表", reference = "DaygainListResponseSnatchListItem")
	@JsonProperty("snatchList")
	private List<SnatchListItem> snatchList;

	@ApiModelProperty(value = "分页信息")
	@JsonProperty("page")
	private Page page;

	@ApiModelProperty(value = "每日优惠信息", reference = "DaygainListResponseDaygain")
	@JsonProperty("daygain")
	private Daygain daygain;

	@Data
	@Generated("com.robohorse.robopojogenerator")
	@ApiModel(description = "每日优惠信息", value = "DaygainListResponseDaygain")
	public static class Daygain implements Serializable {

		@ApiModelProperty(value = "每日优惠ID")
		@JsonProperty("id")
		private Integer id;
		@ApiModelProperty(value = "每日优惠名称")
		@JsonProperty("name")
		private String name;
		@ApiModelProperty(value = "颜色")
		@JsonProperty("color")
		private String color;
		@ApiModelProperty(value = "主题颜色")
		@JsonProperty("theme_color")
		private String themeColor;
		@ApiModelProperty(value = "副标题颜色")
		@JsonProperty("subtitle_color")
		private String subtitleColor;
		@ApiModelProperty(value = "主题文字颜色")
		@JsonProperty("themetext_color")
		private String themetextColor;
		@ApiModelProperty(value = "图片链接")
		@JsonProperty("img")
		private String img;

	}

	@Data
	@Generated("com.robohorse.robopojogenerator")
	@ApiModel(description = "商户抢购列表项", value = "DaygainListResponseSnatchListItem")
	public static class SnatchListItem implements Serializable {
		@ApiModelProperty(value = "商户抢购项ID")
		@JsonProperty("id")
		private Integer id;
		@ApiModelProperty(value = "折扣信息")
		@JsonProperty("discount")
		private String discount;
		@ApiModelProperty(value = "商品列表", reference = "DaygainListResponseItem")
		@JsonProperty("item")
		private List<Item> item;
	}

	@Data
	@Generated("com.robohorse.robopojogenerator")
	@ApiModel(description = "商品信息", value = "DaygainListResponseItem")
	public static class Item implements Serializable {

		@ApiModelProperty(value = "商品ID")
		@JsonProperty("id")
		private Integer id;
		@ApiModelProperty(value = "商品链接")
		@JsonProperty("url")
		private String url;
		@ApiModelProperty(value = "商品logo")
		@JsonProperty("logo")
		private String logo;
		@ApiModelProperty(value = "商户名称")
		@JsonProperty("business_name")
		private String businessName;
		@ApiModelProperty(value = "折扣名称")
		@JsonProperty("discount_name")
		private String discountName;

	}
}