package com.mcoin.mall.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(value = "用戶同意條款-响应体")
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class GetClauseResponse {

    @ApiModelProperty(value = "用戶同意條款状态：0-未同意 1-已同意")
    @JsonProperty("status")
    private Integer status;
    @ApiModelProperty(value = "用戶同意條款 响应描述")
    @JsonProperty("msg")
    private String msg;
    @ApiModelProperty(value = "用戶同意條款ID")
    @JsonProperty("clauseid")
    private String clauseid;
    @ApiModelProperty(value = "用戶同意條款内容")
    @JsonProperty("content")
    private String content;
    @ApiModelProperty(value = "用戶同意條款时间")
    @JsonProperty("create_at")
    private String create_at;
}
