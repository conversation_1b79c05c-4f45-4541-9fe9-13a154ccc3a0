package com.mcoin.mall.model;

import javax.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(description = "保存用戶信息並獲取Token-请求体")
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class GetTokenRequest {
	@NotNull
    @ApiModelProperty(value = "code", required = true, example = "1234567890")
    private String code;
    @ApiModelProperty(value = "state", required = true, example = "1234567890")
    private String state;
}
