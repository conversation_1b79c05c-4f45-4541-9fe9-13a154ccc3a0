package com.mcoin.mall.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.annotation.Generated;
import java.io.Serializable;

@ApiModel(description = "保存用戶信息並獲取Token-响应体")
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@EqualsAndHashCode(callSuper = true)
@Generated("com.robohorse.robopojogenerator")
public class GetTokenResponse extends Response<Object> implements Serializable {
	@JsonProperty("token")
	private String token;
	@JsonProperty("open_id")
	private String openId;
	@JsonProperty("read")
	private Integer read;
	@JsonProperty("state")
	private String state;
	
	//流程错误时，只有msg返回
	@JsonProperty("msg")
	private String msg;

	/**
	 *昵称
	 */
	private String nickname;

	/**
	 *  头像图片
	 */
	private String headimgurl;
}