package com.mcoin.mall.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.annotation.Generated;
import java.io.Serializable;
import java.math.BigDecimal;


@ApiModel(description = "獲取用戶信息-响应体")
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Generated("com.robohorse.robopojogenerator")
public class GetUserInfoResponse implements Serializable {

	@ApiModelProperty(value = "用户积分", example = "100")
	@JsonProperty("point")
	private Integer point;

	@ApiModelProperty(value = "券数量", example = "5")
	@JsonProperty("count")
	private Integer count;

	@ApiModelProperty(value = "用户金额", example = "100.00")
	@JsonProperty("amount")
	private BigDecimal amount;
}