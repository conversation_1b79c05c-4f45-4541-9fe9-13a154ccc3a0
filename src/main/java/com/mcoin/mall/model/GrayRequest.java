package com.mcoin.mall.model;

import javax.validation.constraints.NotBlank;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 灰度请求
 */
@ApiModel(value = "GrayRequest", description = "灰度请求")
@Getter
@Setter
public class GrayRequest {

    /**
     * 特性键
     */
    @ApiModelProperty(value = "特性键", required = true, example = "new_feature_001")
    @NotBlank(message = "featureKey 不能为空")
    private String featureKey;

    /**
     * 客户ID
     */
    @ApiModelProperty(value = "客户ID", example = "12345")
    private String custId;
} 