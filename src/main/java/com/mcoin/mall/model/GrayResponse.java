package com.mcoin.mall.model;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 灰度响应
 */
@ApiModel(value = "GrayResponse", description = "灰度响应")
@Getter
@Setter
public class GrayResponse implements Serializable {

    /**
     * 是否命中灰度
     */
    @ApiModelProperty(value = "是否命中灰度", example = "true")
    private Boolean gray;

} 