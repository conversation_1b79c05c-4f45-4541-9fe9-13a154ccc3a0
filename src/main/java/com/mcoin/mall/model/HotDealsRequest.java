package com.mcoin.mall.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.AccessLevel;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.EqualsAndHashCode;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "熱門優惠-请求体")
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@EqualsAndHashCode(callSuper=true)
public class HotDealsRequest extends BaseDistanceRequest{
    @Getter(AccessLevel.NONE)
    @Setter(AccessLevel.NONE)
    private boolean isRefresh;

    @ApiModelProperty(value = "是否刷新", required = false, example = "false")
    public boolean getIsRefresh() {
        return isRefresh;
    }

    public void setIsRefresh(boolean refresh) {
        isRefresh = refresh;
    }
}
