package com.mcoin.mall.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.annotation.Generated;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@ApiModel(description = "熱門優惠-响应体")
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Generated("com.robohorse.robopojogenerator")
public class HotDealsResponse implements Serializable {
	@ApiModelProperty(value = "抢购列表", reference = "HotDealsResponseSnatchListItem")
	@JsonProperty("snatchList")
	private List<SnatchListItem> snatchList;

    @Data
    @ApiModel(description = "抢购列表项", value = "HotDealsResponseSnatchListItem")
    @Generated("com.robohorse.robopojogenerator")
    public static class SnatchListItem implements Serializable {
        @ApiModelProperty(value = "商品ID")
        @JsonProperty("id")
        private Integer id;

        @ApiModelProperty(value = "商品标题")
        @JsonProperty("title")
        private String title;

        @ApiModelProperty(value = "商品图片URL")
        @JsonProperty("img")
        private String img;

        @ApiModelProperty(value = "商品图片URL（备用）")
        @JsonProperty("image")
        private String image;

        @ApiModelProperty(value = "优惠金额")
        @JsonProperty("preferential")
        private BigDecimal preferential;

        @ApiModelProperty(value = "商品价格")
        @JsonProperty("price")
        private String price;

        @ApiModelProperty(value = "商品原价")
        @JsonProperty("retail_price")
        private String retailPrice;

        @ApiModelProperty(value = "排序字段")
        @JsonProperty("sort")
        private String sort;

        @ApiModelProperty(value = "所需积分")
        @JsonProperty("point")
        private Integer point;

        @ApiModelProperty(value = "商家名称")
        @JsonProperty("business_name")
        private String businessName;

        @ApiModelProperty(value = "购买结束时间")
        @JsonProperty("buy_end_time")
        private String buyEndTime;

        @ApiModelProperty(value = "购买开始时间")
        @JsonProperty("buy_start_time")
        private String buyStartTime;

        @ApiModelProperty(value = "福利类型", notes = "参考BusinessProductTypeEnum枚举")
        private Integer type;

        @ApiModelProperty(value = "跳转链接")
        private String hrefUrl;
    }
}