package com.mcoin.mall.model;

import java.io.Serializable;
import java.util.List;

import javax.annotation.Generated;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(description = "搜索熱詞-响应体")
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Generated("com.robohorse.robopojogenerator")
public class HotKeywordsResponse implements Serializable {
	@ApiModelProperty(value = "搜索建议", reference = "HotKeywordsResponseSuggest")
	@JsonProperty("suggest")
	private Suggest suggest;

	@ApiModelProperty(value = "搜索历史", reference = "HotKeywordsResponseSearches")
	@JsonProperty("searches")
	private Searches searches;

	@Data
	@ApiModel(value = "搜索建议", reference = "HotKeywordsResponseSuggest")
	@Generated("com.robohorse.robopojogenerator")
	public static class Suggest implements Serializable {

		@ApiModelProperty(value = "建议列表", reference = "HotKeywordsResponseSuggestSnatchListItem")
		@JsonProperty("snatchList")
		private List<SnatchListItem> snatchList;

		@Data
		@Generated("com.robohorse.robopojogenerator")
		@ApiModel(value = "搜索建议列表项", reference = "HotKeywordsResponseSuggestSnatchListItem")
		public static class SnatchListItem implements Serializable {

			@ApiModelProperty(value = "建议标签")
			@JsonProperty("label")
			private String label;

			@ApiModelProperty(value = "热度值")
			@JsonProperty("popularity")
			private Integer popularity;

			@ApiModelProperty(value = "标签类型")
			@JsonProperty("label_type")
			private Integer labelType;
		}
	}

	@Data
	@ApiModel(value = "搜索历史", reference = "HotKeywordsResponseSearches")
	@Generated("com.robohorse.robopojogenerator")
	public static class Searches implements Serializable {

		@ApiModelProperty(value = "搜索历史列表", reference = "HotKeywordsResponseSearchesSnatchListItem")
		@JsonProperty("snatchList")
		private List<SnatchListItem> snatchList;

		@Data
		@ApiModel(value = "搜索历史列表项", reference = "HotKeywordsResponseSearchesSnatchListItem")
		@Generated("com.robohorse.robopojogenerator")
		public static class SnatchListItem implements Serializable {

			@ApiModelProperty(value = "搜索ID")
			@JsonProperty("id")
			private Integer id;

			@ApiModelProperty(value = "搜索标签")
			@JsonProperty("label")
			private String label;

			@ApiModelProperty(value = "标签类型")
			@JsonProperty("label_type")
			private Integer labelType;

			@ApiModelProperty(value = "热度值")
			@JsonProperty("popularity")
			private Integer popularity;
		}
	}

	@Data
	@ApiModel(value = "店铺关键词", reference = "HotKeywordsResponseStoreKeyword")
	@Generated("com.robohorse.robopojogenerator")
	public static class StoreKeyword implements Serializable {

		@ApiModelProperty(value = "店铺关键词列表", reference = "HotKeywordsResponseStoreKeywordSnatchListItem")
		@JsonProperty("snatchList")
		private List<SnatchListItem> snatchList;

		@Data
		@ApiModel(value = "店铺关键词列表项", reference = "HotKeywordsResponseStoreKeywordSnatchListItem")
		@Generated("com.robohorse.robopojogenerator")
		public static class SnatchListItem implements Serializable {

			@ApiModelProperty(value = "店铺ID")
			@JsonProperty("id")
			private Integer id;

			@ApiModelProperty(value = "店铺名称")
			@JsonProperty("name")
			private String name;

			@ApiModelProperty(value = "店铺图片URL")
			@JsonProperty("img")
			private String img;

			@ApiModelProperty(value = "排序值")
			@JsonProperty("sort")
			private Integer sort;

			@ApiModelProperty(value = "来源")
			@JsonProperty("src")
			private String src;
		}
	}
}