package com.mcoin.mall.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.annotation.Generated;
import java.io.Serializable;
import java.util.List;

@ApiModel(description = "首页模块-响应体")
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Generated("com.robohorse.robopojogenerator")
public class IndexModuleResponse implements Serializable {
    @ApiModelProperty(value = "抢购列表", reference = "IndexModuleResponseSnatchListItem")
    @JsonProperty("snatchList")
    private List<SnatchListItem> snatchList;

    @Data
    @ApiModel(description = "抢购列表项", value = "IndexModuleResponseSnatchListItem")
    @Generated("com.robohorse.robopojogenerator")
    public static class SnatchListItem implements Serializable {
        /**
         * String	模块
         * 广告-BANNER
         * 著数分类-CATEGORY
         * 限时抢购-CHOOSE
         * 精选推荐-RECOMMEND
         */
        @ApiModelProperty(value = "模块类型", 
            notes = "广告-BANNER\n著数分类-CATEGORY\n限时抢购-CHOOSE\n精选推荐-RECOMMEND")
        private String module;

        /**
         *  ■ 1模塊 - 对应数据库值1
         *     ■ 1*2模塊 -  对应数据库值2
         *     ■ 1*1模塊 - 对应数据库值3
         *   ■ feeds流 - 对应数据库值4
         */
        @ApiModelProperty(value = "banner样式",
            notes = "1模塊 - 对应数据库值1\n1*2模塊 - 对应数据库值2\n1*1模塊 - 对应数据库值3\nfeeds流 - 对应数据库值4")
        private String bannerStyle;
    }

    @ApiModelProperty(value = "是否隐藏鸿蒙模块", notes = "true: 隐藏, false: 不隐藏")
    @JsonProperty("hideHarmonyModule")
    private boolean hideHarmonyModule;

}