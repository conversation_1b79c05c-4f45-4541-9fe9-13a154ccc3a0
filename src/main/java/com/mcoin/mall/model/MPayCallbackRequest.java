package com.mcoin.mall.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(description = "熱門優惠-请求体")
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class MPayCallbackRequest {
    @ApiModelProperty(value = "字符编码", example = "UTF-8")
    private String charset;
    @ApiModelProperty(value = "商户优惠金额", example = "0.00")
    private String mer_discount_amount;
    @ApiModelProperty(value = "订单标题", example = "iPhone 15 Pro Max")
    private String subject;
    @ApiModelProperty(value = "优惠金额", example = "0.00")
    private String discount_amount;
    @ApiModelProperty(value = "签名", example = "e10adc3949ba59abbe56e057f20f883e")
    private String sign;
    @ApiModelProperty(value = "订单描述", example = "iPhone 15 Pro Max 256GB")
    private String body;
    @ApiModelProperty(value = "支付渠道列表")
    private String fund_bill_list;
    @ApiModelProperty(value = "通知ID", example = "1234567890")
    private String notify_id;
    @ApiModelProperty(value = "积分", example = "1000")
    private String points;
    @ApiModelProperty(value = "通知类型", example = "trade_status_sync")
    private String notify_type;
    @ApiModelProperty(value = "交易状态：TRADE_SUCCESS-交易成功;TRADE_FINISHED-交易完成;" +
        "WAIT_BUYER_PAY-等待用户支付;TRADE_DEAL-交易处理中;TRADE_CLOSED-交易关闭", example = "TRADE_SUCCESS")
    private String trade_status;
    @ApiModelProperty(value = "实收金额", example = "9999.99")
    private String receipt_amount;
    @ApiModelProperty(value = "MP优惠金额", example = "0.00")
    private String mp_discount_amount;
    @ApiModelProperty(value = "应用ID", example = "2021003123456789")
    private String app_id;
    @ApiModelProperty(value = "签名类型", example = "RSA2")
    private String sign_type;
    @ApiModelProperty(value = "买家实付金额", example = "9999.99")
    private String buyer_pay_amount;
    @ApiModelProperty(value = "当天是否有活动", example = "true")
    private String curday_has_activity;
    @ApiModelProperty(value = "积分交易号", example = "202310240001")
    private String point_trade_no;
    @ApiModelProperty(value = "通知时间", example = "2023-10-24 12:00:00")
    private String notify_time;
    @ApiModelProperty(value = "买家用户ID", example = "2088102123456789")
    private String buyer_user_id;
    @ApiModelProperty(value = "版本号", example = "1.0")
    private String version;
    @ApiModelProperty(value = "商户订单号", example = "202310240001")
    private String out_trade_no;
    @ApiModelProperty(value = "订单总金额", example = "9999.99")
    private String total_amount;
    @ApiModelProperty(value = "交易号", example = "2023102422001423456789012345")
    private String trade_no;
    @ApiModelProperty(value = "买家登录账号", example = "<EMAIL>")
    private String buyer_logon_id;
    @ApiModelProperty(value = "积分金额", example = "1000")
    private String point_amount;

    public static class Bill {
        @ApiModelProperty(value = "实际金额", example = "9999.99")
        private String real_amount;
        @ApiModelProperty(value = "金额", example = "9999.99")
        private String amount;
        @ApiModelProperty(value = "支付渠道", example = "ALIPAYACCOUNT")
        private String fund_channel;
    }
}
