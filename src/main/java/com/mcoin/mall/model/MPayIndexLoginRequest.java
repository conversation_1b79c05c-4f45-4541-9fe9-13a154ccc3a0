package com.mcoin.mall.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@ApiModel(description = "MPay首頁推廣 登錄-请求体")
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class MPayIndexLoginRequest {
    @ApiModelProperty(value = "用户ID", required = true)
    @NotBlank
    private String id;

    @ApiModelProperty(value = "标签编号")
    private String tabNum;

    @ApiModelProperty(value = "登录类型")
    private String type;

    @ApiModelProperty(value = "来源渠道")
    private String source;

    @ApiModelProperty(value = "OAuth认证参数")
    private String oauthParams;

    /**
     * 返回按钮是否返回原页
     * 1- 是
     * 0或 null或"" -否
     */
    @ApiModelProperty(value = "是否返回原页(1:是, 0/null/空字符串:否)")
    private Integer skipIndex;

}
