package com.mcoin.mall.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(description = "授權登錄-请求体")
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class MPayLoginRequest {
    /**
     * 授权登录的URL
     */
    @ApiModelProperty(value = "授权登录的URL", example = "https://example.com/login")
    private String url;
    /**
     * 授权登录的状态参数
     */
    @ApiModelProperty(value = "授权登录的状态参数", example = "abc123")
    private String state;
    /**
     * 授权登录的来源
     */
    @ApiModelProperty(value = "授权登录的来源", example = "web")
    private String source;
    /**
     * 授权登录的OAuth参数
     */
    @ApiModelProperty(value = "授权登录的OAuth参数", example = "param1=value1&param2=value2")
    private String oauthParams;

}