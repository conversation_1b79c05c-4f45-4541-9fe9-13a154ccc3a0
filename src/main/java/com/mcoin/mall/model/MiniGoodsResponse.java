package com.mcoin.mall.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.annotation.Generated;
import java.io.Serializable;
import java.util.List;

/***
 *
 */
@ApiModel(description = "商城获取福利详情-响应体")
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Generated("com.robohorse.robopojogenerator")
public class MiniGoodsResponse implements Serializable {
    @ApiModelProperty(value = "商品列表", reference = "MiniGoodsResponseSnatchListItem")
    private List<SnatchListItem> goodsList;

    @Data
    @ApiModel(description = "商品信息项", value = "MiniGoodsResponseSnatchListItem")
    @Generated("com.robohorse.robopojogenerator")
    public static class SnatchListItem implements Serializable {

        @ApiModelProperty(value = "商品唯一标识")
        private String uuid;

        @ApiModelProperty(value = "商品库存")
        private Integer stock;

        @ApiModelProperty(value = "商品销量")
        private Integer sales;
    }



}