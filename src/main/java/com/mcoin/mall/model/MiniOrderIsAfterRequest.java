package com.mcoin.mall.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 *
 */
@ApiModel(description = "商城获取订单是否需售后-请求体")
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class MiniOrderIsAfterRequest {
    /**
     * 订单id
     */
    @NotNull(message = "orderId can not be null")
    @ApiModelProperty(value = "订单ID", required = true, example = "1234567890")
    private Integer orderId;

}
