package com.mcoin.mall.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.annotation.Generated;
import java.io.Serializable;
import java.util.List;

/***
 * @author: laiji<PERSON><PERSON>
 * @date 2023-10-20
 */
@ApiModel(description = "请求零售小程序获取订单信息-响应体")
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Accessors(chain = true)
@Generated("com.robohorse.robopojogenerator")
public class MiniOrderListResponse implements Serializable {

    /**
     * 零售小程序订单列表
     */
    @ApiModelProperty(value = "订单列表")
    private List<MiniOrderResponse> resultList;
}