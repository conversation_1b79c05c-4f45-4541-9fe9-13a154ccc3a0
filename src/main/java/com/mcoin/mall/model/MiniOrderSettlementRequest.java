package com.mcoin.mall.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @author: laijinjie
 * @date: 2023/11/16
 */
@ApiModel(description = "定时任务触发零售小程序结算信息同步-请求体")
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class MiniOrderSettlementRequest {
    /**
     * 格式如：["2023-11-25","2023-11-26"]
     */
    @ApiModelProperty(value = "结算日期列表，格式为['yyyy-MM-dd', 'yyyy-MM-dd']", example = "[\"2023-11-25\",\"2023-11-26\"]")
    private List<String> settlementDates;

    /**
     * 测试用，如：/Users/<USER>/Desktop/merchant_settlement_2023-11-15.csv
     */
    @ApiModelProperty(value = "测试文件路径", example = "/Users/<USER>/Desktop/merchant_settlement_2023-11-15.csv")
    private String testFilename;
}
