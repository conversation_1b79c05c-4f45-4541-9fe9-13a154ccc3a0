package com.mcoin.mall.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "获取我的其他券列表-请求体")
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class MyCouponOtherRequest {

    @ApiModelProperty(value = "卖家ID", example = "123456")
    private String sellerid;
    @ApiModelProperty(value = "券的状态，如0-未使用，1-已使用等", example = "0")
    private Integer status;
    @ApiModelProperty(value = "页码，用于分页查询", example = "1")
    private Integer page;

}
