package com.mcoin.mall.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import javax.annotation.Generated;
import java.io.Serializable;
import java.util.List;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "获取我的其他券列表-响应体")
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Generated("com.robohorse.robopojogenerator")
public class MyCouponOtherResponse implements Serializable {
    @ApiModelProperty(value = "抢券列表", reference = "MyCouponOtherResponseSnatchListItem")
    @JsonProperty("snatchList")
    private List<SnatchListItem> snatchList;

    @ApiModel(description = "抢券列表项", value = "MyCouponOtherResponseSnatchListItem")
    @Data
    @Generated("com.robohorse.robopojogenerator")
    public static class SnatchListItem implements Serializable {
        @ApiModelProperty(value = "项目ID")
        @JsonProperty("id")
        private Integer id;
        @ApiModelProperty(value = "订单ID")
        @JsonProperty("orderid")
        private Integer orderid;
        @ApiModelProperty(value = "订单信息", reference = "MyCouponOtherResponseOrderinfo")
        @JsonProperty("orderinfo")
        private Orderinfo orderinfo;
        @ApiModelProperty(value = "店铺名称")
        @JsonProperty("store_name")
        private String storeName;
        @ApiModelProperty(value = "店铺图片")
        @JsonProperty("store_img")
        private String storeImg;
        @ApiModelProperty(value = "店铺ID")
        @JsonProperty("store_id")
        private Integer storeId;
        @ApiModelProperty(value = "退款状态")
        @JsonProperty("refund_status")
        private Integer refundStatus;
        @ApiModelProperty(value = "是否快过期标识")
        @JsonProperty("fast_expiration")
        private String fastExpiration;
        @ApiModelProperty(value = "有效期开始时间", example = "2023-06-26")
        @JsonProperty("vaild_start_time")
        private String vaildStartTime;
        @ApiModelProperty(value = "有效期结束时间", example = "2023-06-26")
        @JsonProperty("vaild_end_time")
        private String vaildEndTime;
        @ApiModelProperty(value = "券状态： 1未使用，2已使用,3.已失效")
        @JsonProperty("status")
        private Integer status;
        @ApiModelProperty(value = "状态提示信息")
        @JsonProperty("status_tips")
        private String statusTips;
        @ApiModelProperty(value = "支付时间", example = "2023-06-26 00:00:00")
        @JsonProperty("payment_time")
        private String paymentTime;
        @ApiModelProperty(value = "卖家ID")
        @JsonProperty("sellerid")
        private Integer sellerid;
        @ApiModelProperty(value = "总页数")
        @JsonProperty("page_count")
        private Integer pageCount;
        @ApiModelProperty(value = "当前页码")
        @JsonProperty("page")
        private Integer page;
        @ApiModelProperty(value = "每页数量")
        @JsonProperty("size")
        private Integer size;
        @ApiModelProperty(value = "店铺是否可用标识")
        @JsonProperty("store_enable")
        private Integer storeEnable;
        @ApiModelProperty(value = "用户使用时间", example = "2023-06-26")
        @JsonProperty("user_time")
        private String userTime;
        @ApiModelProperty(value = "跳转链接")
        @JsonProperty("redirect_url")
        private String redirectUrl;

    }

    @ApiModel(description = "订单信息", value = "MyCouponOtherResponseOrderinfo")
    @Generated("com.robohorse.robopojogenerator")
    @Data
    public static class Orderinfo implements Serializable {

        @ApiModelProperty(value = "订单ID")
        @JsonProperty("id")
        private Integer id;
        @ApiModelProperty(value = "产品ID")
        @JsonProperty("prodcutid")
        private Integer prodcutid;
        @ApiModelProperty(value = "类型（1-招牌產品;2-新優惠;3-現金券;4-1蚊優惠;" +
        "5-積分換領;6-mCoin現金券;7-mPay满减券;8-mPay代金券;9-Mpay優惠券;10-交易核銷券;11-第三方兑换码;12-实物链路）")
        @JsonProperty("type")
        private Integer type;
        @ApiModelProperty(value = "里程会员信息")
        @JsonProperty("miles_member")
        private Object milesMember;
        @ApiModelProperty(value = "图片")
        @JsonProperty("img")
        private String img;
        @ApiModelProperty(value = "里程数")
        @JsonProperty("miles_milage")
        private Object milesMilage;
        @ApiModelProperty(value = "标题快照")
        @JsonProperty("title_snapshots")
        private String titleSnapshots;
    }
}