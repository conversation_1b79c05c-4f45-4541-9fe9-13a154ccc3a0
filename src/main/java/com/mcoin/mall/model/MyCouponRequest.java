package com.mcoin.mall.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(description = "获取我的未使用券列表-请求体")
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class MyCouponRequest {
    @ApiModelProperty(value = "优惠券状态:1未使用，2已使用,3.已失效", example = "1", required = true)
    private Integer status;
}
