package com.mcoin.mall.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

@ApiModel(description = "福利詳情-请求体")
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@EqualsAndHashCode(callSuper=true)
public class ObtainPayRequest extends BaseDistanceRequest{
    /**
     * 实际是productId
     */
    @ApiModelProperty(value = "实际是productId", required = true)
    @NotNull
    private Integer businessId;

    /**
     * 场次ID
     */
    @ApiModelProperty(value = "场次ID", required = true, notes = "场次ID必须大于等于1")
    @Min(1)
    private Integer sessionId;

    /**
     * 未知用途的字段g_v1
     */
    @ApiModelProperty(value = "灰度标识：0-灰度关闭；1-灰度中；2-全量")
    private String g_v1;

    @JsonIgnoreProperties
    public Integer getProductId(){
        return businessId;
    }
}
