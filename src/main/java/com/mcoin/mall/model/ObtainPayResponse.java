package com.mcoin.mall.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.annotation.Generated;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@ApiModel(description = "福利詳情-响应体")
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Generated("com.robohorse.robopojogenerator")
public class ObtainPayResponse implements Serializable, Collectable {

	@JsonProperty("id")
	private Integer id;
	@ApiModelProperty("商戶歸屬哪個系統(1.mCoin,2.會員卡,3.亞洲萬裡通商戶)")
	@JsonProperty("system_type")
	private Integer systemType;
	@ApiModelProperty("最少使用积分")
	@JsonProperty("point")
	private Integer point;
	@ApiModelProperty("最少实际购买价")
	@JsonProperty("preferential")
	private String preferential;
	@ApiModelProperty("限购数量")
	@JsonProperty("limited_number")
	private Integer limitedNumber;
	@ApiModelProperty("现价")
	@JsonProperty("price")
	private String price;
	@ApiModelProperty("福利積分比例")
	@JsonProperty("mpayintegral_proportion")
	private Integer mpayintegralProportion;
	@ApiModelProperty("最大兌換積分（若設置了只能純積分購買，最大兌換積分數不生效）")
	@JsonProperty("maximum_points")
	private Integer maximumPoints;
	@ApiModelProperty("支付类型(0：混合支付  1：純積分  2：純金額)")
	@JsonProperty("pay_type")
	private Integer payType;
	@ApiModelProperty("最小兌換積分（混合支付才有最小兌換積分）")
	@JsonProperty("min_point")
	private Integer minPoint;
	@ApiModelProperty("配额阶梯")
	@JsonProperty("proportion")
	private List<List<Object>> proportion;
	@ApiModelProperty("配额：0-無 1-有")
	@JsonProperty("quota")
	private Integer quota;
	@ApiModelProperty("每個用戶當月兌換上限")
	@JsonProperty("month_mileage")
	private Integer monthMileage;
	@ApiModelProperty("每日用戶當月兌換上限")
	@JsonProperty("day_mileage")
	private Integer dayMileage;
	@ApiModelProperty("抢购状态：0非搶購 1按庫存 2隨機分配")
	@JsonProperty("snap_up")
	private Integer snapUp;
	@ApiModelProperty("库存")
	@JsonProperty("stock")
	private Integer stock;
	@ApiModelProperty("福利标题")
	@JsonProperty("title")
	private String title;
	// 主图
	@ApiModelProperty("福利省略图")
	@JsonProperty("img")
	private String img;;
	@ApiModelProperty("所有图片")
	@JsonProperty("imgs")
	private List<String> imgs;
	@ApiModelProperty("銷量")
	@JsonProperty("sales")
	private String sales;
	@ApiModelProperty("是否顯示銷量：0-不顯示 1-顯示")
	@JsonProperty("is_show_stock_sales")
	private Integer isShowStockSales;

	@ApiModelProperty("門市價")
	@JsonProperty("retail_price")
	private String retailPrice;
	@ApiModelProperty("時間範圍可用")
	@JsonProperty("available")
	private String available;
	@ApiModelProperty("周六日是否可以使用（1可用0不可用）")
	@JsonProperty("applicable")
	private String applicable;
	@ApiModelProperty("細則")
	@JsonProperty("tnc")
	private String tnc;
	@ApiModelProperty("上架狀態（1、上架，2、下架）")
	@JsonProperty("shelf_status")
	private int shelfStatus;
	@ApiModelProperty(value = "適用門店", reference = "ObtainPayResponseStoresItem")
	@JsonProperty("stores")
	private List<StoresItem> stores;
	@ApiModelProperty(value = "第一個適用門店", reference = "ObtainPayResponseOneStores")
	@JsonProperty("one_stores")
	private OneStores oneStores;
	@ApiModelProperty("福利类型")
	@JsonProperty("type")
	private Integer type;
	@ApiModelProperty("亞洲萬裡通提示A")
	@JsonProperty("tips_a")
	private Integer tipsA;
	@ApiModelProperty("亞洲萬裡通提示B")
	@JsonProperty("tips_b")
	private Integer tipsB;
	@ApiModelProperty("分享鏈接")
	@JsonProperty("share_url")
	private String shareUrl;

	@ApiModelProperty("类别编码")
	private String classCode;

	@ApiModelProperty("类别提示内容地址")
	private String classUrl;

	@ApiModelProperty("是否收藏，0：未收藏；1-收藏")
	@JsonProperty("collect")
	private Integer collect;
	@ApiModelProperty("收藏数量")
	@JsonProperty("collect_count")
	private String collectCount;

	@ApiModelProperty("当前系统时间")
	private Date sysTime;
	@ApiModelProperty("购买开始时间")
	private Date buyStartTime;
	@ApiModelProperty("购买结束时间")
	private Date buyEndTime;
	@ApiModelProperty("平台补贴金额")
	private BigDecimal subsidyAmount;
	@ApiModelProperty("抢购状态: 0-抢购中；1-即将开抢；2-已结束")
	private Integer snapStatus;
	@ApiModelProperty("抢购场次ID")
	private Integer sessionId;
	@ApiModelProperty("下单token")
	private String orderToken;
	@ApiModelProperty(value = "是否售罄", example = "true")
	@JsonProperty("sold_out")
	private boolean soldOut;

	@Data
    @Generated("com.robohorse.robopojogenerator")
    @ApiModel(value = "ObtainPayResponseStoresItem", description = "门店项目")
    public static class StoresItem implements Serializable {
		@ApiModelProperty("門店ID")
        @JsonProperty("id")
        private Integer id;
    }

	@Data
	@ApiModel(value = "ObtainPayResponseOneStores", description = "门店项目")
	@Generated("com.robohorse.robopojogenerator")
	public static class OneStores implements Serializable {

		@ApiModelProperty("門店ID")
		@JsonProperty("id")
		private Integer id;
		@ApiModelProperty("門店圖片")
		@JsonProperty("img")
		private String img;
		@ApiModelProperty("門店名稱")
		@JsonProperty("name")
		private String name;
		@ApiModelProperty("商戶ID")
		@JsonProperty("business_id")
		private Integer businessId;
		@ApiModelProperty("距離")
		@JsonProperty("distance")
		private String distance;

	}
}