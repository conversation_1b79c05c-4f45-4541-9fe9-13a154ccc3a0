package com.mcoin.mall.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "获取订单详情-请求体")
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@EqualsAndHashCode(callSuper=true)
public class OrderDetailRequest extends BaseDistanceRequest{
    @ApiModelProperty(value = "券码ID，不能为空", required = true)
    @NotNull
    private Integer codeid;
}
