package com.mcoin.mall.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import javax.annotation.Generated;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "获取订单详情-响应体")
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Generated("com.robohorse.robopojogenerator")
public class OrderDetailResponse implements Serializable {
    @ApiModelProperty(value = "订单ID", example = "1")
    @JsonProperty("id")
    private Integer id;
    @ApiModelProperty(value = "订单状态", example = "2")
    @JsonProperty("status")
    private Integer status;
    @ApiModelProperty(value = "是否允许退款，1表示允许，0表示不允许", example = "1")
    @JsonProperty("is_allow_refund")
    private Integer isAllowRefund;
    @ApiModelProperty(value = "是否为整单，1表示是，0表示否", example = "0")
    @JsonProperty("is_whole")
    private Integer isWhole;
    @ApiModelProperty(value = "订单退款信息", reference = "OrderDetailResponseOrderRefund")
    @JsonProperty("OrderRefund")
    private OrderRefund orderRefund;
    @ApiModelProperty(value = "券码编号", example = "202503110001")
    @JsonProperty("code")
    private String code;
    @ApiModelProperty(value = "订单详情信息", reference = "OrderDetailResponseOrderInfo")
    @JsonProperty("OrderInfo")
    private OrderInfo orderInfo;
    @ApiModelProperty(value = "店铺ID", example = "1001")
    @JsonProperty("store_id")
    private Integer storeId;
    @ApiModelProperty(value = "店铺名称", example = "示例店铺")
    @JsonProperty("store_name")
    private String storeName;
    @ApiModelProperty(value = "分摊的M支付积分", example = "100")
    @JsonProperty("apportion_mpayintegral")
    private Integer apportionMpayintegral;
    @ApiModelProperty(value = "分摊的账单最终金额", example = "100.00")
    @JsonProperty("apportion_bill_final_amount")
    private String apportionBillFinalAmount;
    @ApiModelProperty(value = "状态提示信息", example = "订单已支付")
    @JsonProperty("status_tips")
    private String statusTips;
    @ApiModelProperty(value = "有效期开始时间", example = "2025-03-11 00:00:00")
    @JsonProperty("vaild_start_time")
    private String vaildStartTime;
    @ApiModelProperty(value = "有效期结束时间", example = "2025-03-12 00:00:00")
    @JsonProperty("vaild_end_time")
    private String vaildEndTime;
    @ApiModelProperty(value = "是否支持再次购买，1表示支持，0表示不支持", example = "1")
    @JsonProperty("buy_again")
    private Integer buyAgain;
    @ApiModelProperty(value = "二维码内容")
    @JsonProperty("qrcode")
    private String qrcode;
    @ApiModelProperty(value = "是否可复制，1表示可复制，0表示不可复制", example = "1")
    @JsonProperty("if_copy")
    private Integer ifCopy;
    @ApiModelProperty(value = "可用状态描述", example = "可用")
    @JsonProperty("available")
    private String available;
    @ApiModelProperty(value = "适用范围描述", example = "全店通用")
    @JsonProperty("applicable")
    private String applicable;
    @ApiModelProperty(value = "用户时间")
    @JsonProperty("user_time")
    private Object userTime;
    /**
     * 商户名称
     */
    @ApiModelProperty(value = "商户名称", example = "示例商户")
    private String businessName;

    @ApiModel(description = "订单退款信息", value = "OrderDetailResponseOrderRefund")
    @Data
    public static class OrderRefund implements Serializable {
        @ApiModelProperty(value = "退款ID", example = "1")
        @JsonProperty("id")
        private Integer id;
        @ApiModelProperty(value = "退款订单号", example = "202503110001_REFUND")
        @JsonProperty("refund_orderno")
        private String refundOrderno;
        @ApiModelProperty(value = "退还的M支付积分", example = "100")
        @JsonProperty("mpayintegral")
        private Long mpayintegral;
        @ApiModelProperty(value = "退款金额", example = "100.00")
        @JsonProperty("refund_amount")
        private String refundAmount;
        @ApiModelProperty(value = "申请退款时间", example = "2025-03-11 10:00:00")
        @JsonProperty("application_time")
        private String applicationTime;
        @ApiModelProperty(value = "退款到账时间", example = "2025-03-11 11:00:00")
        @JsonProperty("refund_time")
        private String refundTime;
    }


    @Data
    @Generated("com.robohorse.robopojogenerator")
    @ApiModel(description = "订单详情信息", value = "OrderDetailResponseBusinessProduct")
    public static class BusinessProduct implements Serializable {

        @ApiModelProperty(value = "商品ID", example = "1")
        @JsonProperty("id")
        private Integer id;
        @ApiModelProperty(value = "店铺列表", reference = "OrderDetailResponseStoresItem")
        @JsonProperty("stores")
        private List<StoresItem> stores;

    }

    @Data
    @ApiModel(description = "店铺列表", value = "OrderDetailResponseKeyStoresItem")
    @Generated("com.robohorse.robopojogenerator")
    public static class KeyStoresItem implements Serializable {

        @ApiModelProperty(value = "主键ID", example = "1")
        @JsonProperty("id")
        private Integer id;
        @ApiModelProperty(value = "店铺信息", reference = "OrderDetailResponseStores")
        @JsonProperty("stores")
        private Stores stores;

    }

    @Data
    @ApiModel(description = "店铺信息", value = "OrderDetailResponseOrder")
    @Generated("com.robohorse.robopojogenerator")
    public static class Order implements Serializable {
        @ApiModelProperty(value = "订单ID", example = "1")
        @JsonProperty("id")
        private Integer id;
        @ApiModelProperty(value = "订单编号", example = "202503110001")
        @JsonProperty("order_no")
        private String orderNo;
        @ApiModelProperty(value = "订单创建时间", example = "2025-03-11 09:00:00")
        @JsonProperty("create_time")
        private String createTime;
        @ApiModelProperty(value = "订单支付时间", example = "2025-03-11 09:30:00")
        @JsonProperty("payment_time")
        private String paymentTime;

    }

    @Data
    @ApiModel(description = "订单详情信息", value = "OrderDetailResponseOrderInfo")
    @Generated("com.robohorse.robopojogenerator")
    public static class OrderInfo implements Serializable {

        @ApiModelProperty(value = "订单详情ID", example = "1")
        @JsonProperty("id")
        private Integer id;

        /**
         * 福利类型
         * @see com.mcoin.mall.constant.BusinessProductTypeEnum
         */
        @ApiModelProperty(value = "福利类型，参考 com.mcoin.mall.constant.BusinessProductTypeEnum", example = "1")
        @JsonProperty("type")
        private Integer type;
        @ApiModelProperty(value = "商品图片快照链接", example = "https://example.com/product.jpg")
        @JsonProperty("image_snapshots")
        private String imageSnapshots;
        @ApiModelProperty(value = "商品ID", example = "1")
        @JsonProperty("prodcutid")
        private Integer prodcutid;
        @ApiModelProperty(value = "商品标题快照", example = "示例商品标题")
        @JsonProperty("title_snapshots")
        private String titleSnapshots;
        @ApiModelProperty(value = "是否为代金券，1表示是，0表示否", example = "0")
        @JsonProperty("is_voucher")
        private Integer isVoucher;
        @ApiModelProperty(value = "商户商品信息", reference = "OrderDetailResponseBusinessProduct")
        @JsonProperty("business_product")
        private BusinessProduct businessProduct;
        @ApiModelProperty(value = "使用条款和条件", example = "具体条款内容")
        @JsonProperty("tnc")
        private String tnc;
        @ApiModelProperty(value = "订单ID", example = "1")
        @JsonProperty("orderid")
        private Integer orderid;
        @ApiModelProperty(value = "里程数相关信息")
        @JsonProperty("miles_milage")
        private Object milesMilage;
        @ApiModelProperty(value = "订单信息", reference = "OrderDetailResponseOrder")
        @JsonProperty("order")
        private Order order;
        @ApiModelProperty(value = "里程会员相关信息")
        @JsonProperty("miles_member")
        private Object milesMember;
        @ApiModelProperty(value = "首单里程相关信息")
        @JsonProperty("miles_first")
        private Object milesFirst;
        @ApiModelProperty(value = "里程名称", example = "示例里程名称")
        @JsonProperty("miles_name")
        private Object milesName;
        @ApiModelProperty(value = "关键店铺列表", reference = "OrderDetailResponseKeyStoresItem")
        @JsonProperty("key_stores")
        private List<KeyStoresItem> keyStores;

    }

    @Data
    @ApiModel(description = "店铺信息", value = "OrderDetailResponseStores")
    @Generated("com.robohorse.robopojogenerator")
    public static class Stores implements Serializable {

        @ApiModelProperty(value = "店铺ID", example = "1001")
        @JsonProperty("id")
        private Integer id;
        @ApiModelProperty(value = "店铺名称", example = "示例店铺")
        @JsonProperty("name")
        private String name;

    }

    @Data
    @ApiModel(description = "店铺项", value = "OrderDetailResponseStoresItem")
    @Generated("com.robohorse.robopojogenerator")
    public static class StoresItem implements Serializable {

        @ApiModelProperty(value = "店铺项ID", example = "1")
        @JsonProperty("id")
        private Integer id;
        @ApiModelProperty(value = "商户ID", example = "1")
        @JsonProperty("business_id")
        private Integer businessId;
        @ApiModelProperty(value = "距离描述", example = "1.5公里")
        @JsonProperty("distance")
        private String distance;
        @ApiModelProperty(value = "距离数值", example = "1.5")
        @JsonProperty("distance_num")
        private BigDecimal distanceNum;
        /**
         * 名称
         */
        @ApiModelProperty(value = "店铺名称", example = "示例店铺")
        private String name;
        /**
         * 地址
         */
        @ApiModelProperty(value = "店铺地址", example = "示例地址")
        private String address;
        /**
         * 电话
         */
        @ApiModelProperty(value = "店铺联系电话", example = "1234567890")
        private String phone;
    }


}