package com.mcoin.mall.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

@ApiModel(description = "订单环境信息-请求体")
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@ToString
public class OrderEnvInfo {

    @ApiModelProperty("订单来源：https://aliyuque.antfin.com/mpass/mpay/gz8gdno00hpc7eyw#BEB6")
    private String source;
    @ApiModelProperty("终端类型:H5/小程序")
    private String terminalType;
    @ApiModelProperty("设备品牌：苹果、华为、联想")
    private String deviceBrand;
    @ApiModelProperty("设备型号")
    private String deviceModel;
    @ApiModelProperty("app版本")
    private String appVersion;
    @ApiModelProperty("操作系统版本")
    private String osVersion;
    @ApiModelProperty("sdk的版本")
    private String sdkVersion;
    @ApiModelProperty("国际移动用户识别码")
    private String imsi;
    @ApiModelProperty("国际移动设备身份码")
    private String imei;
    @ApiModelProperty("设备mac")
    private String mac;
    @ApiModelProperty("浏览器")
    private String browser;
    @ApiModelProperty("屏幕大小")
    private String screenSize;
    @ApiModelProperty("app")
    private String app;
    @ApiModelProperty("os")
    private String os;
    @ApiModelProperty("ip地址")
    private String ip;
    @ApiModelProperty("网络类型：2G/3G/4G/5G/WIFI")
    private String networkType;
    @ApiModelProperty("经度")
    private String lng;
    @ApiModelProperty("纬度")
    private String lat;
}
