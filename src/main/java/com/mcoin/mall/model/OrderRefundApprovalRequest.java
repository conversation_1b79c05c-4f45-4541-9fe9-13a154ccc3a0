package com.mcoin.mall.model;

import javax.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "定时任务-自动审批用户退款-请求体")
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class OrderRefundApprovalRequest {

    @NotNull
    @ApiModelProperty(value = "唯一标识ID", example = "1")
    private Integer id;

    @NotNull
    @ApiModelProperty(value = "类型，具体类型含义根据业务而定", example = "2")
    private Integer type;

    @NotNull
    @ApiModelProperty(value = "操作类型，具体操作类型含义根据业务而定", example = "3")
    private Integer operationType;

    @NotNull
    @ApiModelProperty(value = "操作ID", example = "4")

    private Integer operationId;

    @NotNull
    @ApiModelProperty(value = "场景", example = "APPROVAL_REFUND")
    private String refundScene;
}
