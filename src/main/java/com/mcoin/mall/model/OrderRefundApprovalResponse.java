package com.mcoin.mall.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import javax.annotation.Generated;
import java.io.Serializable;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "自动审批退款-响应体")
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Generated("com.robohorse.robopojogenerator")
public class OrderRefundApprovalResponse implements Serializable {

    /**
     * 订单退款审批状态
     */
    @ApiModelProperty(value = "订单退款审批状态：200-成功", example = "200")
    private Integer status;

    /**
     * 订单退款审批响应码
     */
    @ApiModelProperty(value = "订单退款审批响应码：1005-已同意退款；1006-已拒绝退款", example = "1005")
    private Integer code;

}