package com.mcoin.mall.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * See 使用新版PageV2 驼峰标识
 */
@ApiModel(description = "分页", value = "Page")
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Deprecated
public class Page {

    @ApiModelProperty(value = "当前页")
    @JsonProperty("current_page")
    private Integer currentPage;

    @ApiModelProperty(value = "最后页")
    @JsonProperty("last_page")
    private Integer lastPage;

    @ApiModelProperty(value = "每页条数")
    @JsonProperty("per_page")
    private Integer perPage;

    @ApiModelProperty(value = "数据总数")
    @JsonProperty("total")
    private Integer total;
}
