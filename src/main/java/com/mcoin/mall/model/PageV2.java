package com.mcoin.mall.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(description = "分页", value = "PageV2")
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class PageV2 {

    @ApiModelProperty(value = "当前页")
    private Integer currentPage;

    @ApiModelProperty(value = "最后页")
    private Integer lastPage;

    @ApiModelProperty(value = "每页条数")
    private Integer perPage;

    @ApiModelProperty(value = "数据总数")
    private Integer total;
}
