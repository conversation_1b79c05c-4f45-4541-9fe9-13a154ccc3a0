package com.mcoin.mall.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(description = "拉起收銀台-响应体")
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class PopUpCashierResponse {
    /**
     * 应用ID
     */
    @JsonProperty("app_id")
    @ApiModelProperty(value = "应用ID", example = "your_app_id")
    private String appId;
    /**
     * 接口名称
     */
    @JsonProperty("api_name")
    @ApiModelProperty(value = "接口名称", example = "your_api_name")
    private String apiName;
    /**
     * 业务接口代码
     */
    @JsonProperty("biz_api_code")
    @ApiModelProperty(value = "业务接口代码", example = "your_biz_api_code")
    private String bizApiCode;
    /**
     * 数据类型，默认值为Json
     */
    @JsonProperty("data_type")
    @ApiModelProperty(value = "数据类型，默认值为Json", example = "Json")
    private String dataType = "Json";
    /**
     * 字符编码，默认值为UTF-8
     */
    @ApiModelProperty(value = "字符编码，默认值为UTF-8", example = "UTF-8")
    private String charset = "UTF-8";
    /**
     * 签名类型，默认值为RSA
     */
    @JsonProperty("sign_type")
    @ApiModelProperty(value = "签名类型，默认值为RSA", example = "RSA")
    private String signType = "RSA";
    /**
     * 签名
     */
    @ApiModelProperty(value = "签名", example = "your_signature")
    private String sign;
    /**
     * 时间戳
     */
    @ApiModelProperty(value = "时间戳", example = "2025-03-12 09:54:21")
    private String timestamp;
    /**
     * 版本号，默认值为V1.0
     */
    @ApiModelProperty(value = "版本号，默认值为V1.0", example = "V1.0")
    private String version = "V1.0";
    /**
     * 业务内容
     */
    @JsonProperty("biz_content")
    @ApiModelProperty(value = "业务内容", example = "your_biz_content")
    private String bizContent;
}
