package com.mcoin.mall.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.annotation.Generated;
import java.io.Serializable;
import java.util.List;

@ApiModel(description = "获取热门关键字-响应体")
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Generated("com.robohorse.robopojogenerator")
public class PopularSearchesResponse implements Serializable {

    /**
     * 抢购列表
     */
    @ApiModelProperty(value = "抢购列表", notes = "包含热门搜索项的列表", reference = "PopularSearchesResponsePopularSearchItem")
    @JsonProperty("snatchList")
    private List<PopularSearchItem> snatchList;

    @Data
    @ApiModel(description = "热门搜索项", value = "PopularSearchesResponsePopularSearchItem")
    public static class PopularSearchItem{
        /**
         * 标签名称
         */
        @ApiModelProperty(value = "标签名称", notes = "热门搜索项的标签名称")
        @JsonProperty("label")
        private String label;
        /**
         * 热门程度
         */
        @ApiModelProperty(value = "热门程度", notes = "热门搜索项的热门程度，用整数表示")
        @JsonProperty("popularity")
        private Integer popularity;
        /**
         * 标签类型
         */
        @ApiModelProperty(value = "标签类型", notes = "热门搜索项的标签类型，用整数表示")
        @JsonProperty("label_type")
        private Integer labelType;
    }
}