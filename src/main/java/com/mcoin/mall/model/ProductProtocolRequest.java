package com.mcoin.mall.model;

import javax.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "同意产品协议-请求体")
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class ProductProtocolRequest {
    @NotNull
    @ApiModelProperty(value = "产品ID", required = true)
    private Integer productId;
}
