package com.mcoin.mall.model;

import java.io.Serializable;

import javax.annotation.Generated;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import io.swagger.annotations.ApiModel;
import lombok.Data;

@ApiModel(description = "用户同意产品协议-响应体")
@JsonIgnoreProperties(ignoreUnknown = true)
@Generated("com.robohorse.robopojogenerator")
@Data
public class ProductProtocolResponse implements Serializable {
	
}