package com.mcoin.mall.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.mcoin.mall.constant.BusinessProductTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@ApiModel(description = "支付查詢-响应体")
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class QueryPayResponse {
    /**
     * 响应消息
     */
    @ApiModelProperty(value = "响应消息")
    private String respMsg;
    /**
     * 订单ID
     */
    @JsonProperty("orderid")
    @ApiModelProperty(value = "订单ID")
    private Integer orderId;
    /**
     * 订单代码ID
     */
    @JsonProperty("ordercodeid")
    @ApiModelProperty(value = "订单代码ID")
    private Integer orderCodeId;
    /**
     * 支付状态：0-支付中；1=支付成功；<0-失败
     */
    @ApiModelProperty(value = "支付状态：0-支付中；1=支付成功；<0-失败")
    private Integer respStatus;
    /**
     * 重定向URL
     */
    @ApiModelProperty(value = "重定向URL")
    private String redirectUrl;
    /**
     * 福利类型
     * @see BusinessProductTypeEnum
     */
    @ApiModelProperty(value = "福利类型，参考BusinessProductTypeEnum", reference = "BusinessProductTypeEnum")
    private Integer type;
    /**
     * 代码ID列表
     */
    @JsonProperty("codeid")
    @ApiModelProperty(value = "代码ID列表", reference = "QueryPayResponseCodeIdItem")
    private List<CodeIdItem> codeId;
    /**
     * 订单token（respStatus不等于0时返回新的token）
     */
    @ApiModelProperty(value = "订单token（respStatus不等于0时返回新的token）")
    private String orderToken;

    /**
     * 代码ID项
     */
    @Data
    @ApiModel(description = "代码ID项", value = "QueryPayResponseCodeIdItem")
    public static class CodeIdItem {
        /**
         * 代码ID
         */
        @ApiModelProperty(value = "代码ID")
        private Integer id;
    }
}
