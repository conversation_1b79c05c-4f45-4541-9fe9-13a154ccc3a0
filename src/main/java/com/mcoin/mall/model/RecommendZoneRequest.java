package com.mcoin.mall.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

@ApiModel(description = "專區福利-请求体")
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@EqualsAndHashCode(callSuper=true)
public class RecommendZoneRequest extends BaseDistanceRequest{
    @ApiModelProperty(value = "专区类型")
    private String type;
    
    @ApiModelProperty(value = "专区ID", example = "1", required = true)
    @NotNull
    private Integer zonne_id;

    @ApiModelProperty(value = "是否显示喜欢文字", example = "false")
    private boolean isShowLikeText = false;
}
