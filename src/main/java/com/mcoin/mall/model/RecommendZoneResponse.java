package com.mcoin.mall.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.annotation.Generated;
import java.io.Serializable;

@ApiModel(description = "專區福利-响应体")
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Generated("com.robohorse.robopojogenerator")
public class RecommendZoneResponse implements Serializable, Collectable {
	/**
	 * 支付类型
	 */
	@JsonProperty("type")
	@ApiModelProperty(value = "支付类型", example = "credit_card")
	private String type;
	@JsonProperty("store_name")
	@ApiModelProperty(value = "店铺名称", example = "示例店铺")
	private String storeName;
	@JsonProperty("id")
	@ApiModelProperty(value = "商品ID", example = "12345")
	private String id;
	@JsonProperty("name")
	@ApiModelProperty(value = "商品名称", example = "示例商品")
	private String name;
	@JsonProperty("preferential")
	@ApiModelProperty(value = "优惠信息", example = "满减10元")
	private String preferential;
	@JsonProperty("price")
	@ApiModelProperty(value = "商品价格", example = "99.99")
	private String price;
	@JsonProperty("retail_price")
	@ApiModelProperty(value = "零售价格", example = "129.99")
	private String retailPrice;
	@JsonProperty("redirect_url")
	@ApiModelProperty(value = "跳转链接", example = "https://example.com")
	private String redirectUrl;
	@JsonProperty("point")
	@ApiModelProperty(value = "积分", example = "100")
	private String point;
	@JsonProperty("img")
	@ApiModelProperty(value = "商品图片链接", example = "https://example.com/image.jpg")
	private String img;

	/**
	 * 福利商品类型
	 * @see com.mcoin.mall.constant.BusinessProductTypeEnum
	 */
	@ApiModelProperty(value = "福利商品类型，参考com.mcoin.mall.constant.BusinessProductTypeEnum", example = "1")
	private Integer productType;

	/**
	 * 跳转链接
	 */
	@ApiModelProperty(value = "跳转链接", example = "https://example.com/href")
	private String hrefUrl;
	/**
	 * 是否收藏，0：未收藏；1-收藏
	 */
	@JsonProperty("collect")
	@ApiModelProperty(value = "是否收藏，0：未收藏；1：收藏", example = "0")
	private Integer collect;
	/**
	 * 收藏数量
	 */
	@JsonProperty("collect_count")
	@ApiModelProperty(value = "收藏数量", example = "20")
	private String collectCount;

}