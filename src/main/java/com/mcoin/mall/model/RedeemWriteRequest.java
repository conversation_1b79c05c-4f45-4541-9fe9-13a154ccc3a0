package com.mcoin.mall.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "密码核销-请求体")
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class RedeemWriteRequest {
    @ApiModelProperty(value = "核销编号，不能为空", required = true)
    @NotBlank
    private String writenumber;
    @ApiModelProperty(value = "核销码，不能为空", required = true)
    @NotBlank
    private String code;
}
