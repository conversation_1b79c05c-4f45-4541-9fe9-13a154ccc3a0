package com.mcoin.mall.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(description = "刷新Token-响应体")
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class RefreshTokenResponse {

    @ApiModelProperty(value = "访问令牌", example = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...", required = true)
    @JsonProperty("access_token")
    private String accessToken;
    
    @ApiModelProperty(value = "令牌类型", example = "Bearer", required = true)
    @JsonProperty("token_type")
    private String tokenType;
    
    @ApiModelProperty(value = "过期时间（秒）", example = "3600", required = true)
    @JsonProperty("expires_in")
    private Long expiresIn;
    
    @ApiModelProperty(value = "状态码：0-失败；1-成功", example = "1", required = true)
    @JsonProperty("status")
    private Integer status = 1;
}
