package com.mcoin.mall.model;

import javax.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "获取退款券详情-请求体")
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@EqualsAndHashCode(callSuper=true)
public class RefundCodeRequest extends BaseDistanceRequest{
    @NotNull
    @ApiModelProperty(value = "退款券的代码ID，该字段不能为空", required = true)
    private String codeId;
}
