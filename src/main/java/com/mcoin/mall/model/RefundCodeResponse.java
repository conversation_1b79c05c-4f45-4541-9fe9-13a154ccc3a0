package com.mcoin.mall.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import javax.annotation.Generated;
import java.io.Serializable;
import java.math.BigDecimal;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;

@ApiModel(description = "获取退款券详情-响应体")
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Generated("com.robohorse.robopojogenerator")
public class RefundCodeResponse {
    @JsonProperty("id")
    @ApiModelProperty(value = "退款券的唯一标识", example = "1")
    private Integer id;
    @JsonProperty("store_id")
    @ApiModelProperty(value = "店铺的唯一标识", example = "S001")
    private String storeId;
    @JsonProperty("store_name")
    @ApiModelProperty(value = "店铺名称", example = "示例店铺")
    private String storeName;
    @JsonProperty("image_snapshots")
    @ApiModelProperty(value = "图片快照信息", example = "http://example.com/image.jpg")
    private String imageSnapshots;
    @JsonProperty("title_snapshots")
    @ApiModelProperty(value = "标题快照信息", example = "示例标题")
    private String titleSnapshots;
    @JsonProperty("OrderInfoId")
    @ApiModelProperty(value = "订单信息的唯一标识", example = "1001")
    private Integer orderInfoId;
    @JsonProperty("refundNumber")
    @ApiModelProperty(value = "退款数量", example = "1")
    private Integer refundNumber;
    @JsonProperty("point")
    @ApiModelProperty(value = "积分数量", example = "100")
    private Integer point;
    @JsonProperty("preferential")
    @ApiModelProperty(value = "优惠金额", example = "10.00")
    private BigDecimal preferential;
    /**
     * 商户名称
     */
    @ApiModelProperty(value = "商户名称", example = "示例商户")
    private String businessName;

    /**
     * 福利id
     */
    @ApiModelProperty(value = "福利的唯一标识", example = "2001")
    private Integer productId;

	/**
	 * 适用门店列表
	 */
	private List<StoresItem> stores;

	@Data
	@Generated("com.robohorse.robopojogenerator")
	public static class StoresItem implements Serializable {

		@JsonProperty("id")
		private Integer id;
		/**
		 * 名称
		 */
		private String name;

	}

}
