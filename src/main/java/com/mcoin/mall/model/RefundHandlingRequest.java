package com.mcoin.mall.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "提交退款申请-请求体")
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class RefundHandlingRequest {
    @NotNull
    @ApiModelProperty(value = "订单信息ID，不能为空", required = true)
    private Integer orderInfoId;
    @NotNull
    @Min(1)
    @ApiModelProperty(value = "退款数量，不能为空且最小值为1", required = true)
    private Integer number;
    @ApiModelProperty(value = "退款原因")
    private String refund_reason;
    @ApiModelProperty(value = "用户备注")
    private String user_remark;
}
