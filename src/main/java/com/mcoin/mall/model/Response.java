package com.mcoin.mall.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@ApiModel(value = "通用响应头")
@JsonIgnoreProperties(ignoreUnknown = true)
public class Response <T> extends RespMeta<T>{
    @ApiModelProperty(value = "返回码：200-成功")
    @JsonProperty("code")
    private int code;
    @ApiModelProperty(value = "返回信息")
    @JsonProperty("message")
    private String message;
    @ApiModelProperty(value = "状态: 0-失败，1-成功")
    @JsonProperty("status")
    private int status;
    @ApiModelProperty(value = "数据体")
    @JsonProperty("data")
    private T data;


    public enum Status {
        FAILED,
        SUCCESS;
    }

    public enum Code {
        ST_CONSUMED(100),
        ST_NOT_EXIST(101),
        ST_VERIFICATION_UNSUCCESSFUL(102),
        ST_IDEMPOTENT_VERIFICATION(103),
        ST_STORE_NOT_EXIST(105),
        ST_EXPIRED(106),
        ST_NOT_STARTED(107),
        ST_REFUND_STATUS(108),
        ST_REVOKE_INCONSISTENT(109),
        ST_UNPROCESSED(110),
        ST_INVALID_COUPON_TYPE(111),
        ST_NOT_MEET_CONDITION(112),
        ST_NOT_AVAILABLE_TIME(120),
        ST_CANNOT_REVOKE(130),
        ST_VERIFICATION_SUCCESS(131),
        ST_REVOKE_SUCCESS(132),
        ST_VERIFICATION_IN_PROGRESS(133),
        ST_REVOKE_IN_PROGRESS(134),
        ST_VERIFICATION_FAILED(135),
        ST_REVOKE_FAILED(136),
        ST_CODE_INVALID(160),
        ST_CODE_STOP_USE(161),
        BP_NOT_EXISTS(201),

        SUCCESS(200),
        ST_NOT_APPLICABLE(404),
        ST_NOT_MATCH(450),
        ST_FORBIDDEN(451),

        SIGNATURE_FAILED(499),
        BAD_REQUEST(400),
        UNAUTHORIZED(401),
        FORBIDDEN(403),
        TOO_MANY_REQUESTS(429),
        UA_ADD_LIMIT(10001),
        UA_PHONE(10002),
        // 售罄
        SOLD_OUT(10003),
        UNKNOWN_ERROR(500);
        private final int code;
        Code(int code) {
            this.code = code;
        }
        public int get(){
            return this.code;
        }

    }
}
