package com.mcoin.mall.model;

import cn.hutool.core.util.ObjectUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.MessageSource;

import com.mcoin.mall.component.ContextHolder;
import com.mcoin.mall.exception.BusinessException;
import com.mcoin.mall.model.RespMeta.Links;
import com.mcoin.mall.model.RespMeta.Meta;
import com.mcoin.mall.util.ContextUtils;

public class Responses{

    public static <T> Response<T> ok(T data){
        return ok(data, Response.Status.SUCCESS.ordinal());
    }
    public static <T> Response<T> okStatus200(T data){
        return ok(data, Response.Code.SUCCESS.get());
    }

    public static <T> Response<T> ok(T data, int status){
        return ok(data, status, null);
    }

    public static <T> Response<T> ok(T data, int status, String message){
        return ok(data, status,Response.Code.SUCCESS, message);
    }
    
    @SuppressWarnings("unchecked")
    public static <T> Response<T> ok(T data, int status, Response.Code code, String message){
        Response<T> response;

        if (data instanceof Response) {
            response = (Response<T>)data;
        } else {
            response = new Response<>();
            response.setData(data);
        }
        response.setCode(code.get());
        response.setStatus(status);

        if(StringUtils.isEmpty(message)) {
            ContextHolder contextHolder = ContextUtils.getBean(ContextHolder.class);
            MessageSource messageSource = ContextUtils.getBean("messageSource", MessageSource.class);
            message = StringUtils.defaultIfBlank(contextHolder.getResponseMessage(),
                    messageSource.getMessage("message.basic.successful", null, contextHolder.getLocale()));
            response.setMessage(message);
        }else {
        	response.setMessage(message);
        }
        return response;
    }

    public static Response<Object> error(BusinessException e){
        Response<Object> response = new Response<>();
        response.setCode(e.getRespCode().get());
        response.setMessage(e.getMessage());
        response.setStatus(Response.Status.FAILED.ordinal());
        response.setData(ObjectUtil.defaultIfNull(e.getData(), new Object()));
        return response;
    }

    public static <T> Response<T> metaOk(T data, int status, String message, Links links, Meta meta){
    	Response<T> response;

        if (data instanceof RespMeta) {
            response = (Response<T>)data;
        } else {
            response = new Response<>();
            response.setData(data);
        }
        response.setCode(Response.Code.SUCCESS.get());
        response.setStatus(status);
        response.setLinks(links);
        response.setMeta(meta);

        if(StringUtils.isEmpty(message)) {
            ContextHolder contextHolder = ContextUtils.getBean(ContextHolder.class);
            MessageSource messageSource = ContextUtils.getBean("messageSource", MessageSource.class);
            message = StringUtils.defaultIfBlank(contextHolder.getResponseMessage(),
                    messageSource.getMessage("message.basic.successful", null, contextHolder.getLocale()));
            response.setMessage(message);
        }else {
        	response.setMessage(message);
        }
        return response;
    }

}
