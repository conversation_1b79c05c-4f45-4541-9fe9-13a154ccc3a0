package com.mcoin.mall.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "福利搜索-请求体")
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@EqualsAndHashCode(callSuper=true)
public class SearchProductRequest extends BaseDistanceRequest{
    @ApiModelProperty(value = "搜索关键词", example = "手机")
    private String searchTerm;
    @ApiModelProperty(value = "商品分类数组", example = "[\"1\",\"2\"]")
    private String[] category;
    @ApiModelProperty(value = "页码", example = "1")
    private Integer page;
    @ApiModelProperty(value = "商圈ID数组", example = "[\"1\",\"2\"]")
    private String[] information;
    @ApiModelProperty(value = "类型数组", example = "[\"1\",\"2\"]")
    private String[] type;
    @ApiModelProperty(value = "排序字段", example = "price")
    private String sort;
    @ApiModelProperty(value = "排序类型，0-按照经纬度；1-价格；2-销量", example = "0")
    private Integer ordertype;
    @ApiModelProperty(value = "业务分类ID列表，多个ID用逗号分隔", example = "1,2,3")
    private String businessCategoriesIds;

}
