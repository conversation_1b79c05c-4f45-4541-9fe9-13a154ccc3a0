package com.mcoin.mall.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.annotation.Generated;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "福利搜索-响应体")
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@EqualsAndHashCode(callSuper = true)
@Generated("com.robohorse.robopojogenerator")
public class SearchProductResponse extends Page implements Serializable {
	@JsonProperty("snatchList")
	@ApiModelProperty(value = "抢购列表", dataType = "List<SnatchListItem>", reference = "SearchProductResponseSnatchListItem")
	private List<SnatchListItem> snatchList;

	@JsonProperty("page")
	@ApiModelProperty(value = "分页信息", dataType = "Page")
	private Page page;

	@Data
	@Generated("com.robohorse.robopojogenerator")
	@ApiModel(description = "抢购列表项", value = "SearchProductResponseSnatchListItem")
	public static class SnatchListItem implements Serializable {

		@JsonProperty("id")
		@ApiModelProperty(value = "ID", dataType = "Integer")
		private Integer id;
		@JsonProperty("system_type")
		@ApiModelProperty(value = "系统类型", dataType = "Integer")
		private Integer systemType;
		@JsonProperty("img")
		@ApiModelProperty(value = "图片", dataType = "String")
		private String img;
		@JsonProperty("title")
		@ApiModelProperty(value = "标题", dataType = "String")
		private String title;
		@JsonProperty("distance")
		@ApiModelProperty(value = "距离", dataType = "String")
		private String distance;
		@JsonProperty("stores_name")
		@ApiModelProperty(value = "店铺名称", dataType = "String")
		private String storesName;
		@JsonProperty("collect_count")
		@ApiModelProperty(value = "收藏数量", dataType = "String")
		private String collectCount;
		@JsonProperty("retail_price")
		@ApiModelProperty(value = "零售价", dataType = "String")
		private String retailPrice;
		@JsonProperty("pay_type")
		@ApiModelProperty(value = "支付类型", dataType = "Integer")
		private Integer payType;
		@JsonProperty("point")
		@ApiModelProperty(value = "积分", dataType = "Integer")
		private Integer point;
		@JsonProperty("preferential")
		@ApiModelProperty(value = "优惠金额", dataType = "BigDecimal")
		private BigDecimal preferential;
		@JsonProperty("collect")
		@ApiModelProperty(value = "是否收藏", dataType = "Integer")
		private Integer collect;

		/**
		 * 福利类型
		 * @see com.mcoin.mall.constant.BusinessProductTypeEnum
		 */
		@ApiModelProperty(value = "福利类型，参考 com.mcoin.mall.constant.BusinessProductTypeEnum", dataType = "Integer")
		private Integer type;

		/**
		 * 跳转链接
		 */
		@ApiModelProperty(value = "跳转链接", dataType = "String")
		private String hrefUrl;

		/**
		 * 是否抢购，0非抢购，1抢购
		 */
		@ApiModelProperty(value = "是否抢购，0非抢购，1抢购", dataType = "String")
		private String snapUp;


		// 忽略该字段的序列化，仅为CollectionListResponse.SnatchListItem传值用
		@JsonIgnore
		private boolean soldOut;
	}

}