package com.mcoin.mall.model;

import lombok.Data;

import java.util.List;


@Data
public class SearchStoreQuery {
    private String language;
    private String currentTime;
    private String searchTerm;
    private String searchTermSimple;
    private String searchTermTraditional;
    private String searchTermTwTraditional;
    private List<Integer> storeIds;
    private List<Integer> businessInformationIds;
    private List<Integer> category;
    private Integer page;
    private String lat;
    private String lot;
    private List<Integer> type;
    private String sort;
    private Integer offset;
    private Integer limit;
    private List<Integer> businessCategoriesIds;

    /**
     * 是否为HARMONY设备且开关开启
     */
    private Boolean harmonySwitch;
}
