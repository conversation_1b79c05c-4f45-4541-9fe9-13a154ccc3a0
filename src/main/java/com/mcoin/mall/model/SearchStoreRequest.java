package com.mcoin.mall.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "店鋪搜索-请求体")
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@EqualsAndHashCode(callSuper=true)
public class SearchStoreRequest extends BaseDistanceRequest{
    @ApiModelProperty(value = "搜索关键词", example = "便利店")
    private String searchTerm;
    @ApiModelProperty(value = "分类列表", example = "[\"1\",\"2\"]")
    private String[] category;
    @ApiModelProperty(value = "页码", example = "1")
    private Integer page;
    @ApiModelProperty(value = "商圈信息列表", example = "[\"1\",\"2\"]")
    private String[] information;
    @ApiModelProperty(value = "类型列表", example = "[\"实体店\",\"网店\"]")
    private String[] type;
    @ApiModelProperty(value = "排序字段", example = "distance")
    private String sort;
    @ApiModelProperty(value = "排序类型，1：升序，2：降序", example = "1")
    private Integer ordertype;
    @ApiModelProperty(value = "业务分类ID列表", example = "1,2,3")
    private String businessCategoriesIds;

}
