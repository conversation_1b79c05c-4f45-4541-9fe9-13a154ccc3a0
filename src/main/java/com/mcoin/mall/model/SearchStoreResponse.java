package com.mcoin.mall.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import javax.annotation.Generated;
import java.io.Serializable;
import java.util.List;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "店鋪搜索-响应体")
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Generated("com.robohorse.robopojogenerator")
public class SearchStoreResponse implements Serializable {
    @JsonProperty("snatchList")
    @ApiModelProperty(value = "门店列表", required = false, reference = "SearchStoreResponseSnatchListItem")
    private List<SnatchListItem> snatchList;

    @JsonProperty("page")
    @ApiModelProperty(value = "分页信息", required = false)
    private Page page;

    @Data
    @ApiModel(description = "店鋪搜索-响应体", value = "SearchStoreResponseSnatchListItem")
    @Generated("com.robohorse.robopojogenerator")
    public static class SnatchListItem implements Serializable {

        @JsonProperty("id")
        @ApiModelProperty(value = "门店ID", required = false)
        private Integer id;
        @JsonProperty("name")
        @ApiModelProperty(value = "门店名称", required = false)
        private String name;
        @JsonProperty("cover")
        @ApiModelProperty(value = "门店封面", required = false)
        private String cover;
        @JsonProperty("product_count")
        @ApiModelProperty(value = "商品数量", required = false)
        private String productCount;
        @JsonProperty("stores_keyword_name")
        @ApiModelProperty(value = "门店关键词名称", required = false)
        private String storesKeywordName;
        @JsonProperty("distance")
        @ApiModelProperty(value = "距离", required = false)
        private String distance;
    }
}