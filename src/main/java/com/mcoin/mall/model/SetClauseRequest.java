package com.mcoin.mall.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@JsonIgnoreProperties(ignoreUnknown = true)
@ApiModel(description = "用戶同意條款-请求体")
@Data
public class SetClauseRequest {
    @NotNull
    @ApiModelProperty(value = "用戶同意條款ID")
    private String clauseid;
    @NotNull
    @ApiModelProperty(value = "用戶同意條款状态：0-未同意 1-已同意")
    private String status;
}
