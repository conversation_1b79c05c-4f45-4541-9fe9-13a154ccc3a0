package com.mcoin.mall.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(value = "用戶同意條款-响应体")
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class SetClauseResponse {

    @ApiModelProperty(value = "用戶同意條款状态：0-未同意 1-已同意")
    @JsonProperty("status")
    private Integer status;
    @ApiModelProperty(value = "用戶同意條款 响应描述")
    @JsonProperty("msg")
    private String msg;

}
