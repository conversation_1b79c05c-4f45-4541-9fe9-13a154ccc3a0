package com.mcoin.mall.model;

import com.mcoin.mall.bean.FookPlatformOrdercode;
import lombok.Data;

import java.util.Date;

@Data
public class SettlementCtx {
    private FookPlatformOrdercode ordercode;
    private int userId;
    private Integer storesId;
    private Integer mode;
    private String code;
    private boolean passCheck;

    /**
     * 是否是撤销券
     */
    private boolean isWithDraw;

    private String branchCode;
    private String merchantCode;
    private String terminalCode;
    private String externalRefNo;
    private String callbackMsg;

    private String trackingNo;
    private Long businessRedeemTime;
    private Date settlementTime;
    private Date now;
    private Integer orderid;
    private Integer codeId;
}
