package com.mcoin.mall.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import io.swagger.annotations.ApiModelProperty;

@JsonIgnoreProperties(ignoreUnknown = true)
@ApiModel(description = "分享福利-请求体")
@Data
public class ShareProductRequest {
    /**
     * 产品ID，不能为空
     */
    @ApiModelProperty(value = "产品ID，不能为空", required = true)
    @NotBlank
    private String id;
}
