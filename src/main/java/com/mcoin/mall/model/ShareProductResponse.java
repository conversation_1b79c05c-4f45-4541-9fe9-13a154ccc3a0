package com.mcoin.mall.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.math.BigDecimal;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "分享福利-响应体")
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class ShareProductResponse {
    @ApiModelProperty(value = "商品图片", example = "https://example.com/image.jpg")
    private String img;
    @ApiModelProperty(value = "商品名称", example = "iPhone 15")
    private String name;
    @JsonProperty("retail_price")
    @ApiModelProperty(value = "零售价", example = "9999.00")
    private BigDecimal retailPrice;
    @ApiModelProperty(value = "积分", example = "100")
    private Integer point;
    @ApiModelProperty(value = "优惠金额", example = "100.00")
    private BigDecimal preferential;
    @JsonProperty("store_name")
    @ApiModelProperty(value = "店铺名称", example = "Apple Store")
    private String storeName;
    @JsonProperty("snap_up")
    @ApiModelProperty(value = "搶購方式： 0非搶購 1按庫存 2隨機分配", example = "1")
    private Integer snapUp;
    @JsonProperty("redirect_url")
    @ApiModelProperty(value = "跳转链接", example = "https://example.com/product/1")
    private String redirectUrl;
}
