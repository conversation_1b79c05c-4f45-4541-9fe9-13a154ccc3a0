package com.mcoin.mall.model;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.annotation.Generated;
import java.io.Serializable;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "類別福利積分、金額區間-响应体")
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Generated("com.robohorse.robopojogenerator")
public class ShopIntervalResponse implements Serializable {

	@JsonProperty("point_interval")
    @Schema(description = "积分区间列表", example = "实际为数组：[\"150\", \"300\", \"600\"]")
	private List<String> pointInterval;

	@JsonProperty("price_interval")
    @Schema(description = "价格区间列表", example = "实际为数组：[\"1\", \"1000\"]")
	private List<String> priceInterval;
}