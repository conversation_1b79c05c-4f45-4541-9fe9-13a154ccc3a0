package com.mcoin.mall.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.annotation.Generated;
import java.io.Serializable;
import io.swagger.annotations.ApiModelProperty;

@JsonIgnoreProperties(ignoreUnknown = true)
@ApiModel(description = "類別福利-请求体")
@Data
@EqualsAndHashCode(callSuper=true)
@Generated("com.robohorse.robopojogenerator")
public class ShopTypeRequest extends BaseDistanceRequest implements Serializable{

	private Integer category;
	@ApiModelProperty(value = "页码", example = "1")
	private Integer page;
	@ApiModelProperty(value = "起始位置", example = "0")
	private Integer start;
	@ApiModelProperty(value = "每页数量，默认值为10", example = "10")
	private Integer count = 10;
	@ApiModelProperty(value = "类型", example = "type_example")
	private String type;

	@ApiModelProperty(value = "起始积分区间", example = "0-100")
	private String start_point_interval;
	@ApiModelProperty(value = "结束积分区间", example = "100-200")
	private String end_point_interval;

	@ApiModelProperty(value = "起始价格区间", example = "0-100")
	private String start_price_interval;
	@ApiModelProperty(value = "结束价格区间", example = "100-200")
	private String end_price_interval;
}