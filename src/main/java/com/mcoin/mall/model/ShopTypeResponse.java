package com.mcoin.mall.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import javax.annotation.Generated;
import java.io.Serializable;
import java.util.List;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "類別福利-响应体")
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Generated("com.robohorse.robopojogenerator")
public class ShopTypeResponse implements Serializable {
	@JsonProperty("snatchList")
	@ApiModelProperty(value = "门店列表", dataType = "List<SnatchListItem>", reference = "ShopTypeResponseSnatchListItem")
	private List<SnatchListItem> snatchList;

	@JsonProperty("page")
	@ApiModelProperty(value = "分页信息", dataType = "Page", reference = "Page")
	private Page page;

    @Data
    @Generated("com.robohorse.robopojogenerator")
    @ApiModel(description = "门店信息项", value = "ShopTypeResponseSnatchListItem")
    public static class SnatchListItem implements Serializable, Collectable {

        @JsonProperty("id")
        @ApiModelProperty(value = "门店ID", dataType = "String")
        private String id;
        @JsonProperty("store_name")
        @ApiModelProperty(value = "门店名称", dataType = "String")
        private String storeName;
        @JsonProperty("product_name")
        @ApiModelProperty(value = "商品名称", dataType = "String")
        private String productName;
        @JsonProperty("img")
        @ApiModelProperty(value = "商品图片链接", dataType = "String")
        private String img;
        @JsonProperty("price")
        @ApiModelProperty(value = "商品价格", dataType = "String")
        private String price;
        @JsonProperty("retail_price")
        @ApiModelProperty(value = "商品零售价", dataType = "String")
        private String retailPrice;
        @JsonProperty("sales")
        @ApiModelProperty(value = "商品销量", dataType = "String")
        private String sales;
        @JsonProperty("point")
        @ApiModelProperty(value = "商品积分", dataType = "String")
        private String point;
        @JsonProperty("preferential")
        @ApiModelProperty(value = "商品优惠信息", dataType = "String")
        private String preferential;
        @JsonProperty("type")
        @ApiModelProperty(value = "商品类型", dataType = "String")
        private String type;
        @JsonProperty("distance")
        @ApiModelProperty(value = "距离信息", dataType = "String")
        private String distance;
        @JsonProperty("is_show_slaes")
        @ApiModelProperty(value = "是否显示销量", dataType = "String")
        private String isShowSlaes;

        /**
         * 福利类型
         * @see com.mcoin.mall.constant.BusinessProductTypeEnum
         */
        @ApiModelProperty(value = "福利类型，参考 com.mcoin.mall.constant.BusinessProductTypeEnum", dataType = "Integer")
        private Integer productType;

        /**
         * 跳转链接
         */
        @ApiModelProperty(value = "跳转链接", dataType = "String")
        private String hrefUrl;
        /**
         * 是否收藏，0：未收藏；1-收藏
         */
        @JsonProperty("collect")
        @ApiModelProperty(value = "是否收藏，0：未收藏；1：收藏", dataType = "Integer")
        private Integer collect;
        /**
         * 收藏数量
         */
        @JsonProperty("collect_count")
        @ApiModelProperty(value = "收藏数量", dataType = "String")
        private String collectCount;

    }
}