package com.mcoin.mall.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.annotation.Generated;
import java.util.List;
import io.swagger.annotations.ApiModelProperty;

/**
 * @author: laiji<PERSON><PERSON>
 * @date: 2023/11/2
 */
@ApiModel(description = "专场信息-响应体")
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Accessors(chain = true)
@Generated("com.robohorse.robopojogenerator")
public class ShowInfoResponse {
    /**
     * 专场的id
     */
    @ApiModelProperty(value = "专场的id", required = true)
    private Integer showId;

    /**
     * 专场的名称（后端自适配语言）
     */
    @ApiModelProperty(value = "专场的名称（后端自适配语言）", required = true)
    private String showName;

    /**
     * 跳转链接
     */
    @ApiModelProperty(value = "跳转链接", required = true)
    private String redirectUrl;

    /**
     * banner图片路径(数组)
     */
    @ApiModelProperty(value = "banner图片路径(数组)", required = true)
    private List<String> bannerImgs;
}