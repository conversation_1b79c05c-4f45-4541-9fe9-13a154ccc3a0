package com.mcoin.mall.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.annotation.Generated;
import java.io.Serializable;
import java.util.List;
import io.swagger.annotations.ApiModelProperty;

/**
 * @author: laijinjie
 * @date: 2023/10/26
 */
@ApiModel(description = "专场-专区-福利商品信息-响应体")
@JsonIgnoreProperties(ignoreUnknown = true)
@Accessors(chain = true)
@Data
@Generated("com.robohorse.robopojogenerator")
public class ShowResponse {
    /**
     * 专场下专区集合
     */
    @ApiModelProperty(value = "专场下专区集合", required = false)
    private List<ShowZoneResponse> showZones;

    /**
     * 专场-专区下面福利产品的集合，属于同一个专区id
     */
    @ApiModelProperty(value = "专场-专区下面福利产品的集合，属于同一个专区id", required = false)
    private List<ShowZoneProductResponse> showZoneProducts;

    /**
     * 专场的名称（后端自适配语言）
     */
    @ApiModelProperty(value = "专场的名称（后端自适配语言）", required = false)
    private String showName;

    /**
     * 专场分享的链接
     */
    @ApiModelProperty(value = "专场分享的链接", required = false)
    private String shareUrl;

    /**
     * banner图片路径(数组)
     */
    @ApiModelProperty(value = "banner图片路径(数组)", required = false)
    private List<String> bannerImgs;

    /**
     * 条款内容
     */
    @ApiModelProperty(value = "条款内容", required = false)
    private String clauseContent;

    /**
     * 控制只有一个专区时，是否展示专区的名称
     */
    @ApiModelProperty(value = "控制只有一个专区时，是否展示专区的名称", required = false)
    private Integer zoneNameSwitch;

    /**
     * 背景颜色
     */
    @ApiModelProperty(value = "背景颜色", required = false)
    private String showBackgroud;

    /**
     * 主题，1 - Theme 1，2 - Theme 2， 3 - Theme 3
     */
    @ApiModelProperty(value = "主题，1 - Theme 1，2 - Theme 2， 3 - Theme 3", required = false)
    private String showTheme;

    /**
     * 头图列表
     */
    @ApiModelProperty(
        value = "头图列表", 
        required = false,
        reference = "ShowResponseSnatchListItem"  // 添加完整类路径
    )
    private List<SnatchListItem> banners;

    @Data
    @Generated("com.robohorse.robopojogenerator")
    @ApiModel(value= "ShowResponseSnatchListItem", description = "头图列表项")
    public static class SnatchListItem implements Serializable {

        @JsonProperty("img")
        @ApiModelProperty(value = "图片", required = false)
        private String img;
        @JsonProperty("title")
        @ApiModelProperty(value = "标题", required = false)
        private String title;
        /**
         * 跳转链接
         */
        @ApiModelProperty(value = "跳转链接", required = false)
        private String src;
        /**
         * 类型，"0": "無","1": "福利","2": "門店","3": "外部鏈接","4": "實物鏈路","5": "專場廣告"
         */
        @ApiModelProperty(value = "类型，\"0\": \"無\",\"1\": \"福利\",\"2\": \"門店\",\"3\": \"外部鏈接\",\"4\": \"實物鏈路\",\"5\": \"專場廣告\"", required = false)
        private String type;

    }

}