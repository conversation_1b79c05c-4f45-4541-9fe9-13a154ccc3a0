package com.mcoin.mall.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2024/4/29
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "专区搶購下期精选-请求体")
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class ShowSnapUpNextRequest extends SnapUpNextRequest{
    @NotNull(message = "专场不能为空")
    private Integer showId;
}
