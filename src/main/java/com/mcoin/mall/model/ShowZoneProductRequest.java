package com.mcoin.mall.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import javax.validation.constraints.NotNull;
import io.swagger.annotations.ApiModelProperty;

/**
 * @author: laijinjie
 * @date: 2023/10/24
 */
@ApiModel(description = "获取专场-专区-福利商品-请求体")
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class ShowZoneProductRequest {
    /**
     * 专场的Id
     */
    @ApiModelProperty(value = "专场的Id", required = true)
    @NotNull(message = "showId can not be null")
    private Integer showId;

    /**
     * 专区的id
     */
    @ApiModelProperty(value = "专区的id")
    private Integer zoneId;

    @ApiModelProperty(value = "灰度标识：0-灰度关闭；1-灰度中；2-全量")
    private String g_v1;

}
