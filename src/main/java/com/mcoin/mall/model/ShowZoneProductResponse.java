package com.mcoin.mall.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import javax.annotation.Generated;
import java.math.BigDecimal;
import io.swagger.annotations.ApiModelProperty;

/**
 * @author: laiji<PERSON><PERSON>
 * @date: 2023/10/26
 */
@ApiModel(description = "专场-专区-福利商品-响应体")
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Generated("com.robohorse.robopojogenerator")
public class ShowZoneProductResponse implements Collectable {
    /**
     * 专区id
     */
    @ApiModelProperty(value = "专区id", example = "1")
    private Integer zoneId;

    /**
     * 福利商品id
     */
    @ApiModelProperty(value = "福利商品id", example = "1001")
    private Integer productId;

    /**
     * 福利图片地址，如https://oss-mcoin-uat.fookunion.com/product/EzEyMVwyF69n2UxfrYNc.jpg
     */
    @ApiModelProperty(value = "福利图片地址", example = "https://oss-mcoin-uat.fookunion.com/product/EzEyMVwyF69n2UxfrYNc.jpg")
    private String img;

    /**
     * 福利商品类型
     *
     * @see com.mcoin.mall.constant.BusinessProductTypeEnum
     */
    @ApiModelProperty(value = "福利商品类型，参考com.mcoin.mall.constant.BusinessProductTypeEnum", example = "1")
    private Integer type;

    /**
     * 所需积分（页面展示）
     */
    @ApiModelProperty(value = "所需积分（页面展示）", example = "100")
    private Integer point;

    /**
     * 门店名称（页面展示）
     */
    @ApiModelProperty(value = "门店名称（页面展示）", example = "示例门店")
    private String storeName;

    /**
     * 福利商品名称（页面展示）
     */
    @ApiModelProperty(value = "福利商品名称（页面展示）", example = "示例商品")
    private String productName;

    /**
     * 优惠的价格（页面展示）
     */
    @ApiModelProperty(value = "优惠的价格（页面展示）", example = "9.9")
    private BigDecimal preferential;

    /**
     * 跳转链接
     */
    @ApiModelProperty(value = "跳转链接", example = "https://example.com")
    private String redirectUrl;

    /**
     * 零售的价格（页面会展示）
     */
    @ApiModelProperty(value = "零售的价格（页面会展示）", example = "19.9")
    private BigDecimal retailPrice;

    /**
     * 价格
     */
    @ApiModelProperty(value = "价格", example = "15.9")
    private BigDecimal price;

    /**
     * 是否收藏，0：未收藏；1-收藏
     */
    @JsonProperty("collect")
    @ApiModelProperty(value = "是否收藏，0：未收藏；1-收藏", example = "0")
    private Integer collect;

    /**
     * 收藏数量
     */
    @JsonProperty("collect_count")
    @ApiModelProperty(value = "收藏数量", example = "10")
    private String collectCount;

    @ApiModelProperty(value = "是否售罄", example = "true")
    @JsonProperty("sold_out")
    private boolean soldOut;

}