package com.mcoin.mall.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.annotation.Generated;
import io.swagger.annotations.ApiModelProperty;

/**
 * @author: laiji<PERSON><PERSON>
 * @date: 2023/10/26
 */
@ApiModel(description = "专场-专区-响应体")
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Accessors(chain = true)
@Generated("com.robohorse.robopojogenerator")
public class ShowZoneResponse {

    /**
     * 专区的id
     */
    @ApiModelProperty(value = "专区的id", example = "1")
    private Integer zoneId;

    /**
     * 专区的标题（自动匹配语言）
     */
    @ApiModelProperty(value = "专区的标题（自动匹配语言）", example = "热门专区")
    private String title;

}