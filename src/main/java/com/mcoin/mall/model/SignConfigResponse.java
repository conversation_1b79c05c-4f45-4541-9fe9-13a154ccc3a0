package com.mcoin.mall.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "簽到配置-响应体")
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class SignConfigResponse {

    @ApiModelProperty(value = "HTTP链接", required = false)
    @JsonProperty("http_url")
    private String httpUrl;

    @ApiModelProperty(value = "图片链接", required = false)
    @JsonProperty("image_url")
    private String imageUrl;

    /**
     * 签到标题
     */
    @ApiModelProperty(value = "签到标题", required = false)
    private String title;
}
