package com.mcoin.mall.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "签到-请求体")
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class SignRequest implements Serializable {
    /**
     * 1 -显示 img_url
     */
    @ApiModelProperty(value = "1 -显示 img_url", example = "1")
    private Integer showImg;
}
