package com.mcoin.mall.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "搶購更多-请求体")
@JsonIgnoreProperties(ignoreUnknown = true)
@EqualsAndHashCode(callSuper = true)
@Data
public class SnapUpMoreRequest extends SnapUpRequest{
    @ApiModelProperty(value = "页码", example = "1")
    private Integer page;
}
