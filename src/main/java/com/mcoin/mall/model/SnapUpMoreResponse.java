package com.mcoin.mall.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "搶購更多-响应体")
@JsonIgnoreProperties(ignoreUnknown = true)
@EqualsAndHashCode(callSuper = true)
@Data
public class SnapUpMoreResponse extends SnapUpResponse{
    @ApiModelProperty(value = "分页信息", notes = "包含分页的相关数据，如页码、每页数量等")
    private Page page;
}
