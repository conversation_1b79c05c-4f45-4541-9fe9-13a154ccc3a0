package com.mcoin.mall.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "搶購下期精选-请求体")
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class SnapUpNextRequest extends SnapUpRequest {
    @ApiModelProperty(value = "页码", example = "1")
    private Integer page;
}
