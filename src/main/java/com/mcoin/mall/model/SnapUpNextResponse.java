package com.mcoin.mall.model;

import javax.annotation.Generated;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

@ApiModel(description = "搶購下期精选-响应体")
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@EqualsAndHashCode(callSuper = true)
@Generated("com.robohorse.robopojogenerator")
public class SnapUpNextResponse extends SnapUpResponse {

}