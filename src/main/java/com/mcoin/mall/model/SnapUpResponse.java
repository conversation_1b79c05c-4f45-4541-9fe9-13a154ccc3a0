package com.mcoin.mall.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import javax.annotation.Generated;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "本期搶購-响应体")
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Generated("com.robohorse.robopojogenerator")
public class SnapUpResponse implements Serializable {
    @ApiModelProperty(value = "抢购列表", required = false, reference = "SnapUpResponseSnatchListItem")
    @JsonProperty("snatchList")
    private List<SnatchListItem> snatchList;

    @ApiModelProperty(value = "系统时间", required = false)
    @JsonProperty("sys_time")
    private String sysTime;

    @Data
    @ApiModel(description = "抢购列表项", value = "SnapUpResponseSnatchListItem")
    @Generated("com.robohorse.robopojogenerator")
    public static class SnatchListItem implements Serializable, Collectable {

        @ApiModelProperty(value = "ID", required = false)
        @JsonProperty("id")
        private Integer id;

        @ApiModelProperty(value = "标题", required = false)
        @JsonProperty("title")
        private String title;

        @ApiModelProperty(value = "抢购状态", required = false)
        @JsonProperty("snap_up_status")
        private Integer snapUpStatus;

        @ApiModelProperty(value = "图片", required = false)
        @JsonProperty("img")
        private String img;

        @ApiModelProperty(value = "距离", required = false)
        @JsonProperty("distance")
        private String distance;

        @ApiModelProperty(value = "零售价格", required = false)
        @JsonProperty("retail_price")
        private BigDecimal retailPrice;

        @ApiModelProperty(value = "支付类型", required = false)
        @JsonProperty("pay_type")
        private Integer payType;

        @ApiModelProperty(value = "积分", required = false)
        @JsonProperty("point")
        private Integer point;

        @ApiModelProperty(value = "优惠金额", required = false)
        @JsonProperty("preferential")
        private BigDecimal preferential;

        @ApiModelProperty(value = "占用情况", required = false)
        @JsonProperty("occupy")
        private Integer occupy;

        @ApiModelProperty(value = "抢购提示", required = false)
        @JsonProperty("snap_up_tips")
        private String snapUpTips;

        @ApiModelProperty(value = "图片", required = false)
        @JsonProperty("image")
        private String image;

        @ApiModelProperty(value = "价格", required = false)
        @JsonProperty("price")
        private BigDecimal price;

        @ApiModelProperty(value = "购买结束时间", required = false)
        @JsonProperty("buy_end_time")
        private String buyEndTime;

        @ApiModelProperty(value = "热度", required = false)
        @JsonProperty("boom")
        private Integer boom;

        @ApiModelProperty(value = "抢购时间", required = false)
        @JsonProperty("rush_to_buy_time")
        private String rushToBuyTime;

        @ApiModelProperty(value = "是否可抢购", required = false)
        @JsonProperty("snap_up")
        private Integer snapUp;

        @ApiModelProperty(value = "是否收藏", required = false)
        @JsonProperty("collect")
        private Integer collect;

        @ApiModelProperty(value = "收藏数量", required = false)
        @JsonProperty("collect_count")
        private String collectCount;

        @ApiModelProperty(value = "商家名称", required = false)
        @JsonProperty("business_name")
        private String businessName;

        @ApiModelProperty(value = "库存", required = false)
        @JsonProperty("stock")
        private Integer stock;

        @ApiModelProperty(value = "实际销量", required = false)
        @JsonProperty("actual_sales")
        private Integer actualSales;

        @ApiModelProperty(value = "剩余时间", required = false)
        @JsonProperty("time_left")
        private Long timeLeft;

        @ApiModelProperty(value = "开始时间戳", required = false)
        @JsonProperty("time_begin")
        private Long timeBegin;

        @ApiModelProperty(value = "开始时间字符串", required = false)
        @JsonProperty("start")
        private String start;

        @ApiModelProperty(value = "结束时间字符串", required = false)
        @JsonProperty("end")
        private String end;

        /**
         * 福利类型
         * @see com.mcoin.mall.constant.BusinessProductTypeEnum
         */
        @ApiModelProperty(value = "福利类型，参考 com.mcoin.mall.constant.BusinessProductTypeEnum", required = false)
        private Integer type;

        /**
         * 跳转链接
         */
        @ApiModelProperty(value = "跳转链接", required = false)
        private String hrefUrl;

    }
}