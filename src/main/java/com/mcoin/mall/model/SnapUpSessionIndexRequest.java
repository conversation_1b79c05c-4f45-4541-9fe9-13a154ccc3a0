package com.mcoin.mall.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.Min;

@ApiModel(description = "搶購專場-首页福利-请求体")
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@EqualsAndHashCode(callSuper = true)
public class SnapUpSessionIndexRequest extends BaseDistanceRequest {

    @ApiModelProperty("返回商品数量，默认3")
    @Min(value = 1, message = "商品数量不能小于1")
    private Integer num;

}
