package com.mcoin.mall.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@ApiModel(description = "搶購專場-首页福利-响应体")
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class SnapUpSessionIndexResponse {

    @ApiModelProperty(value = "抢购福利集合")
    private List<SnapUpSessionProductItem> snatchList;

    @ApiModelProperty(value = "系统时间", notes = "2025-03-12T09:39:50.478+00:00")
    private Date sysTime;

    @ApiModelProperty(value = "场次开始使时间", notes = "2025-03-12T09:39:50.478+00:00")
    private Date sessionStartTime;

    @ApiModelProperty(value = "场次结束时间", notes = "2025-03-12T09:39:50.478+00:00")
    private Date sessionEndTime;

    @ApiModelProperty("抢购状态: 0-抢购中；1-即将开抢；3-已结束")
    private Integer sessionStatus;

    @ApiModelProperty("场次ID")
    private Integer sessionId;

}
