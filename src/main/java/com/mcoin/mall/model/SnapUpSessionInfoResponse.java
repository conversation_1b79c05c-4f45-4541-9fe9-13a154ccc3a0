package com.mcoin.mall.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@ApiModel(description = "搶購專場場次-响应体")
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class SnapUpSessionInfoResponse {

    @ApiModelProperty("系统时间")
    private Date sysTime;

    @ApiModelProperty(value = "抢购场次列表", reference = "SnapUpSessionInfoResponseSnapUpSessionInfo")
    private List<SnapUpSessionInfo> sessionInfoList;

    @Data
    @ApiModel(description = "抢购场次信息", value = "SnapUpSessionInfoResponseSnapUpSessionInfo")
    public static class SnapUpSessionInfo{
        @ApiModelProperty("抢购场次id")
        private Integer sessionId;

        @ApiModelProperty("抢购场次名称")
        private String sessionName;

        @ApiModelProperty("抢购场次日期: 开枪中显示“今日/Today”，即将开始显示“明日/Tomorrow”")
        private String sessionDateTxt;

        @ApiModelProperty(value = "开始时间", notes = "2025-03-12T09:39:50.478+00:00")
        private Date startTime;

        @ApiModelProperty(value = "结束时间", notes = "2025-03-12T09:39:50.478+00:00")
        private Date endTime;

        @ApiModelProperty("抢购状态: 0-抢购中；1-即将开抢；2-已结束")
        private Integer status;
    }

}
