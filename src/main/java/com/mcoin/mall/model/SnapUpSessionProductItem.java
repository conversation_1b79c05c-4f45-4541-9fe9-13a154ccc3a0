package com.mcoin.mall.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class SnapUpSessionProductItem implements Collectable {

    @ApiModelProperty("福利ID")
    private Integer id;
    @ApiModelProperty("福利标题")
    private String title;
    @ApiModelProperty("福利省略图")
    private String img;
    @ApiModelProperty("距离")
    private String distance;
    @ApiModelProperty("門市價")
    private BigDecimal retailPrice;
    @ApiModelProperty("支付类型(0：混合支付  1：純積分  2：純金額)")
    private Integer payType;
    @ApiModelProperty("最少使用积分")
    private Integer point;
    @ApiModelProperty("最少实际购买价")
    private BigDecimal preferential;
    @ApiModelProperty("抢购进度：0-100")
    private Integer occupy;

    @ApiModelProperty("福利图")
    private String image;
    @ApiModelProperty("现价")
    private BigDecimal price;
    @ApiModelProperty(value = "购买结束时间", notes = "2025-03-12T09:39:50.478+00:00")
    private Date buyEndTime;
    @ApiModelProperty("商户ID")
    private Integer boom;
    @ApiModelProperty("抢购状态：0非搶購 1按庫存 2隨機分配")
    private Integer snapUp;
    @ApiModelProperty("是否收藏")
    private Integer collect;
    @ApiModelProperty("收藏数量")
    private String collectCount;
    @ApiModelProperty("商户名称")
    private String businessName;
    @ApiModelProperty("库存")
    private Integer stock;
    @ApiModelProperty("销量")
    private Integer actualSales;

    /**
     * 福利类型
     * @see com.mcoin.mall.constant.BusinessProductTypeEnum
     */
    @ApiModelProperty("福利类型")
    private Integer type;

    @ApiModelProperty("跳转链接")
    private String hrefUrl;

    @ApiModelProperty("直降金额=原价-现价")
    private BigDecimal subsidyAmount;
}
