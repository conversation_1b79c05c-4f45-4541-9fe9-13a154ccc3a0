package com.mcoin.mall.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

@ApiModel(description = "搶購專場場次-商品-请求体")
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@EqualsAndHashCode(callSuper = true)
public class SnapUpSessionProductRequest extends BaseDistanceRequest {

    @ApiModelProperty("场次ID")
    @NotNull
    private Integer sessionId;
    @ApiModelProperty("第几页，从1开始")
    @NotNull
    private Integer page;
    @ApiModelProperty("置顶福利ID")
    private Integer productId;

}
