package com.mcoin.mall.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import javax.annotation.Generated;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "查询是否登记成功-请求体")
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Generated("com.robohorse.robopojogenerator")
public class SnapUpSuccessRequest implements Serializable {

    @ApiModelProperty(value = "ID，不能为空", required = true)
    @NotBlank
    private String id;
    @ApiModelProperty(value = "产品ID，不能为空", required = true)
    @NotNull
    private Integer product_id;
    @ApiModelProperty(value = "数量，不能为空且最小值为1", required = true)
    @NotNull
    @Min(1)
    private Integer number;
    @ApiModelProperty(value = "场次ID，最小值为1", required = false)
    @Min(1)
    private Integer sessionId;
}