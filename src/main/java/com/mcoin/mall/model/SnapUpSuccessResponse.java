package com.mcoin.mall.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import javax.annotation.Generated;
import java.io.Serializable;
import java.util.Date;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "查询是否登记成功-响应体")
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Generated("com.robohorse.robopojogenerator")
public class SnapUpSuccessResponse implements Serializable {
	@ApiModelProperty(value = "唯一标识", example = "123456")
	@JsonProperty("id")
	private String id;
	@ApiModelProperty(value = "产品ID", example = "1")
	@JsonProperty("product_id")
	private Integer productId;
	@ApiModelProperty(value = "数量", example = "10")
	@JsonProperty("number")
	private Integer number;
	@ApiModelProperty(value = "状态", example = "1")
	@JsonProperty("status")
	private Integer status;
	@ApiModelProperty(value = "类型", example = "1")
	@JsonProperty("type")
	private Integer type;
	@ApiModelProperty(value = "创建时间", notes = "2025-03-12T09:39:50.478+00:00")
	@JsonProperty("created_at")
	private Date createdAt;
	@ApiModelProperty(value = "库存数量", example = "100")
	@JsonProperty("stock")
	private Integer stock;
}