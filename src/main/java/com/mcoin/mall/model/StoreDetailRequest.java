package com.mcoin.mall.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

@ApiModel(description = "門店詳情-请求体")
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@EqualsAndHashCode(callSuper = true)
public class StoreDetailRequest extends BaseDistanceRequest{
    @NotNull
    @ApiModelProperty("門店ID")
    private String welfareId;
    @ApiModelProperty("第几页，从1开始")
    private Integer page;
}
