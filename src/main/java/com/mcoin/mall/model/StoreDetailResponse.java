package com.mcoin.mall.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.mcoin.mall.bean.FookStoresType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.annotation.Generated;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@ApiModel(description = "門店詳情-响应体")
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Generated("com.robohorse.robopojogenerator")
public class StoreDetailResponse implements Serializable {
	@JsonProperty("id")
	@ApiModelProperty(value = "门店ID")
	private Integer id;
	@JsonProperty("name")
	@ApiModelProperty(value = "门店名称")
	private String name;
	@JsonProperty("background_img")
	@ApiModelProperty(value = "门店背景图片")
	private String backgroundImg;
	@JsonProperty("img")
	@ApiModelProperty(value = "门店图片")
	private String img;
	@JsonProperty("distance")
	@ApiModelProperty(value = "距离信息")
	private String distance;
	@JsonProperty("detail")
	@ApiModelProperty(value = "门店详情")
	private String detail;
	@JsonProperty("business_time")
	@ApiModelProperty(value = "营业时间")
	private String businessTime;
	@JsonProperty("address")
	@ApiModelProperty(value = "门店地址")
	private String address;
	@JsonProperty("phone")
	@ApiModelProperty(value = "门店联系电话")
	private String phone;
	@JsonProperty("branch")
	@ApiModelProperty(value = "分店信息")
	private Integer branch;
	@JsonProperty("business_id")
	@ApiModelProperty(value = "业务ID")
	private Integer businessId;
	@JsonProperty("type")
	@ApiModelProperty(value = "门店类型列表")
	private List<FookStoresType> type;
	@JsonProperty("provide_services")
	@ApiModelProperty(value = "提供的服务，默认为空字符串")
	private String provideServices = "";
	@JsonProperty("business")
	@ApiModelProperty(value = "业务项列表", reference = "StoreDetailResponseBusinessItem")
	private List<BusinessItem> business;
	@JsonProperty("businessinformation")
	@ApiModelProperty(value = "业务信息", reference = "StoreDetailResponseBusinessinformation")
	private Businessinformation businessinformation;
	@JsonProperty("keyword")
	@ApiModelProperty(value = "门店关键词列表", reference = "StoreDetailResponseStoresKeyword")
	private List<StoresKeyword> keyword;

	@Data
	@Generated("com.robohorse.robopojogenerator")
	@ApiModel(description = "门店类型", value = "StoreDetailResponseBusinessinformation")
	public static class Businessinformation implements Serializable {
		@JsonProperty("id")
		@ApiModelProperty(value = "业务信息ID")
		private Integer id;
		@JsonProperty("name")
		@ApiModelProperty(value = "业务信息名称")
		private String name;
	}

	@Data
	@ApiModel(description = "业务项", value = "StoreDetailResponseBusinessItem")
	@Generated("com.robohorse.robopojogenerator")
	public static class BusinessItem implements Serializable,Collectable {

		@JsonProperty("id")
		@ApiModelProperty(value = "业务项ID")
		private Integer id;
		@JsonProperty("title")
		@ApiModelProperty(value = "业务项标题")
		private String title;
		@JsonProperty("img")
		@ApiModelProperty(value = "业务项图片")
		private String img;
		@JsonProperty("retail_price")
		@ApiModelProperty(value = "零售价")
		private String retailPrice;
		@JsonProperty("point")
		@ApiModelProperty(value = "积分")
		private Integer point;
		@JsonProperty("preferential")
		@ApiModelProperty(value = "优惠金额")
		private BigDecimal preferential;
		@JsonProperty("pay_type")
		@ApiModelProperty(value = "支付类型")
		private Integer payType;

		/**
		 * 福利类型
		 * @see com.mcoin.mall.constant.BusinessProductTypeEnum
		 */
		@ApiModelProperty("福利类型:1-招牌產品,2-新優惠,3-現金券,4-1蚊優惠,5-積分換領,6-mCoin現金券,7-mPay满减券,8-mPay代金券," +
				"9-Mpay優惠券,10-交易核銷券,11-第三方兑换码,12-实物链路")
		private Integer type;

		/**
		 * 跳转链接
		 */
		@ApiModelProperty("跳转链接")
		private String hrefUrl;
		/**
		 * 是否收藏，0：未收藏；1-收藏
		 */
		@ApiModelProperty("是否收藏，0：未收藏；1-收藏")
		@JsonProperty("collect")
		private Integer collect;
		/**
		 * 收藏数量
		 */
		@ApiModelProperty("收藏数量")
		@JsonProperty("collect_count")
		private String collectCount;

		@ApiModelProperty("是否抢购: 0非抢购，1抢购")
		private Integer snapUp;

		@ApiModelProperty(value = "是否售罄", example = "true")
		@JsonProperty("sold_out")
		private boolean soldOut;


	}

	@Data
	@ApiModel(description = "门店关键词", value = "StoreDetailResponseStoresKeyword")
	@Generated("com.robohorse.robopojogenerator")
	public static class StoresKeyword implements Serializable{
		@ApiModelProperty(value = "关键词ID")
		private Integer id;

		@ApiModelProperty(value = "父关键词ID")
		private Integer pid;

		@ApiModelProperty(value = "关键词名称")
		private String name;

		@ApiModelProperty(value = "关键词英文名称")
		private String englishName;

		@ApiModelProperty(value = "关键词排序")
		private Integer sort;

		@ApiModelProperty(value = "关键词是否启用")
		private Integer enable;

		@ApiModelProperty(value = "关键词图标")
		private String icon;

		@ApiModelProperty(value = "是否在首页显示")
		private Integer ifIndexShow;

		@ApiModelProperty(value = "创建时间")
		private Date createdAt;

		@ApiModelProperty(value = "更新时间")
		private Date updatedAt;

		@ApiModelProperty(value = "资源链接")
		private String src;
	}

	@ApiModelProperty(value = "分页信息", reference = "PageV2")
	private PageV2 page;

}