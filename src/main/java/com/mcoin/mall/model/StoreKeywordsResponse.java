package com.mcoin.mall.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import javax.annotation.Generated;
import java.io.Serializable;
import java.util.List;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "著数分类-响应体")
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Generated("com.robohorse.robopojogenerator")
public class StoreKeywordsResponse implements Serializable {
    @JsonProperty("snatchList")
    @ApiModelProperty(value = "门店列表", notes = "包含多个门店的信息", reference = "StoreKeywordsResponseSnatchListItem")
    private List<SnatchListItem> snatchList;

    @Data
    @Generated("com.robohorse.robopojogenerator")
    @ApiModel(description = "门店信息项", value = "StoreKeywordsResponseSnatchListItem")
    public static class SnatchListItem implements Serializable {

        @ApiModelProperty(value = "门店ID", example = "1")
        private Integer id;
        @ApiModelProperty(value = "门店名称", example = "示例门店")
        private String name;
        @ApiModelProperty(value = "门店图片地址", example = "http://example.com/image.jpg")
        private String img;
        @ApiModelProperty(value = "门店排序", example = "1")
        private Integer sort;
        @ApiModelProperty(value = "门店来源", example = "source1")
        private String src;
        @ApiModelProperty(value = "门店来源类型（福利-1、門店-2、外部連結-3、小程序連結-4、專場廣告-5、门店关键字-6时src固定为classify、门店关键的类目id搜索-7时src固定为classify）", example = "type1")
        private String srcType;
        /** 目前分类在用,示例：1，2，3**/
        @ApiModelProperty(value = "扩展信息，目前分类在用", example = "1,2,3")
        private String extendInfo;

    }

}