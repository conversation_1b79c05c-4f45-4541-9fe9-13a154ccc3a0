package com.mcoin.mall.model;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.annotation.Generated;
import java.io.Serializable;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "門店分类-响应体")
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Generated("com.robohorse.robopojogenerator")
public class StoreTypeResponse implements Serializable {
    /**
     * 否则不显示"推荐"tab
     * true 开启，
     * false 关闭
     */
    @ApiModelProperty(value = "是否开启推荐，true为开启，false为关闭，否则不显示'推荐'tab", example = "true")
    @JsonProperty("recommend")
    private Boolean recommend;

    @Schema(description = "门店列表",  ref = "StoreTypeResponseSnatchListItem")
    @JsonProperty("snatchList")
    private List<SnatchListItem> snatchList;

    @Data
    @Generated("com.robohorse.robopojogenerator")
    @ApiModel(description = "门店信息", value = "StoreTypeResponseSnatchListItem")
    public static class SnatchListItem implements Serializable {

        @ApiModelProperty(value = "门店项ID", example = "1")
        @JsonProperty("id")
        private Integer id;

        @ApiModelProperty(value = "门店名称", example = "Apple Store")
        @JsonProperty("name")
        private String name;

        @ApiModelProperty(value = "门店图片链接", example = "https://example.com/image.jpg")
        @JsonProperty("img")
        private String img;

        @ApiModelProperty(value = "门店排序", example = "1")
        @JsonProperty("sort")
        private Integer sort;
    }


    @JsonProperty("shop_interval")
    @ApiModelProperty(value = "類別福利積分、金額區間")
    ShopIntervalResponse shopInterval;

}