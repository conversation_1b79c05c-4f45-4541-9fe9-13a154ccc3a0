package com.mcoin.mall.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@ApiModel(description = "多門店查詢-请求体")
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@EqualsAndHashCode(callSuper = true)
public class StoresRequest extends BaseDistanceRequest{

    @ApiModelProperty(name = "BusinessId", value = "业务ID", notes = "用于标识业务的唯一ID")
    public String BusinessId;
}
