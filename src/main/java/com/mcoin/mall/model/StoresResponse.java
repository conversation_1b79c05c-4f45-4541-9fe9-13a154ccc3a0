package com.mcoin.mall.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import javax.annotation.Generated;
import java.io.Serializable;
import java.util.List;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "多門店查詢-响应体")
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Generated("com.robohorse.robopojogenerator")
public class StoresResponse implements Serializable {
	@JsonProperty("snatchList")
    @ApiModelProperty(value = "门店列表", notes = "包含多个门店的信息列表", reference = "StoresResponseSnatchListItem")
	private List<SnatchListItem> snatchList;
    @Data
    @Generated("com.robohorse.robopojogenerator")
    @ApiModel(description = "门店信息", value = "StoresResponseSnatchListItem")
    public static class SnatchListItem implements Serializable {

        @JsonProperty("id")
        @ApiModelProperty(value = "门店ID", notes = "门店的唯一标识")
        private Integer id;
        @JsonProperty("name")
        @ApiModelProperty(value = "门店名称", notes = "门店的名称")
        private String name;
        @JsonProperty("address")
        @ApiModelProperty(value = "门店地址", notes = "门店的具体地址")
        private String address;
        @JsonProperty("phone")
        @ApiModelProperty(value = "门店联系电话", notes = "门店的联系电话号码")
        private String phone;
        @JsonProperty("distance")
        @ApiModelProperty(value = "距离", notes = "与当前位置的距离")
        private String distance;

    }

}