package com.mcoin.mall.model;

import javax.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "更新券码状态-请求体")
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class UpdateCouponCodeRequest {
    @NotNull
    @ApiModelProperty(value = "券码", required = true)
    private String code;

}
