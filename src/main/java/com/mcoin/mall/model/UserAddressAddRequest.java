package com.mcoin.mall.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "用户创建地址-请求体")
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class UserAddressAddRequest implements Serializable {
    @ApiModelProperty(value = "地址ID", example = "1")
    private Integer id;

    @ApiModelProperty(value = "联系人姓名", example = "张三")
    private String contact;

    @ApiModelProperty(value = "联系人手机号码", example = "13800138000")
    private String mobile;

    @ApiModelProperty(value = "省份ID", example = "110000")
    private Integer provinceId;

    @ApiModelProperty(value = "城市ID", example = "110100")
    private Integer cityId;

    @ApiModelProperty(value = "区县ID", example = "110101")
    private Integer districtId;

    @ApiModelProperty(value = "详细地址", example = "北京市东城区王府井大街1号")
    private String address;

    @ApiModelProperty(value = "纬度", example = "39.9042")
    private String latitude;

    @ApiModelProperty(value = "经度", example = "116.4074")
    private String longitude;

    @ApiModelProperty(value = "是否为默认地址，1是，0否", example = "1")
    private Integer isDefault;

    @ApiModelProperty(value = "省份名称", example = "北京市")
    private String province;

    @ApiModelProperty(value = "城市名称", example = "北京市")
    private String city;

    @ApiModelProperty(value = "区县名称", example = "东城区")
    private String district;

}
