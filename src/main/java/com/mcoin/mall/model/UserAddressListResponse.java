package com.mcoin.mall.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import javax.annotation.Generated;
import java.io.Serializable;
import java.util.List;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "用户地址详情-响应体")
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Generated("com.robohorse.robopojogenerator")
public class UserAddressListResponse implements Serializable {
    @JsonProperty("snatchList")
    @ApiModelProperty(value = "地址列表", dataType = "List<SnatchListItem>", notes = "包含多个地址信息的列表",
                        reference = "UserAddressListResponseSnatchListItem")
    private List<SnatchListItem> snatchList;

    @Data
    @Generated("com.robohorse.robopojogenerator")
    @ApiModel(description = "地址详情项", value = "UserAddressListResponseSnatchListItem")
    public static class SnatchListItem implements Serializable {

        @ApiModelProperty(value = "地址ID", dataType = "Integer", notes = "唯一标识地址的ID")
        private Integer id;

        @ApiModelProperty(value = "联系人姓名", dataType = "String", notes = "与该地址关联的联系人姓名")
        private String contact;

        @ApiModelProperty(value = "联系电话", dataType = "String", notes = "与该地址关联的联系电话")
        private String mobile;

        @ApiModelProperty(value = "省份ID", dataType = "Integer", notes = "该地址所在省份的ID")
        private Integer provinceId;

        @ApiModelProperty(value = "城市ID", dataType = "Integer", notes = "该地址所在城市的ID")
        private Integer cityId;

        @ApiModelProperty(value = "区域ID", dataType = "Integer", notes = "该地址所在区域的ID")
        private Integer districtId;

        @ApiModelProperty(value = "详细地址", dataType = "String", notes = "该地址的详细描述")
        private String address;

        @ApiModelProperty(value = "纬度", dataType = "String", notes = "该地址的纬度信息")
        private String latitude;

        @ApiModelProperty(value = "经度", dataType = "String", notes = "该地址的经度信息")
        private String longitude;

        @ApiModelProperty(value = "是否为默认地址", dataType = "Integer", notes = "标识该地址是否为默认地址，1表示是，0表示否")
        private Integer isDefault;

        @ApiModelProperty(value = "省份名称", dataType = "String", notes = "该地址所在省份的名称")
        private String province;

        @ApiModelProperty(value = "城市名称", dataType = "String", notes = "该地址所在城市的名称")
        private String city;

        @ApiModelProperty(value = "区域名称", dataType = "String", notes = "该地址所在区域的名称")
        private String district;

    }

}