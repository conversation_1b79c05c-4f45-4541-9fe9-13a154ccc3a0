package com.mcoin.mall.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "用户地址-请求体")
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class UserAddressLocationRequest {

    /**
     * 地址id
     */
    @ApiModelProperty(value = "地址id", required = true)
    @NotBlank
    private String location;

}
