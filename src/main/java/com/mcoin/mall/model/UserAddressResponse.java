package com.mcoin.mall.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import javax.annotation.Generated;
import java.io.Serializable;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "用户地址详情-响应体")
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Generated("com.robohorse.robopojogenerator")
public class UserAddressResponse implements Serializable {
    @ApiModelProperty(value = "用户地址ID")
    private String id;

    @ApiModelProperty(value = "联系人姓名")
    private String contact;

    @ApiModelProperty(value = "联系人手机号码")
    private String mobile;

    @ApiModelProperty(value = "省份ID")
    private Integer provinceId;

    @ApiModelProperty(value = "城市ID")
    private Integer cityId;

    @ApiModelProperty(value = "区县ID")
    private Integer districtId;

    @ApiModelProperty(value = "详细地址")
    private String address;

    @ApiModelProperty(value = "纬度")
    private String latitude;

    @ApiModelProperty(value = "经度")
    private String longitude;

    @ApiModelProperty(value = "是否为默认地址，1是，0否")
    private Integer isDefault;

    @ApiModelProperty(value = "省份名称")
    private String province;

    @ApiModelProperty(value = "城市名称")
    private String city;

    @ApiModelProperty(value = "区县名称")
    private String district;

}