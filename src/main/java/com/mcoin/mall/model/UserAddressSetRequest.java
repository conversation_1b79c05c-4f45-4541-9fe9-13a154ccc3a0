package com.mcoin.mall.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import javax.validation.constraints.NotNull;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "用户设置默认地址-请求体")
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class UserAddressSetRequest {

    @NotNull
    @ApiModelProperty(value = "用户地址ID", required = true)
    private Integer id;

}
