package com.mcoin.mall.model;

import java.io.Serializable;

import javax.annotation.Generated;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(description = "获取用户昵称和头像-响应体")
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Generated("com.robohorse.robopojogenerator")
public class UserProfileResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 用户昵称
     */
    @ApiModelProperty(value = "用户昵称", example = "Car")
    @JsonProperty("nickname")
    private String nickname;

    /**
     * 用户头像URL
     */
    @ApiModelProperty(value = "用户头像URL", example = "http://xxxx")
    @JsonProperty("headimgurl")
    private String headimgurl;
} 