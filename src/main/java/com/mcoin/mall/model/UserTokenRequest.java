package com.mcoin.mall.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import javax.validation.constraints.NotNull;

@ApiModel(description = "网关请求更新用户Token-请求体")
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public  class UserTokenRequest {
        @NotNull
        public String code;
        public String getCode() { return code; }
        public void setCode(String code) { this.code = code; }
}