package com.mcoin.mall.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import javax.annotation.Generated;
import java.io.Serializable;
import java.util.List;

import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "專區類型-响应体")
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Generated("com.robohorse.robopojogenerator")
public class ZoneTypeResponse implements Serializable {
	@ApiModelProperty(value = "专区ID", example = "123456")
	@JsonProperty("zone_id")
	private String zoneId;

	@ApiModelProperty(value = "专区名称", example = "热门专区")
	@JsonProperty("name")
	private String name;

	@ApiModelProperty(value = "专区類型：1、 mcoin  2、h5", example = "商品专区")
	@JsonProperty("type")
	private String type;

	@ApiModelProperty(value = "专区状态：0、 下架  1、上架", example = "0")
	@JsonProperty("status")
	private String status;
	/**
	 * 商品列表
	 */
	@JsonProperty("products")
	@ApiModelProperty(value = "商品列表")
	List<RecommendZoneResponse> products;
}