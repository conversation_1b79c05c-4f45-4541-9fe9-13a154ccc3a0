package com.mcoin.mall.model.api;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@ApiModel(description = "钟意某个福利-请求体")
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class ApiCollectionRequest  implements ApiBaseRequest{
    @NotBlank
    private String custId;
    @NotBlank
    private String signature;
    @NotNull
    private Integer goodsId;
    @NotNull
    private Integer type;
    @NotNull
    private Integer isCollect;


}
