package com.mcoin.mall.model.api;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import lombok.Data;

@ApiModel(description = "钟意某个福利-响应体")
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class ApiCollectionResponse {

    @JsonProperty("collect_count")
    private String collectCount;

    private Integer collect;

}
