package com.mcoin.mall.model.api;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Mpay核销-响应体")
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class MpayRedeemVoucherResponse implements Serializable,Logable {
    @ApiModelProperty(value = "响应数据", required = false, reference = "MpayRedeemVoucherResponseDdata")
    private Ddata data;
    @ApiModelProperty(value = "响应代码", required = false)
    private Integer rspcod;
    @ApiModelProperty(value = "响应消息", required = false)
    private String rspmsg;

    @Data
    @NoArgsConstructor
    @ApiModel(description = "Mpay核销-响应数据", value = "MpayRedeemVoucherResponseDdata")
    public static class Ddata {
        @ApiModelProperty(value = "结果代码", required = false)
        private String resultcode;
        @ApiModelProperty(value = "结果消息", required = false)
        private String resultmsg;
    }

}
