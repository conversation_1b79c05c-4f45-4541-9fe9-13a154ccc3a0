package com.mcoin.mall.model.api;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "核销-响应体")
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class RedeemVoucherResponse {
    @ApiModelProperty(value = "核销时间", example = "2025-03-13 10:27:20")
    private String redeemTime;
    @ApiModelProperty(value = "代金券状态", example = "1")
    private Integer voucherStatus;
    @ApiModelProperty(value = "代金券名称", example = "满减券")
    private String voucherName;
    @ApiModelProperty(value = "代金券类型", example = "满减")
    private String voucherType;
    @ApiModelProperty(value = "代金券代码", example = "VC001")
    private String voucherCode;
    @ApiModelProperty(value = "代金券原始单价", example = "100.00")
    private String voucherOriUnitPrice;
    @ApiModelProperty(value = "代金券单价", example = "80.00")
    private String voucherUnitPrice;
    @ApiModelProperty(value = "优惠金额", example = "20.00")
    private String preferentialAmount;
    @ApiModelProperty(value = "请求订单号", example = "20250313102720001")
    private String requestOrderNo;
    @JsonProperty("trans_coupon_type")
    @ApiModelProperty(value = "交易优惠券类型", example = "1")
    private Integer transCouponType;
    @ApiModelProperty(value = "代金券数量", example = "1")
    private String voucherNum;
    @ApiModelProperty(value = "终端代码", example = "T001")
    private String terminalCode;
    @ApiModelProperty(value = "分支机构代码", example = "B001")
    private String branchCode;
    @ApiModelProperty(value = "商户代码", example = "M001")
    private String merchantCode;
    @ApiModelProperty(value = "店铺联系电话", example = "13800138000")
    private String storeMobile;
    @ApiModelProperty(value = "地区代码", example = "110000")
    private String areaCode;
    @ApiModelProperty(value = "外部参考号", example = "EXT001")
    private String externalRefNo;
    @ApiModelProperty(value = "元数据信息", reference = "RedeemVoucherResponseMeta")
    private Meta meta;
    @ApiModelProperty(value = "回调消息", example = "核销成功")
    private String callbackMsg;
    @ApiModelProperty(value = "店铺ID", example = "1")
    private Integer storeId;
    @ApiModelProperty(value = "卖家ID", example = "1")
    private Integer sellerId;
    @ApiModelProperty(value = "业务信息ID", example = "1")
    private Integer businessInformationId;
    @ApiModelProperty(value = "店铺列表", reference = "RedeemVoucherResponseStore")
    private List<Store> stores;

    @Data
    @NoArgsConstructor
    @ApiModel(description = "元数据信息", value = "RedeemVoucherResponseMeta")
    public static class Meta {
        @ApiModelProperty(value = "元数据类型", example = "type1")
        private String type;
    }

    @Data
    @NoArgsConstructor
    @ApiModel(description = "店铺信息", value = "RedeemVoucherResponseStore")
    public static class Store {
        @ApiModelProperty(value = "店铺ID", example = "1")
        private Integer storeId;
        @ApiModelProperty(value = "分支机构代码", example = "B001")
        private String branchCode;
        @ApiModelProperty(value = "卖家ID", example = "1")
        private Integer sellerId;
        @ApiModelProperty(value = "商户代码", example = "M001")
        private String merchantCode;
        @ApiModelProperty(value = "业务信息ID", example = "1")
        private Integer businessInformationId;
    }
}
