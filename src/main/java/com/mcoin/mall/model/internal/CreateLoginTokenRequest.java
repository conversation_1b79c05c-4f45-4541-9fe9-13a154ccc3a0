package com.mcoin.mall.model.internal;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@ApiModel(description = "创建登陆Token-请求体")
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class CreateLoginTokenRequest {

    @ApiModelProperty(value = "mPay用户ID", required = true, example = "00000102287208")
    @NotNull
    @NotBlank
    private String custId;

}
