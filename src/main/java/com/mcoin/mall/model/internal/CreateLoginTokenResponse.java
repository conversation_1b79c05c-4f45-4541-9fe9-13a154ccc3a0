package com.mcoin.mall.model.internal;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(description = "创建登陆Token-响应体")
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class CreateLoginTokenResponse {


    @ApiModelProperty(value = "JWT Token", required = true)
    private String token;

    @ApiModelProperty(value = "过期时间，单位秒", required = true, example = "7200")
    private Integer expire;
}
