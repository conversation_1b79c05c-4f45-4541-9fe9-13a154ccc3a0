package com.mcoin.mall.model.job;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.mcoin.mall.mq.model.MiniOrderSyncMessage;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@ApiModel(description = "同步小程序订单-请求体")
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class MiniOrderSyncRequest {

    @NotNull
    @Valid
    private MiniOrderSyncMessage message;

}
