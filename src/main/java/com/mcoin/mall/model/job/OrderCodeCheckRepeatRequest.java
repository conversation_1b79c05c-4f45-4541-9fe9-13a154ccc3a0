package com.mcoin.mall.model.job;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.mcoin.mall.util.JodaTimeUtil;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@ApiModel(description = "定时任务-檢測一次券碼是否有重複-请求体")
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class OrderCodeCheckRepeatRequest {

    @DateTimeFormat(pattern = JodaTimeUtil.DEFAULT_PATTERN)
    private Date currentTime;
}
