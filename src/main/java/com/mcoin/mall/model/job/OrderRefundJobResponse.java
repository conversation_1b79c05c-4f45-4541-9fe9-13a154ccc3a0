package com.mcoin.mall.model.job;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import javax.annotation.Generated;
import java.io.Serializable;

@ApiModel(description = "自动审批退款-响应体")
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Generated("com.robohorse.robopojogenerator")
public class OrderRefundJobResponse implements Serializable {

	private Integer status;
	private Integer code;

}