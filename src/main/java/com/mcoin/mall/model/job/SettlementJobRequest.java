package com.mcoin.mall.model.job;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.mcoin.mall.util.JodaTimeUtil;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

@ApiModel(description = "结算-请求体")
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class SettlementJobRequest implements Serializable {
    @NotNull
    @DateTimeFormat(pattern = JodaTimeUtil.YYYY_MM_DD)
    private Date currentDate;
}