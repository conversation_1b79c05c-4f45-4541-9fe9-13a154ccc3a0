package com.mcoin.mall.model.job;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.mcoin.mall.util.JodaTimeUtil;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

@ApiModel(description = "发送结算报表-请求体")
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class SettlementOptionalReportSendJobRequest implements Serializable {
    @NotNull
    @DateTimeFormat(pattern = JodaTimeUtil.DEFAULT_PATTERN)
    private Date currentTime;
}