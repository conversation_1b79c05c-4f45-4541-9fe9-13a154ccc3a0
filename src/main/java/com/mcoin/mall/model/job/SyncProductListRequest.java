package com.mcoin.mall.model.job;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.mcoin.mall.util.JodaTimeUtil;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.util.Date;

@ApiModel(description = "同步库存和销量-请求体")
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class SyncProductListRequest {
    @NotNull
    @DateTimeFormat(pattern = JodaTimeUtil.DEFAULT_PATTERN)
    private Date startTime;

    @NotNull
    @DateTimeFormat(pattern = JodaTimeUtil.DEFAULT_PATTERN)
    private Date endTime;
}
