package com.mcoin.mall.model.job;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.mcoin.mall.util.JodaTimeUtil;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.util.Date;

@ApiModel(description = "定时任务-创建交易统计文件-请求体")
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class TradingDataCreateFileRequest {
    @NotNull
    @DateTimeFormat(pattern = JodaTimeUtil.YYYY_MM_DD)
    private Date currentDate;
}
