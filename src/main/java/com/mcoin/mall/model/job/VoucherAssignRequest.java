package com.mcoin.mall.model.job;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.mcoin.mall.util.JodaTimeUtil;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.util.Date;

@ApiModel(description = "每分鐘執行一次派券任務-请求体")
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class VoucherAssignRequest {
    @NotNull
    @DateTimeFormat(pattern = JodaTimeUtil.DEFAULT_PATTERN)
    private Date currentTime;
}
