package com.mcoin.mall.model.management;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

@ApiModel(description = "场次信息同步-请求体")
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class SyncSessionInfoRequest {
    @NotNull
    @Min(1)
    @ApiModelProperty(value = "场次ID", required = true)
    private Integer sessionId;

    @NotNull
    @ApiModelProperty(value = "动作：EDIT_SESSION\n" +
            "EDIT_SESSION_PRODUCT\n" +
            "CLOSE_SESSION\n" +
            "OPEN_SESSION\n" +
            "DELETE_SESSION", required = true)
    private String action;
}
