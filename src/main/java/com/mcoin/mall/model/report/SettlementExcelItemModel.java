package com.mcoin.mall.model.report;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Data
@Accessors(chain = true)
public class SettlementExcelItemModel {
    private Integer sn;
    private String type;
    private String orderNo;
    private String orderTransaction;
    private String voucherCode;
    private BigDecimal billAmount;
    private BigDecimal commission;
    private BigDecimal merchantSettleAmount;
    private String useTime;
    private String storeName;
    private String voucherName;
    private String deliveryType;
    private String productPrice;
    private String startTime;
    private String endTime;
    private String storeMid;
    private String trackingNo;
    private String businessRedeemTime;
}
