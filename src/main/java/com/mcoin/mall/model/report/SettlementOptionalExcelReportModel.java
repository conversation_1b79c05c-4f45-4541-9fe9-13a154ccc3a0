package com.mcoin.mall.model.report;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
public class SettlementOptionalExcelReportModel {
    private String code;
    private String businessName;
    private String currentDate;
    private String headEmail;
    private String startTime;
    private String endTime;
    private List<SettlementExcelItemModel> dataList;

}
