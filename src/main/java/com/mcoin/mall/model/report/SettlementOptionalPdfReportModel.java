package com.mcoin.mall.model.report;

import com.mcoin.mall.bo.SettlementReportDetailBo;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
@Accessors(chain = true)
public class SettlementOptionalPdfReportModel {
    private String code;
    private String businessName;
    private Date currentDate;
    private String headEmail;
    private Date startTime;
    private Date endTime;
    private Integer pageCountStart;
    private BigDecimal billAmountSum;
    private BigDecimal commissionSum;
    private BigDecimal merchantSettleAmountSum;
    private Integer isLastPage;
    private List<SettlementReportDetailBo> dataList;

}
