package com.mcoin.mall.service.amap.impl;

import static com.mcoin.mall.util.SentinelUtils.handleBlockException;

import java.util.Map;

import javax.annotation.Resource;

import com.github.houbb.opencc4j.util.ZhConverterUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.mcoin.mall.client.AmapClient;
import com.mcoin.mall.exception.BusinessException;
import com.mcoin.mall.model.Response;
import com.mcoin.mall.service.amap.AmapService;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2024/10/28
 */
@Slf4j
@Service
public class AmapServiceImpl implements AmapService {

    @Value("${map.tencent_map_key}")
    private String mapTencentMapkey;

    @Resource
    private AmapClient amapClient;

    @Override
    public Map<String, Object> geocoderCoordinate(String location) {
        if (StringUtils.isBlank(mapTencentMapkey)) {
            throw new BusinessException(Response.Code.UNKNOWN_ERROR, "system error");
        }
        String decodeLocation = handleBlockException(()->amapClient.getLocation(location, mapTencentMapkey), null);
        if (StringUtils.isBlank(location)) {
            throw new BusinessException(Response.Code.UNKNOWN_ERROR, "system error");
        }
        log.info("高德逆解析地址:{}", decodeLocation);
        return JSON.parseObject(ZhConverterUtil.toTraditional(decodeLocation), new TypeReference<Map<String, Object>>() {});
    }
}
