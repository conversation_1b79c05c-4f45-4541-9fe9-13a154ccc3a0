package com.mcoin.mall.service.auth;

import java.util.Map;

import com.mcoin.mall.model.ConfigMPayJSApiResponse;
import com.mcoin.mall.model.CouponLoginRequest;
import com.mcoin.mall.model.CouponLoginResponse;
import com.mcoin.mall.model.GetTokenRequest;
import com.mcoin.mall.model.GetTokenResponse;
import com.mcoin.mall.model.GetUserInfoResponse;
import com.mcoin.mall.model.RefreshTokenResponse;
import com.mcoin.mall.model.UserProfileResponse;

public interface MPayAuthService {

	String getRedirectUrl(String scope, String state, String source, String oauthParams, Map<String,String> paramsMap);
	ConfigMPayJSApiResponse saveAndGetJSApi(String url);
	GetUserInfoResponse updAndGetUserInfo();
	RefreshTokenResponse refreshToken();
	GetTokenResponse saveAndGetToken(GetTokenRequest request);
	CouponLoginResponse couponLogin(CouponLoginRequest request);
	UserProfileResponse updAndGetUserProfile();
}
