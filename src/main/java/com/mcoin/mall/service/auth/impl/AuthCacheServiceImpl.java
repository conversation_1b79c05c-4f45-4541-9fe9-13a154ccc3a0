package com.mcoin.mall.service.auth.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.mcoin.mall.bean.FookMacaupassToken;
import com.mcoin.mall.client.MPayClient;
import com.mcoin.mall.client.model.MPayAppTokenResponse;
import com.mcoin.mall.client.model.MPayTicketResponse;
import com.mcoin.mall.dao.FookMacaupassTokenDao;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.Date;

import static com.mcoin.mall.service.common.CacheService.CACHE_APP_TOKEN;
import static com.mcoin.mall.util.SentinelUtils.handleBlockException;

@Slf4j
@Service
public class AuthCacheServiceImpl {

	@Autowired
	private FookMacaupassTokenDao fookMacaupassTokenDao;
    @Autowired
    private MPayClient mPayClient;
    private final static String fookMacaupassAppToken = "apptoken";//数据库中固定的token类型名
    private final static String fookMacaupassTicket = "ticket";//数据库中固定的ticket类型名
	
	/***
	 * 获取macaupassTicket-java版
	 * @return
	 */
	@Cacheable(value = CACHE_APP_TOKEN, key = "@cacheService.CACHE_DEFAULT_KEY", unless = "#result == null")
	public String saveAndGetMacaupassTicket(boolean isRend) {
		//获取token
		log.info("请求mpay获取appToken");
		String appTokenResp = handleBlockException(()->mPayClient.getAppToken(), null);
		if(StringUtils.isEmpty(appTokenResp)) {
			log.info("请求mpay获取appToken失败");
			return null;
		}
		log.info("获取appToken成功，响应信息：{}",appTokenResp);
		MPayAppTokenResponse mPayAppTokenResponse = JSONObject.parseObject(appTokenResp,MPayAppTokenResponse.class);
		String appToken = mPayAppTokenResponse.getAppToken();
		String tokenExpiresIn = mPayAppTokenResponse.getExpiresIn()+"";
		
        //获取ticket
		log.info("请求mpay获取appTicket");
		String ticketResp = handleBlockException(()->mPayClient.getTicket(appToken), null);
		if(StringUtils.isEmpty(ticketResp)) {
			log.info("请求mpay获取ticket失败");
			return null;
		}
		log.info("获取ticket成功，响应信息：{}",ticketResp);
		MPayTicketResponse mPayTicketResponse = JSONObject.parseObject(ticketResp,MPayTicketResponse.class);
		Integer errorCode = Integer.parseInt(mPayTicketResponse.getErrcode());
		if(errorCode == 98 && isRend) {
			log.error("========获取jsticket失敗,失敗原因[{}],重新獲取app_token",mPayTicketResponse.getErrmsg());
			return saveAndGetMacaupassTicket(false);
		}
		if(errorCode!=0) {
			log.info("请求mpay获取ticket失败,错误码:{}",errorCode);
			return null;
		}
		String appTicket = mPayTicketResponse.getTicket();
		String ticketExpiresIn = mPayTicketResponse.getExpiresIn()+"";
		
		//处理token和ticket表数据
		processAppToken(fookMacaupassAppToken, appToken, tokenExpiresIn);
		processAppToken(fookMacaupassTicket, appTicket, ticketExpiresIn);
		return appTicket;
	}
	
	/***
	 * 获取macaupassTicket-兼容php原逻辑
	 * @return
	 */
	public String saveAndGetMacaupassTicketPhp() {
		
		FookMacaupassToken macaupassToken = fookMacaupassTokenDao.selectOne(new LambdaQueryWrapper<FookMacaupassToken>()
        		.eq(FookMacaupassToken::getType, fookMacaupassAppToken)
				.last("limit 1")
		);
		String appToken = "";
		if(macaupassToken==null) {
			//获取token
			log.info("请求mpay获取appToken");
			String appTokenResp = handleBlockException(()->mPayClient.getAppToken(), null);
			if(StringUtils.isEmpty(appTokenResp)) {
				log.info("请求mpay获取appToken失败");
				return null;
			}
			log.info("获取appToken成功，响应信息：{}",appTokenResp);
			MPayAppTokenResponse mPayAppTokenResponse = JSONObject.parseObject(appTokenResp,MPayAppTokenResponse.class);
			appToken = mPayAppTokenResponse.getAppToken();
			if(StringUtils.isEmpty(appToken) || mPayAppTokenResponse.getExpiresIn() == null) {
				log.info("获取appToken或expiresin失败");
				return null;
			}
			//处理token
			processAppToken(fookMacaupassAppToken, appToken, mPayAppTokenResponse.getExpiresIn()+"");
		}else {
			appToken = macaupassToken.getToken();
			log.info("已有token数据:{}，直接获取", appToken);
		}
		
		FookMacaupassToken macaupassTicket = fookMacaupassTokenDao.selectOne(new LambdaQueryWrapper<FookMacaupassToken>()
        		.eq(FookMacaupassToken::getType, fookMacaupassTicket)
				.last("limit 1")
		);
		
		String appTicket = "";
		if(macaupassTicket==null){
			//获取ticket
			log.info("请求mpay获取appTicket");
			String finalAppToken = appToken;
			String ticketResp = handleBlockException(()->mPayClient.getTicket(finalAppToken), null);
			if(StringUtils.isEmpty(ticketResp)) {
				log.info("请求mpay获取ticket失败");
				return null;
			}
			log.info("获取ticket成功，响应信息：{}",ticketResp);
			MPayTicketResponse mPayTicketResponse = JSONObject.parseObject(ticketResp,MPayTicketResponse.class);
			appTicket = mPayTicketResponse.getTicket();
			if(StringUtils.isEmpty(appTicket) || mPayTicketResponse.getExpiresIn() == null) {
				log.info("获取ticket或expiresin失败");
				return null;
			}
			//处理token和ticket表数据
			processAppToken(fookMacaupassTicket, appTicket, mPayTicketResponse.getExpiresIn()+"");
		}else {
			appTicket = macaupassTicket.getToken();
			log.info("已有ticket数据:{}，直接获取", appTicket);
		}
		return appTicket;
	}
	
	/***
	 * appToken或ticket更新到库中
	 * @param type apptoken或者 ticket
	 * @param apptoken
	 * @param expiresIn
	 */
	public void processAppToken(String type, String apptoken, String expiresIn) {
		if(StringUtils.isEmpty(apptoken) || StringUtils.isEmpty(expiresIn)) {
			log.info("token/ticket或过期时间为空，直接返回，不更新数据");
			return;
		}
		FookMacaupassToken macaupassToken = fookMacaupassTokenDao.selectOne(new LambdaQueryWrapper<FookMacaupassToken>()
        		.eq(FookMacaupassToken::getType, type)
				.last("limit 1")
		);
		//更新token
		if(macaupassToken!=null) {
			macaupassToken.setToken(apptoken);
			macaupassToken.setCreateTime(new Date());
			macaupassToken.setExpiresIn(expiresIn);
			fookMacaupassTokenDao.update(macaupassToken, 
					new LambdaQueryWrapper<FookMacaupassToken>().eq(FookMacaupassToken::getType, type));
			log.info("更新{}=[{}]成功",type,apptoken);
		}else {
			FookMacaupassToken newToken = new FookMacaupassToken();
			newToken.setType(type);
			newToken.setToken(apptoken);
			newToken.setCreateTime(new Date());
			newToken.setExpiresIn(expiresIn);
			fookMacaupassTokenDao.insert(newToken);
			log.info("新增{}=[{}]成功",type,apptoken);
		}
	}
}
