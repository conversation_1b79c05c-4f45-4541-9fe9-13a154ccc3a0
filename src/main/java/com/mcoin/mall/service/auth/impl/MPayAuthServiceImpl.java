package com.mcoin.mall.service.auth.impl;

import static com.mcoin.mall.util.McoinMall.NOT_LOGIN;
import static com.mcoin.mall.util.SentinelUtils.handleBlockException;
import static org.apache.commons.lang3.StringUtils.defaultIfBlank;

import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;

import javax.annotation.Resource;

import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Maps;
import com.mcoin.mall.bean.FookMacaupassUser;
import com.mcoin.mall.bean.FookMacaupassUserLoginLog2;
import com.mcoin.mall.bean.FookPlatformOrder;
import com.mcoin.mall.bean.FookPlatformUserinfo;
import com.mcoin.mall.client.MPayClient;
import com.mcoin.mall.client.model.MPayAccessTokenResponse;
import com.mcoin.mall.client.model.MPayRefreshTokenResponse;
import com.mcoin.mall.client.model.MPayUserInfoResponse;
import com.mcoin.mall.component.ContextHolder;
import com.mcoin.mall.constant.YesNo;
import com.mcoin.mall.dao.FookMacaupassUserDao;
import com.mcoin.mall.dao.FookMacaupassUserLoginLog2Dao;
import com.mcoin.mall.dao.FookPlatformOrderDao;
import com.mcoin.mall.dao.FookPlatformOrdercodeDao;
import com.mcoin.mall.dao.FookPlatformUserinfoDao;
import com.mcoin.mall.dao.SettingsDao;
import com.mcoin.mall.exception.BusinessException;
import com.mcoin.mall.exception.RetryException;
import com.mcoin.mall.model.ConfigMPayJSApiResponse;
import com.mcoin.mall.model.CouponLoginRequest;
import com.mcoin.mall.model.CouponLoginResponse;
import com.mcoin.mall.model.GetTokenRequest;
import com.mcoin.mall.model.GetTokenResponse;
import com.mcoin.mall.model.GetUserInfoResponse;
import com.mcoin.mall.model.RefreshTokenResponse;
import com.mcoin.mall.model.Response;
import com.mcoin.mall.model.UserProfileResponse;
import com.mcoin.mall.security.TokenManager;
import com.mcoin.mall.security.UserInfo;
import com.mcoin.mall.service.auth.MPayAuthService;
import com.mcoin.mall.service.chennel.McoinChannelService;
import com.mcoin.mall.util.ConfigUtils;
import com.mcoin.mall.util.GrayUtils;
import com.mcoin.mall.util.JodaTimeUtil;
import com.mcoin.mall.util.StrUtil;

import cn.hutool.http.HttpUtil;
import feign.FeignException;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class MPayAuthServiceImpl implements MPayAuthService {

    @Autowired
    private FookMacaupassUserDao fookMacaupassUserDao;
    @Autowired
    private FookPlatformUserinfoDao fookPlatformUserinfoDao;
    @Autowired
    private FookMacaupassUserLoginLog2Dao fookMacaupassUserLoginLog2Dao;
    @Autowired
    private FookPlatformOrdercodeDao fookPlatformOrdercodeDao;
    @Autowired
    private FookPlatformOrderDao fookPlatformOrderDao;
    @Autowired
    private AuthCacheServiceImpl authCacheServiceImpl;
    @Autowired
    private MPayClient mPayClient;
    @Autowired
    private TokenManager tokenManager;
    @Resource
    private ContextHolder contextHolder;
    @Resource
    private SettingsDao settingsDao;
    @Resource
    private McoinChannelService mcoinChannelService;

    /** 默认加密密码(MD5加密后的"xgfzsl") */
    private static final String DEFAULT_ENCRYPTED_PASSWORD = "4QrcOUm6Wau+VuBX8g+IPg==";
    /** 随机用户昵称数字最小值(6位数) */
    private static final int NICKNAME_RANDOM_MIN = 100000;
    /** 随机用户昵称数字范围 */
    private static final int NICKNAME_RANDOM_RANGE = 900000;

    @Override
    public String getRedirectUrl(String scope, String state, String source, String oauthParams, Map<String, String> paramsMap) {
        Map<String, String> utmParamsMap = getUtmMap(paramsMap);
        StringBuilder url = new StringBuilder();
        String gray = ConfigUtils.getProperty("mcoin.gray.switch", GrayUtils.CLOSED);
        String redirectUri = ConfigUtils.getProperty("macaupass.REDIRECT_URI");
        String gV1 = paramsMap.get("g_v1");
        if ((GrayUtils.GRAYING.equals(gray) && "true".equals(gV1))
                || GrayUtils.ALL.equals(gray)) {
            redirectUri = ConfigUtils.getProperty("macaupass.REDIRECT_URI_V2");
        }

        Map<String, Object> params = Maps.newHashMap();
        if (StringUtils.isNotEmpty(source)) {
            params.put("source", source);
        }
        if (StringUtils.isNotBlank(oauthParams)) {
            params.put("oauthParams", oauthParams);
        }
        params.putAll(utmParamsMap);
        redirectUri = HttpUtil.urlWithForm(redirectUri, params, StandardCharsets.UTF_8, false);
        url.append(ConfigUtils.getProperty("macaupass.URL"))
                .append("/oauth2/authorize?appid=").append(ConfigUtils.getProperty("macaupass.APPID"))
                .append("&redirect_uri=").append(StrUtil.encodeURIComponent(redirectUri))
                .append("&response_type=code&scope=").append(scope)
                .append("&state=").append(state);
        return url.toString();
    }

    @Override
    public ConfigMPayJSApiResponse saveAndGetJSApi(String url) {
        log.info("获取jsApi开始，请求的url：{}", url);
        ConfigMPayJSApiResponse response = new ConfigMPayJSApiResponse();
        long timestamp = System.currentTimeMillis();
        String str = StrUtil.generateCode(32, -1);
        //获取ticket-暂时先使用兼容php项目的获取ticket方式（需要php定时任务刷新支持），后续废弃php后可改为调用saveAndGetMacaupassTicket(true)方法，使用缓存处理ticket
        String appTicket = authCacheServiceImpl.saveAndGetMacaupassTicketPhp();
        response.setDebug(1);
        response.setAppId(ConfigUtils.getProperty("macaupass.APPID"));
        response.setTimestamp(timestamp);
        String nonceStr = "jsapi_ticket=" + appTicket + "&nonceStr=" + str + "&timestamp=" + timestamp + "&url=" + url;
        response.setNonceStr(str);
        //签名
//        String signature = StrUtil.sha1(nonceStr, "utf-8").toString();
        String signature = DigestUtils.sha1Hex(nonceStr);
        response.setSignature(signature);
        List<String> jsApiList = new ArrayList<>();
        jsApiList.add("checkJsApi");
        jsApiList.add("getLocation");
        jsApiList.add("openLocation");
        jsApiList.add("getNetworkType");
        jsApiList.add("ctrlNavBar");
        jsApiList.add("closeWindow");
        jsApiList.add("scanQRCode");
        jsApiList.add("payCode");
        jsApiList.add("choosePayMent");
        jsApiList.add("clearCache");
        jsApiList.add("callPhone");
        jsApiList.add("goBack");
        jsApiList.add("getTakePhoto");
        jsApiList.add("modifyImage");
        jsApiList.add("brightnessSettings");
        jsApiList.add("mpCardAutoChargeActive");
        jsApiList.add("thirdShare");
        jsApiList.add("setupStatusBarBgColor");//該方法只针对IOS端 且當ctrlNavBar的ctrlType為1時生效
        jsApiList.add("openNativePage");
        jsApiList.add("pushWindow");
        jsApiList.add("openTinyApp");
        jsApiList.add("getSystemInfo");
        response.setJsApiList(jsApiList);

        log.info("获取jsApi成功，耗时:{}毫秒，返回:{}", System.currentTimeMillis() - timestamp, JSON.toJSONString(response));
        return response;
    }

    @Override
    public GetTokenResponse saveAndGetToken(GetTokenRequest request) {
        long startTime = System.currentTimeMillis();
        String code = request.getCode();
        String state = request.getState();
        if (StringUtils.isEmpty(code)) {
            log.error("授权回调的url未返回code，触发用户重新登录");
            throw new BusinessException(Response.Code.UNAUTHORIZED, NOT_LOGIN);
        }
        //请求mpay获取用户accesstoken
        String accessTokenResp = handleBlockException(()->mPayClient.accessToken(code), null);
        if (StringUtils.isEmpty(accessTokenResp)) {
            log.error("使用code获取access_token请求失败，触发用户重新登录");
            throw new BusinessException(Response.Code.UNAUTHORIZED, NOT_LOGIN);
        }
        MPayAccessTokenResponse mPayAccessTokenResponse = JSON.parseObject(accessTokenResp, MPayAccessTokenResponse.class);
        String accessToken = mPayAccessTokenResponse.getAccessToken();
        String refreshToken = mPayAccessTokenResponse.getRefreshToken();
        String openid = mPayAccessTokenResponse.getOpenid();
        if (StringUtils.isEmpty(accessToken) || StringUtils.isEmpty(openid)) {
            log.info("使用code获取access_token或openid失败，触发用户重新登录");
            throw new BusinessException(Response.Code.UNAUTHORIZED, NOT_LOGIN);
        }
        String userinfoGet = ConfigUtils.getProperty("macaupassave.userinfo.get", String.valueOf(YesNo.YES.getValue()));
        MPayUserInfoResponse mPayUserInfoResponse = null;
        if("1".equals(userinfoGet)) {
            //请求mpay获取用户信息
            String userInfoResp = handleBlockException(()->mPayClient.getUserInfo(accessToken, openid), null);
            log.info("使用access_token获取用户信息响应内容：{}", userInfoResp);
            if (StringUtils.isEmpty(userInfoResp)) {
                log.error("使用access_token获取用户信息请求失败，触发用户重新登录");
                throw new BusinessException(Response.Code.UNAUTHORIZED, NOT_LOGIN);
            }
            mPayUserInfoResponse = JSON.parseObject(userInfoResp, MPayUserInfoResponse.class);
        }

        //查询用户是否已存在
        FookMacaupassUser userInfo = fookMacaupassUserDao.selectOne(new LambdaQueryWrapper<FookMacaupassUser>()
                .eq(FookMacaupassUser::getOpenid, openid)
                .last("limit 1")
        );
        String jwtToken;
        Integer userId;
        Date date = new Date();
        if (userInfo == null) {

            String custId = getCustId(openid);

            //创建用户信息-PlatformUser
            FookPlatformUserinfo platformUserinfo = new FookPlatformUserinfo();
            platformUserinfo.setPassword(DEFAULT_ENCRYPTED_PASSWORD);//TODO:确认过密码目前不会用到，所以此处暂时写死固定密码"xgfzsl"md5加密后的字符串
            int num = new Random().nextInt(NICKNAME_RANDOM_RANGE) + NICKNAME_RANDOM_MIN;
            String nickName = "FU_" + num;
            platformUserinfo.setNickName(nickName);
            platformUserinfo.setEnable(YesNo.YES.getValue());
            platformUserinfo.setScore(BigDecimal.ZERO);
            fookPlatformUserinfoDao.insert(platformUserinfo);
            userId = platformUserinfo.getId();
            //创建用户信息-macaupassUser
            FookMacaupassUser macaupassUser = new FookMacaupassUser();
            macaupassUser.setUserId(platformUserinfo.getId());
            macaupassUser.setOpenid(openid);
            macaupassUser.setAccessToken(accessToken);
            macaupassUser.setRefreshToken(refreshToken);
            macaupassUser.setCreateTime(date);
            macaupassUser.setUpdateTime(date);
            macaupassUser.setCode(code);
            macaupassUser.setCustomid(custId);
            fookMacaupassUserDao.insert(macaupassUser);

            //更新关联id
            FookPlatformUserinfo updatePlatUser = new FookPlatformUserinfo();
            updatePlatUser.setMacaupassId(macaupassUser.getId());
            fookPlatformUserinfoDao.update(updatePlatUser, new LambdaQueryWrapper<FookPlatformUserinfo>()
                    .eq(FookPlatformUserinfo::getId, platformUserinfo.getId()));
            log.info("新增用户信息成功,userId={}", platformUserinfo.getId());
            //生成jwtToken
            jwtToken = tokenManager.createJwtToken(platformUserinfo.getId(), nickName);

        } else {
            userId = userInfo.getUserId();
            //更新用户信息
            FookMacaupassUser macaupassUser = new FookMacaupassUser();
            macaupassUser.setAccessToken(accessToken);
            macaupassUser.setRefreshToken(refreshToken);
            macaupassUser.setUpdateTime(date);
            macaupassUser.setCode(code);
            if(StringUtils.isBlank(userInfo.getCustomid())) {
                log.info("用户信息中custId为空，重新获取 {}",userInfo.getUserId());
                macaupassUser.setCustomid(getCustId(openid));
            }
            fookMacaupassUserDao.update(macaupassUser, new LambdaQueryWrapper<FookMacaupassUser>()
                    .eq(FookMacaupassUser::getId, userInfo.getId()));
            //查询PlatformUser
            FookPlatformUserinfo platformUserinfo = fookPlatformUserinfoDao.selectOne(new LambdaQueryWrapper<FookPlatformUserinfo>()
                    .eq(FookPlatformUserinfo::getId, userInfo.getUserId())
                    .last("limit 1")
            );
            //生成jwtToken
            jwtToken = tokenManager.createJwtToken(platformUserinfo.getId(), platformUserinfo.getNickName());
        }
        //新增登录日志
        try {
            FookMacaupassUserLoginLog2 loginLog = new FookMacaupassUserLoginLog2();
            loginLog.setUserId(userId);
            loginLog.setOpenid(openid);
            loginLog.setAccessToken(accessToken);
            loginLog.setRefreshToken(refreshToken);
            loginLog.setIp(contextHolder.getClientIpAddress());
            loginLog.setCreateTime(date);
            fookMacaupassUserLoginLog2Dao.insertSelective(loginLog);
        } catch (Exception e) {
            log.error("新增登录日志记录异常:", e);
        }
        //处理返回参数
        GetTokenResponse resp = new GetTokenResponse();
        resp.setToken(jwtToken);
        resp.setOpenId(openid);
        resp.setRead(0);
        resp.setState(state);
        if(null != mPayUserInfoResponse) {
            resp.setNickname(mPayUserInfoResponse.getNickname());
            resp.setHeadimgurl(mPayUserInfoResponse.getHeadimgurl());
        }
        log.info("接口/macaupasssave耗时：{}毫秒", System.currentTimeMillis() - startTime);
        return resp;
    }

    protected String getCustId(String openid) {
        //请求mpay获取用户id
        String userIdResp = handleBlockException(()->mPayClient.getUserId(openid), null);
        log.info("请求mpay获取用户id响应内容：{}", userIdResp);
        if (StringUtils.isEmpty(userIdResp)) {
            log.error("获取用户id请求失败，触发用户重新登录");
            throw new BusinessException(Response.Code.UNAUTHORIZED, NOT_LOGIN);
        }
        MPayUserInfoResponse mPayUserIdResponse = JSON.parseObject(userIdResp, MPayUserInfoResponse.class);
        String custId = mPayUserIdResponse.getUserId();
        if (StringUtils.isEmpty(custId)) {
            log.error("获取用户id失败，触发用户重新登录");
            throw new BusinessException(Response.Code.UNAUTHORIZED, NOT_LOGIN);
        }
        return custId;
    }

    @Override
    public GetUserInfoResponse updAndGetUserInfo() {
        //积分兑换比例-优先从数据读取，默认读取配置，其次默认300
        String ratio = defaultIfBlank(defaultIfBlank(settingsDao.getValue("site.mpay"), ConfigUtils.getProperty("macaupass.POINT_RATIO")), "300");
        GetUserInfoResponse resp = new GetUserInfoResponse();
        UserInfo userInfo = contextHolder.getAuthUserInfo();
        log.info("当前系统积分比例：{},获取用户基础信息开始，当前userId:{}", ratio,userInfo.getUserId());
        long startTime = System.currentTimeMillis();
        FookMacaupassUser fookMacaupassUser = fookMacaupassUserDao.selectOne(new LambdaQueryWrapper<FookMacaupassUser>()
                .eq(FookMacaupassUser::getUserId, userInfo.getUserId())
                .last("limit 1")
        );
        //查询用户优惠券数量
        Integer count = fookPlatformOrdercodeDao.getCountByUserId(userInfo.getUserId());
        resp.setCount(count);
        //沿用php代码逻辑，未查询到用户信息时，返回-10积分
        if (fookMacaupassUser == null) {
            resp.setPoint(-10);
            resp.setAmount(BigDecimal.valueOf(Math.floor((double) -10 / Integer.parseInt(ratio))));
            log.error("未查询到用户信息，返回信息：{}", resp);
            return resp;
        }
        String dateAgo = JodaTimeUtil.addDateHours(-2, JodaTimeUtil.DEFAULT_PATTERN);
        String updateTime = JodaTimeUtil.format(fookMacaupassUser.getUpdateTime(), JodaTimeUtil.DEFAULT_PATTERN);
        boolean isAfter = JodaTimeUtil.isAfter(dateAgo, updateTime, JodaTimeUtil.DEFAULT_PATTERN);
        log.info("isAfter={}", isAfter);
        //如果用户token已过期则请求mpay刷新token
        if (isAfter) {
            String refreshTokenResp = handleBlockException(()->mPayClient.refreshToken(
                    fookMacaupassUser.getRefreshToken()), null);
            log.info("刷新toekn响应内容：{}", refreshTokenResp);
            if (StringUtils.isEmpty(refreshTokenResp)) {
                throw new BusinessException(Response.Code.BAD_REQUEST, "request refreshToken error");
            }
            MPayRefreshTokenResponse mPayRefreshTokenResponse = JSON.parseObject(refreshTokenResp, MPayRefreshTokenResponse.class);
            fookMacaupassUser.setAccessToken(mPayRefreshTokenResponse.getAccessToken());
            fookMacaupassUser.setRefreshToken(mPayRefreshTokenResponse.getRefreshToken());
            fookMacaupassUser.setUpdateTime(new Date());
        }
        //優先去Mcoin積分系統獲取積分
        Integer point;
        try {
            point = mcoinChannelService.getPoint(fookMacaupassUser.getCustomid());
            if (point == null) {
                point = getPointFromMPay(fookMacaupassUser);
            }
        } catch (RetryException e) {
            throw e;
        } catch (Exception e) {
            log.error("獲取積分未知異常", e);
            point = getPointFromMPay(fookMacaupassUser);
        }
        if (point == null) {
            throw new BusinessException(Response.Code.BAD_REQUEST, "get point error");
        }
        fookMacaupassUser.setPoint(point);
        fookMacaupassUser.setStatus(1);
        fookMacaupassUserDao.update(fookMacaupassUser, new LambdaQueryWrapper<FookMacaupassUser>()
                .eq(FookMacaupassUser::getUserId, userInfo.getUserId()));
        resp.setPoint(point);
        resp.setAmount(BigDecimal.valueOf(Math.floor((double) point / Integer.parseInt(ratio))));
        log.info("获取用户基础信息耗时：{}毫秒,返回参数：{}",  System.currentTimeMillis() - startTime,JSON.toJSONString(resp));
        return resp;
    }

    private Integer getPointFromMPay(FookMacaupassUser fookMacaupassUser) {
        //请求mpay获取积分并更新至本地
        String userInfoResp = null;
        try {
            userInfoResp = handleBlockException(()->mPayClient.getUserInfo(fookMacaupassUser.getAccessToken(), fookMacaupassUser.getOpenid()), null);
        } catch (FeignException e) {
            if (e.status() == 401) {
                log.warn("mpay返回401", e);
                throw new BusinessException(Response.Code.BAD_REQUEST, "request userinfo error");
            }
        }
        log.info("请求mpay获取用户信息响应内容：{}", userInfoResp);
        if (StringUtils.isEmpty(userInfoResp)) {
            throw new BusinessException(Response.Code.BAD_REQUEST, "request userinfo error");
        }
        MPayUserInfoResponse mPayUserInfoResponse = JSON.parseObject(userInfoResp, MPayUserInfoResponse.class);
        return mPayUserInfoResponse.getPoint();
    }

    @Override
    public RefreshTokenResponse refreshToken() {
        UserInfo userInfo = contextHolder.getAuthUserInfo();
        RefreshTokenResponse resp = new RefreshTokenResponse();
        String jwtToken = tokenManager.createJwtToken(userInfo.getUserId(), userInfo.getUsername());
        log.info("重新生成的jwtToken为：{}", jwtToken);
        resp.setAccessToken(jwtToken);
        resp.setTokenType("bearer");
        long expireTime = Long.parseLong(ConfigUtils.getProperty("security.jwt.expire", "7200"));
        resp.setExpiresIn(expireTime);
        resp.setStatus(1);
        return resp;
    }

    @Override
    public UserProfileResponse updAndGetUserProfile() {
        log.info("获取用户昵称和头像开始");
        long startTime = System.currentTimeMillis();
        UserProfileResponse response = new UserProfileResponse();
        UserInfo userInfo = contextHolder.getAuthUserInfo();
        
        FookMacaupassUser fookMacaupassUser = fookMacaupassUserDao.selectOne(new LambdaQueryWrapper<FookMacaupassUser>()
                .eq(FookMacaupassUser::getUserId, userInfo.getUserId())
                .last("limit 1")
        );
        
        if (fookMacaupassUser == null) {
            log.error("未查询到用户信息");
            throw new BusinessException(Response.Code.UNAUTHORIZED, NOT_LOGIN);
        }
        
        String dateAgo = JodaTimeUtil.addDateHours(-2, JodaTimeUtil.DEFAULT_PATTERN);
        String updateTime = JodaTimeUtil.format(fookMacaupassUser.getUpdateTime(), JodaTimeUtil.DEFAULT_PATTERN);
        boolean isAfter = JodaTimeUtil.isAfter(dateAgo, updateTime, JodaTimeUtil.DEFAULT_PATTERN);
        log.info("isAfter={}", isAfter);
        
        // 用于存储刷新后的token信息
        String newAccessToken = null;
        String newRefreshToken = null;
        
        //如果用户token已过期则请求mpay刷新token
        if (isAfter) {
            String refreshTokenResp = handleBlockException(()->mPayClient.refreshToken(
                    fookMacaupassUser.getRefreshToken()), null);
            log.info("刷新token响应内容：{}", refreshTokenResp);
            if (StringUtils.isEmpty(refreshTokenResp)) {
                throw new BusinessException(Response.Code.BAD_REQUEST, "request refreshToken error");
            }
            MPayRefreshTokenResponse mPayRefreshTokenResponse = JSON.parseObject(refreshTokenResp, MPayRefreshTokenResponse.class);
            newAccessToken = mPayRefreshTokenResponse.getAccessToken();
            newRefreshToken = mPayRefreshTokenResponse.getRefreshToken();
        }
        
        //请求mpay获取用户信息
        String userInfoResp;
        try {
            userInfoResp = handleBlockException(()->mPayClient.getUserInfo(fookMacaupassUser.getAccessToken(), fookMacaupassUser.getOpenid()), null);
        } catch (FeignException e) {
            if (e.status() == 401) {
                log.warn("mpay返回401", e);
                throw new BusinessException(Response.Code.BAD_REQUEST, "request userinfo error");
            }
            throw new BusinessException(Response.Code.BAD_REQUEST, "request userinfo error");
        }
        
        log.info("请求mpay获取用户信息响应内容：{}", userInfoResp);
        if (StringUtils.isEmpty(userInfoResp)) {
            throw new BusinessException(Response.Code.BAD_REQUEST, "request userinfo error");
        }
        
        MPayUserInfoResponse mPayUserInfoResponse = JSON.parseObject(userInfoResp, MPayUserInfoResponse.class);
        response.setNickname(mPayUserInfoResponse.getNickname());
        response.setHeadimgurl(mPayUserInfoResponse.getHeadimgurl());
        String area = defaultIfBlank(mPayUserInfoResponse.getArea(), "");
        String phone = defaultIfBlank(mPayUserInfoResponse.getPhone(), "");

        // 更新用户的昵称和头像信息
        // 更新FookPlatformUserinfo表中的头像和昵称字段
        FookPlatformUserinfo platformUserinfo = new FookPlatformUserinfo();
        platformUserinfo.setAvatar(mPayUserInfoResponse.getHeadimgurl());
        platformUserinfo.setRegisteredPhone(area.trim().replace("+", "") + phone.trim());
        
        fookPlatformUserinfoDao.update(platformUserinfo, new LambdaQueryWrapper<FookPlatformUserinfo>()
                .eq(FookPlatformUserinfo::getId, userInfo.getUserId()));
        
        // 创建新的更新对象，只设置需要更新的字段
        FookMacaupassUser updateUser = new FookMacaupassUser();
        updateUser.setUpdateTime(new Date());
        updateUser.setPhone(phone);
        updateUser.setArea(area);
        updateUser.setPoint(mPayUserInfoResponse.getPoint());
        
        // 如果刷新了token，添加token信息到更新对象
        if (isAfter && newAccessToken != null && newRefreshToken != null) {
            updateUser.setAccessToken(newAccessToken);
            updateUser.setRefreshToken(newRefreshToken);
            log.info("已更新用户token：userId={}", userInfo.getUserId());
        }
        
        // 使用新的更新对象进行更新
        fookMacaupassUserDao.update(updateUser, new LambdaQueryWrapper<FookMacaupassUser>()
                .eq(FookMacaupassUser::getUserId, userInfo.getUserId()));
        
        log.info("已更新用户信息：userId={}, nickname={}, headimgurl={}", 
                 userInfo.getUserId(), 
                 mPayUserInfoResponse.getNickname(), 
                 mPayUserInfoResponse.getHeadimgurl());

        log.info("获取用户昵称和头像耗时：{}毫秒,返回参数：{}", System.currentTimeMillis() - startTime, JSON.toJSONString(response));
        return response;
    }

    @Override
    public CouponLoginResponse couponLogin(CouponLoginRequest request) {
        String customId = request.getCustomid();
        String orderNo = request.getOrder_no();
        if (StringUtils.isEmpty(customId) || StringUtils.isEmpty(orderNo)) {
            throw new BusinessException(Response.Code.BAD_REQUEST, "customId或orderNo不能为空");
        }
        List<FookMacaupassUser> fookMacaupassUserList = fookMacaupassUserDao.selectList(new LambdaQueryWrapper<FookMacaupassUser>()
                .eq(FookMacaupassUser::getCustomid, customId).orderByDesc(FookMacaupassUser::getUpdateTime));
        if (fookMacaupassUserList == null || fookMacaupassUserList.size() <= 0) {
            throw new BusinessException(Response.Code.BAD_REQUEST, "用户不存在");
        }
        FookMacaupassUser fookMacaupassUser = fookMacaupassUserList.get(0);
        FookPlatformUserinfo fookPlatformUserinfo = fookPlatformUserinfoDao.selectOne(new LambdaQueryWrapper<FookPlatformUserinfo>()
                .eq(FookPlatformUserinfo::getId, fookMacaupassUser.getUserId()));
        if (fookPlatformUserinfo == null) {
            throw new BusinessException(Response.Code.BAD_REQUEST, "用户不存在");
        }
        FookPlatformOrder fookPlatformOrder = fookPlatformOrderDao.selectOne(new LambdaQueryWrapper<FookPlatformOrder>()
                .eq(FookPlatformOrder::getUserid, fookPlatformUserinfo.getId())
                .eq(FookPlatformOrder::getOrderNo, orderNo)
                .last("limit 1")
        );
        if (fookPlatformOrder == null) {
            throw new BusinessException(Response.Code.BAD_REQUEST, "用户订单不存在");
        }
        //生成jwtToken
        String jwtToken = tokenManager.createJwtToken(fookPlatformUserinfo.getId(), fookPlatformUserinfo.getNickName());
        log.info("用户custId[{}]生成的jwtToken为[{}]", customId, jwtToken);
        CouponLoginResponse resp = new CouponLoginResponse();
        resp.setToken(jwtToken);
        return resp;
    }

    private Map<String, String> getUtmMap(Map<String, String> paramsMap) {
        Map<String, String> utmParams = new HashMap<>(16);
        for (Map.Entry<String, String> entry : paramsMap.entrySet()) {
            String key = entry.getKey();
            String value = entry.getValue();
            if (key.startsWith("utm_")) {
                utmParams.put(key, value);
            }
        }
        return utmParams;
    }
}
