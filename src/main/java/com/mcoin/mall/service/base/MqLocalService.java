package com.mcoin.mall.service.base;

import java.util.List;

import com.mcoin.mall.bean.FookMqLocal;

import cn.hutool.core.date.DateTime;


/**
 * MQ
 */
public interface MqLocalService {

    int updateMqLocalFinish(Long id);

    int saveMqLocal(FookMqLocal record);

    void resendMq();

    List<FookMqLocal> queryByIdAndType(String resourceId, String resourceType,List<Integer> status, DateTime startTime);

    void resendGradientMq();

}
