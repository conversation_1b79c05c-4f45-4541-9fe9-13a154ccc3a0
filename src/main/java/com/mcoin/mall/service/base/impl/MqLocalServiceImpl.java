package com.mcoin.mall.service.base.impl;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

import javax.annotation.Resource;

import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.mcoin.mall.bean.FookMqLocal;
import com.mcoin.mall.constant.MqLocalResourceType;
import com.mcoin.mall.constant.MqLocalStatus;
import com.mcoin.mall.constant.Numbers;
import com.mcoin.mall.dao.FookMqLocalDao;
import com.mcoin.mall.exception.BusinessException;
import com.mcoin.mall.model.Response;
import com.mcoin.mall.service.base.MqLocalService;
import com.mcoin.mall.util.ConfigUtils;
import com.mcoin.mall.util.TimeIntervalUtil;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class MqLocalServiceImpl implements MqLocalService {

    @Resource
    private FookMqLocalDao fookMqLocalDao;

    @Resource
    private ApplicationContext applicationContext;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateMqLocalFinish(Long id) {
        FookMqLocal mq = new FookMqLocal();
        mq.setId(id);
        mq.setStatus(1);
        return this.fookMqLocalDao.updateByPrimaryKeySelective(mq);
    }

    @Transactional(rollbackFor = Exception.class)
    public int saveMqLocal(FookMqLocal record) {
        record.setStatus(0);
        String messageBody = record.getMessageBody();
        record.setMessageBody("");
        fookMqLocalDao.insert(record);
        JSONObject msg = JSON.parseObject(messageBody);
        if (null == record.getId()) {
            log.error("insert into fook_mq_local error,can not get id.");
            throw new BusinessException(Response.Code.UNKNOWN_ERROR, "system error");
        }
        msg.put("mqLocalId", record.getId());
        FookMqLocal nrecord = new FookMqLocal();
        nrecord.setId(record.getId());
        nrecord.setMessageBody(JSON.toJSONString(msg));
        return fookMqLocalDao.updateByPrimaryKeySelective(nrecord);
    }

    @Override
    public void resendMq() {
        LambdaQueryWrapper<FookMqLocal> filter = new LambdaQueryWrapper<FookMqLocal>()
                .eq(FookMqLocal::getStatus, MqLocalStatus.PENDING.getStatus())
                .between(FookMqLocal::getNextRetry, DateUtil.lastWeek(), DateUtil.now());
        String whiteList = ConfigUtils.getProperty("mcoin.mq.white.list", MqLocalResourceType.PAY_LOG.getType());
        String[] split = whiteList.split(",");
        filter.in(FookMqLocal::getResourceType, Arrays.asList(split));
        List<FookMqLocal> fookMqLocals = fookMqLocalDao.selectList(filter);
        for (FookMqLocal mqLocal : fookMqLocals) {
            try {
                // 如果超过最大重试次数，则不再重试
                if (0 != mqLocal.getMaxTryCount() && mqLocal.getTryCount() < mqLocal.getMaxTryCount()) {
                    String templateName = mqLocal.getTemplateName();
                    RabbitTemplate rabbitTemplate = applicationContext.getBean(templateName, RabbitTemplate.class);
                    rabbitTemplate.convertAndSend(mqLocal.getMessageBody());
                    log.info("发送到Template：{}，延迟（0ms）消息：{}", templateName, mqLocal.getMessageBody());
                    FookMqLocal updMqLocal = new FookMqLocal();
                    updMqLocal.setId(mqLocal.getId());
                    updMqLocal.setTryCount(mqLocal.getTryCount() + 1);
                    this.fookMqLocalDao.updateByPrimaryKeySelective(updMqLocal);
                } else {
                    log.info("mq local id is {} has reach max retry count!", mqLocal.getId());
                    FookMqLocal updMqLocal = new FookMqLocal();
                    updMqLocal.setId(mqLocal.getId());
                    // 已达到最大尝试次数，不再重试
                    updMqLocal.setStatus(2);
                    this.fookMqLocalDao.updateByPrimaryKeySelective(updMqLocal);
                }
            } catch (Exception e) {
                log.error("resend Mq error, mq local id is " + mqLocal.getId() + " ", e);
            }
        }
    }

    @Override
    public List<FookMqLocal> queryByIdAndType(String resourceId, String resourceType, List<Integer> status,
            DateTime startTime) {
        LambdaQueryWrapper<FookMqLocal> wrapper = new LambdaQueryWrapper<FookMqLocal>()
                .eq(FookMqLocal::getResourceId, resourceId)
                .eq(FookMqLocal::getResourceType, resourceType)
                .in(FookMqLocal::getStatus, status);
        if (null != startTime) {
            wrapper.ge(FookMqLocal::getCreateTime, startTime);
        }
        return fookMqLocalDao.selectList(wrapper);

    }






    @Override
    public void resendGradientMq() {
        String white = ConfigUtils.getProperty("mcoin.gradient.white.list");
        String[] whiteList = white.split(",");
        String startTimeStr = ConfigUtils.getProperty("mcoin.gradient.startTime","-4320");
        String endTimeStr = ConfigUtils.getProperty("mcoin.gradient.endTime","0");
        // 1. 初始化时间
        DateTime startTime = DateUtil.offsetMinute(DateUtil.date(), Integer.parseInt(startTimeStr));
        DateTime endTime = DateUtil.offsetMinute(DateUtil.date(), Integer.parseInt(endTimeStr));

        List<FookMqLocal> mqLocals = fookMqLocalDao.selectList(new LambdaQueryWrapper<FookMqLocal>()
            .eq(FookMqLocal::getStatus, MqLocalStatus.PENDING.getStatus())
            .between(FookMqLocal::getNextRetry, startTime, endTime)
            .in(FookMqLocal::getResourceType, Arrays.asList(whiteList)));

        for(FookMqLocal mqLocal : mqLocals) {
            try {
                doResendGradientMq(mqLocal);
            } catch (Exception e) {
                log.error("重试发送消息失败，mqLocalId:{}", mqLocal.getId(), e);
            }
        }
    }




    @Transactional(rollbackFor = Exception.class)
    public void doResendGradientMq(FookMqLocal mqLocal) {
        String gradientRetryInterval = ConfigUtils.getProperty("mcoin.gradient.interval." + mqLocal.getResourceType(), "1m,10m,1h,4h,12h,24h,48h");
        String[] intervals = gradientRetryInterval.split(",");

        int tryCount = mqLocal.getTryCount();
        if (tryCount < intervals.length) {
            int nextTryCount = tryCount + Numbers.ONE.getIntValue();
            int delayMs = Numbers.TWO_MINUTE_MILSEC.getIntValue();
            if(nextTryCount < intervals.length) {
                delayMs = TimeIntervalUtil.parseIntervalToMillis(intervals[nextTryCount]);
            }
            FookMqLocal updMqLocal = new FookMqLocal();
            updMqLocal.setId(mqLocal.getId());
            updMqLocal.setNextRetry(DateUtil.offsetMillisecond(new Date(), delayMs));
            updMqLocal.setTryCount(nextTryCount);
            fookMqLocalDao.updateByPrimaryKeySelective(updMqLocal);
            RabbitTemplate rabbitTemplate = applicationContext.getBean(mqLocal.getTemplateName(),
                    RabbitTemplate.class);
            rabbitTemplate.convertAndSend(mqLocal.getMessageBody());
            log.info("重试发送消息：（{}ms）消息：{}", delayMs, mqLocal.getMessageBody());
        } else {
            // 已达到最大尝试次数，不再重试
            int update = fookMqLocalDao.update(null, new LambdaUpdateWrapper<FookMqLocal>()
                    .set(FookMqLocal::getStatus, MqLocalStatus.FAILED.getStatus())
                    .eq(FookMqLocal::getId, mqLocal.getId())
                    .eq(FookMqLocal::getStatus, MqLocalStatus.PENDING.getStatus()));
            log.info("更新mq local id is {} status to {} 结果{}", mqLocal.getId(), MqLocalStatus.FAILED.getStatus(),
                    update);
        }
    }

}
















