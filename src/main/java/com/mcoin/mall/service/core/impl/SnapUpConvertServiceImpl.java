package com.mcoin.mall.service.core.impl;

import com.mcoin.mall.bo.ProductsCollectBo;
import com.mcoin.mall.bo.SnapUpItemBo;
import com.mcoin.mall.bo.StoreDistanceBo;
import com.mcoin.mall.dao.FookBusinessStoreProductDao;
import com.mcoin.mall.model.BaseDistanceRequest;
import com.mcoin.mall.model.SnapUpNextResponse;
import com.mcoin.mall.model.SnapUpRequest;
import com.mcoin.mall.model.SnapUpSessionProductItem;
import com.mcoin.mall.service.common.CollectService;
import com.mcoin.mall.service.core.SnapUpConvertService;
import com.mcoin.mall.util.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import static com.mcoin.mall.util.BusinessProductUtil.fillHrefUrlBy;
import static org.apache.commons.lang3.StringUtils.isBlank;

/**
 * <AUTHOR>
 * @date 2024/4/28
 */
@Service
public class SnapUpConvertServiceImpl implements SnapUpConvertService {
    @Resource
    private FookBusinessStoreProductDao fookBusinessStoreProductDao;

    @Resource
    private CollectService collectService;


    @Override
     public List<SnapUpNextResponse.SnatchListItem> getSnatchList(SnapUpRequest request, String language,
                                                                  List<SnapUpItemBo> snapUpItemBos, boolean isNext) {
        List<SnapUpNextResponse.SnatchListItem> snatchList = new ArrayList<>();
        List<Integer> productIds = snapUpItemBos.stream().map(SnapUpItemBo::getId).collect(Collectors.toList());
        // 处理收藏
        ProductsCollectBo collectBo = collectService.getProductsCollect(productIds);
        for (SnapUpItemBo item : snapUpItemBos) {
            String[] times = JodaTimeUtil.format(item.getBuyEndTime(), "yyyy-MM-dd").split("-");
            String timeMsg = "搶購至:" + times[0] + "年" + times[1] + "月" + times[2] + "日";

            String snap_up_tips = "馬上搶";
            int snap_up_status = 1;
            String snap_up_start = "開始";
            String snap_up_end = "結束";
            boolean isEn = McoinMall.LANG_EN.equals(language);
            if (isEn) {
                snap_up_tips = "Buy now";
                timeMsg = "Offer valid until : " + times[0] + "-" + times[1] + "-" + times[2];
                snap_up_start = "Starts";
                snap_up_end = "Ends";
            }
            if (item.getStock() <= 0) {
                snap_up_tips = "已搶完";//已搶完 Sold out  已搶完
                if (isEn) {
                    snap_up_tips = "Sold out";
                }
                snap_up_status = 0;
            } else if (item.getBuyStartTime().compareTo(new Date()) > 0) {
                snap_up_tips = "Coming Soon";
                snap_up_status = 2;
            }
            double occupy = 0;
            if (item.getActualSales() + item.getStock() == 0 || item.getStock() == 0) {
                occupy = 0;
            } else {
                //除數不能為0
                occupy = (double) item.getActualSales() / (double) (item.getActualSales() + item.getStock());
            }
            String img_tmp = StringUtils.defaultIfBlank(item.getZipImg(), item.getImg());
            List<StoreDistanceBo> distanceBos = fookBusinessStoreProductDao
                    .getStoreDistanceListByProductId(item.getId());
            // 如果没有启用门店则跳过
            if (distanceBos.size() == 0) {
                continue;
            }
            String distanceStr = "";
            if (!isBlank(request.getLat()) && !isBlank(request.getLot())) {
                Double distance = distanceBos.stream().map((e) -> DistanceUtil.getDistance(request.getLot(), request.getLat(),
                        e.getLongitude(), e.getDimension())).min(Double::compareTo).orElse(null);
                // 如果没有启用门店则跳过
                if (null == distance) {
                    continue;
                }
                distanceStr = DistanceUtil.getDistanceStr(distance);
            }

            SnapUpNextResponse.SnatchListItem snatchListItem = new SnapUpNextResponse.SnatchListItem();
            snatchListItem.setId(item.getId());
            snatchListItem.setTitle(item.getTitle());
            snatchListItem.setImg(OssUtil.initOssImage(img_tmp));
            snatchListItem.setImage(OssUtil.initOssImage(item.getImg()));
            snatchListItem.setPrice(item.getPrice());
            snatchListItem.setRetailPrice(item.getRetailPrice());
            snatchListItem.setBuyEndTime(JodaTimeUtil.format(item.getBuyEndTime()));
            snatchListItem.setBoom(item.getBusinessId());
            snatchListItem.setRushToBuyTime(timeMsg);
            snatchListItem.setPayType(item.getOnlyPoint());
            snatchListItem.setDistance(distanceStr);
            if (!isNext) {
                snatchListItem.setType(item.getType());
                snatchListItem.setHrefUrl(fillHrefUrlBy(item.getId(), item.getType(), item.getHrefUrl(), item.getGoodsId()));
            }

            snatchListItem.setPoint(SnapUpUtils.getPoint(item));
            snatchListItem.setPreferential(SnapUpUtils.getPreferential(item));
            snatchListItem.setSnapUp(item.getSnapUp());
            snatchListItem.setCollect(0);
            snatchListItem.setCollectCount("");
            snatchListItem.setBusinessName(item.getBusinessName());
            snatchListItem.setSnapUpTips(snap_up_tips);
            snatchListItem.setStock(item.getStock());
            snatchListItem.setActualSales(item.getActualSales());
            snatchListItem.setSnapUpStatus(snap_up_status);
            snatchListItem.setTimeLeft(item.getBuyEndTime().getTime() - System.currentTimeMillis());
            snatchListItem.setTimeBegin(item.getBuyStartTime().getTime() - System.currentTimeMillis());
            snatchListItem.setStart(snap_up_start);
            snatchListItem.setEnd(snap_up_end);
            snatchListItem.setOccupy(SnapUpUtils.getOccupy(item));
            // 处理收藏
            collectService.setCollectNumber(collectBo, item.getId(), snatchListItem);
            snatchList.add(snatchListItem);
        }
        return snatchList;
    }


    /**
     * 将商品列表转换为抢购会话商品列表
     *
     * @param request 包含请求参数的基类
     * @param snapUpItemBos 原始商品列表
     * @return 转换后的抢购会话商品列表
     */
    @Override
    public List<SnapUpSessionProductItem> convertToSessionSnatchList(BaseDistanceRequest request, List<SnapUpItemBo> snapUpItemBos) {
        List<SnapUpSessionProductItem> snatchList = new ArrayList<>();
        List<Integer> productIds = snapUpItemBos.stream().map(SnapUpItemBo::getId).collect(Collectors.toList());
        // 处理收藏
        ProductsCollectBo collectBo = collectService.getProductsCollect(productIds);
        for (SnapUpItemBo item : snapUpItemBos) {

            String distanceStr = "";
            if (!isBlank(request.getLat()) && !isBlank(request.getLot())) {
                List<StoreDistanceBo> distanceBos = fookBusinessStoreProductDao
                        .getStoreDistanceListByProductId(item.getId());
                Double distance = distanceBos.stream().map((e) -> DistanceUtil.getDistance(request.getLot(), request.getLat(),
                        e.getLongitude(), e.getDimension())).min(Double::compareTo).orElse(null);
                if (distance != null) {
                    distanceStr = DistanceUtil.getDistanceStr(distance);
                }
            }

            SnapUpSessionProductItem sessionProductItem = new SnapUpSessionProductItem();
            sessionProductItem.setId(item.getId());
            sessionProductItem.setTitle(item.getTitle());
            String img = StringUtils.defaultIfBlank(item.getSeckillImg(), item.getImg());
            String zipImg = StringUtils.defaultIfBlank(item.getSeckillImg(), item.getZipImg());
            zipImg = StringUtils.defaultIfBlank(zipImg, item.getImg());
            sessionProductItem.setImg(OssUtil.initOssImage(zipImg));
            sessionProductItem.setImage(OssUtil.initOssImage(img));
            sessionProductItem.setPrice(item.getPrice());
            sessionProductItem.setRetailPrice(item.getRetailPrice());
            sessionProductItem.setBuyEndTime(item.getBuyEndTime());
            sessionProductItem.setBoom(item.getBusinessId());
            sessionProductItem.setPayType(item.getOnlyPoint());
            sessionProductItem.setDistance(distanceStr);
            sessionProductItem.setType(item.getType());
            sessionProductItem.setHrefUrl(fillHrefUrlBy(item.getId(), item.getType(), item.getHrefUrl(), item.getGoodsId()));
            sessionProductItem.setPoint(SnapUpUtils.getPoint(item));
            sessionProductItem.setPreferential(SnapUpUtils.getPreferential(item));
            sessionProductItem.setSnapUp(item.getSnapUp());
            sessionProductItem.setCollect(0);
            sessionProductItem.setCollectCount("");
            sessionProductItem.setBusinessName(item.getBusinessName());
            sessionProductItem.setStock(item.getStock());
            sessionProductItem.setActualSales(item.getActualSales());
            sessionProductItem.setOccupy(SnapUpUtils.getOccupy(item));
            //直降金额=原价-现价
            sessionProductItem.setSubsidyAmount(item.getRetailPrice().subtract(item.getPrice()));
            // 处理收藏
            collectService.setCollectNumber(collectBo, item.getId(), sessionProductItem);
            snatchList.add(sessionProductItem);
        }
        return snatchList;
    }
}
