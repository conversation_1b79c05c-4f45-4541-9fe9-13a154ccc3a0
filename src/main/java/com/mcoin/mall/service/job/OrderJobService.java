package com.mcoin.mall.service.job;

import com.mcoin.mall.model.job.EvcodeToOrderRequest;
import com.mcoin.mall.model.job.EvcodeToOrderResponse;
import com.mcoin.mall.model.job.OrderCloseRequest;
import com.mcoin.mall.model.job.OrderQueryStatusRequest;
import com.mcoin.mall.model.job.OrderRefundJobResponse;

public interface OrderJobService {

    void triggerClose(OrderCloseRequest request);

    EvcodeToOrderResponse evcodeToOrder(EvcodeToOrderRequest request);

    void triggerQueryStatus(OrderQueryStatusRequest request);

    OrderRefundJobResponse approvalRefund();

    /** 处理超时未退款订单 **/
    void triggerRefundTimeoutRefundOrder();

}