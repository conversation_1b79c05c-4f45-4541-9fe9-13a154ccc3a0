package com.mcoin.mall.service.job;

import com.mcoin.mall.model.job.SettlementJobRequest;
import com.mcoin.mall.mq.model.SettlementMessage;

public interface SettlementJobService {

    void triggerHalfMonthSettlement(SettlementJobRequest request);

    void doSettlement(SettlementMessage message);

    void sumMcoinOrderInMpay(SettlementJobRequest request);

    void sumYearMcoinOrderInMpay(SettlementJobRequest request);

    void sumSubmitedMcoinCodeInMpay(SettlementJobRequest request);

    void sumBusinessData(SettlementJobRequest request);

    void sumOrderSettlementData(SettlementJobRequest request);

    void sumAllOrderSettlement(SettlementJobRequest request);

    void settlementVoucher(SettlementJobRequest request);
}
