package com.mcoin.mall.service.job;

import com.mcoin.mall.bo.SettlementReportDetailBo;
import freemarker.template.TemplateException;
import org.springframework.core.io.Resource;

import java.io.IOException;
import java.util.List;

public interface SettlementReportUploadService {

    <V> String doCreatePdfAndUploadOss(Integer settlementId, List<SettlementReportDetailBo> reportDetailBos,
                                       String ossPath, String filename, String pdfTemplatePath,
                                       PdfModelCallable<V> pdfModelCallable) throws IOException, TemplateException;

    interface PdfModelCallable<V> {
        V call(List<SettlementReportDetailBo> detailBos, int pageCountStart, boolean isLastPage);
    }

    <V> String doCreateExcelAndUploadOss(Integer settlementId, List<SettlementReportDetailBo> reportDetailBos,
                                         String ossPath, String filename, Resource settlementTemplate,
                                         ExcelModelCallable<V> pdfModelCallable) throws IOException;

    interface ExcelModelCallable<V> {
        V call(List<SettlementReportDetailBo> detailBos, int pageCountStart, boolean isLastPage);
    }
}
