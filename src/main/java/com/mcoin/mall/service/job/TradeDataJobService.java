package com.mcoin.mall.service.job;

import com.mcoin.mall.model.job.TradingCycleCreateRequest;
import com.mcoin.mall.model.job.TradingDataCreateFileRequest;
import com.mcoin.mall.model.job.TradingDataRequest;
import com.mcoin.mall.model.job.TradingDataSendEmailRequest;

/**
 * <AUTHOR>
 */
public interface TradeDataJobService {
    //mCoin交易統計數據(設定的時間要晚於每日訂單統計結算報表、退款統計報表生成時間)  trading:data
    void doCreateTradingData(TradingDataRequest request);
    //mCoin交易統計週期  trading:trading
    void doCreateTradingCycle(TradingCycleCreateRequest request);
    //每分鐘mCoin交易統計週期Excel處理  trading:create-excel
    void doCreateTrade2Excel(TradingDataCreateFileRequest request);
    //mCoin交易統計數據郵件發送       send:trading-data-email
    void sendTradingEmail(TradingDataSendEmailRequest request);
}
