package com.mcoin.mall.service.job.impl;

import com.mcoin.mall.bo.RecommendProductBo;
import com.mcoin.mall.constant.BusinessProductTypeEnum;
import com.mcoin.mall.dao.FookProductManualRecommendDao;
import com.mcoin.mall.exception.RetryException;
import com.mcoin.mall.model.job.UpdateRecommendCacheRequest;
import com.mcoin.mall.service.job.FeedsJobService;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RScoredSortedSet;
import org.redisson.api.RedissonClient;
import org.redisson.client.protocol.ScoredEntry;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.Cache;
import org.springframework.data.redis.cache.RedisCacheManager;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static com.mcoin.mall.service.common.CacheService.CACHE_STORES_TYPE;
import static com.mcoin.mall.util.McoinMall.*;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class FeedsJobServiceImpl implements FeedsJobService {
    @Resource
    private FookProductManualRecommendDao fookProductManualRecommendDao;

    @Resource
    private RedissonClient redissonClient;

    @Value("${job.feeds.lock.update.cache.seconds:10}")
    int lockUpdateCacheSeconds;

    @Resource
    private RedisCacheManager cacheManager;

    @Override
    public void doUpdateRecommendCache(UpdateRecommendCacheRequest request) {
        RLock lock = redissonClient.getLock("FeedsJobServiceImpl.doUpdateRecommendCache");
        try {
            if (lock.tryLock(lockUpdateCacheSeconds, TimeUnit.SECONDS)) {
                // 1. 查询手动排序数据
                List<RecommendProductBo> manualRecommends = fookProductManualRecommendDao
                        .selectRecommendProductBos(new Date());
                // 2. 拷贝“综合”类型下“最新”的缓存到“推荐”tab下
                copyCreateTimeToRecommendCache("");
                // 3. 拷贝“综合”类型下“最新”的缓存到harmony“推荐”tab下
                copyCreateTimeToRecommendHarmonyCache("");
                // 4. 更新“综合”类型手动排序数据，支持100年内福利排序不乱
                long currentTimeMillis = System.currentTimeMillis() + (31536000000L * 100);
                for (int i = 0; i < manualRecommends.size(); i++) {
                    RScoredSortedSet<Integer> recommendSet = redissonClient.getScoredSortedSet(PRODUCT_RECOMMEND_KEY_PREFIX);
                    RecommendProductBo recommendProductBo = manualRecommends.get(i);
                    recommendSet.add((double)currentTimeMillis - i, recommendProductBo.getProductId());
                    if (recommendProductBo.getType() != BusinessProductTypeEnum.PHYSICAL_LINK.getTypeId()) {
                        RScoredSortedSet<Integer> recommendHarmonySet = redissonClient.getScoredSortedSet(
                                PRODUCT_RECOMMEND_KEY_PREFIX + FEEDS_HARMONY_SUFFIX);
                        recommendHarmonySet.add((double)currentTimeMillis - i, recommendProductBo.getProductId());
                    }
                }
                log.info("add manual recommend to zset_product_recommend_{} size: {}", "", manualRecommends.size());
                log.info("add manual recommend to zset_product_recommend_harmony_{} size: {}", "",
                        (int) manualRecommends.stream().filter(r -> r.getType() !=
                                BusinessProductTypeEnum.PHYSICAL_LINK.getTypeId()).count());
                // 5. 清除开关缓存
                if (request.getClearSwitchCache()) {
                    Cache cache = cacheManager.getCache(CACHE_STORES_TYPE);
                    if (cache != null) {
                        cache.clear();
                        log.info("清除开关缓存 {}: {}", CACHE_STORES_TYPE, cache.getName());
                    }
                }
            } else {
                throw new RetryException("加锁失败");
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new RuntimeException(e);
        } finally {
            lock.unlock();
        }
    }

    private void copyCreateTimeToRecommendCache(String typeId){
        // 查询“最新”tab的缓存数据
        RScoredSortedSet<Integer> typeSet = redissonClient.getScoredSortedSet(PRODUCT_CREATE_TIME_KEY_PREFIX + typeId);
        Collection<ScoredEntry<Integer>> scoredEntries = typeSet.entryRange(0, -1);
        log.info("get zset_product_created_time_{} size: {}", typeId, scoredEntries.size());
        // 临时插入“推荐”tab的缓存数据，用于后续删除
        String recommendKey = PRODUCT_RECOMMEND_KEY_PREFIX + typeId;
        // 临时key加上{}保证分配的slot是同一个
        RScoredSortedSet<Integer> recommendSetTmp = redissonClient.getScoredSortedSet("{" + recommendKey + "}_tmp");
        boolean tmpDelete = recommendSetTmp.delete();
        log.info("delete {zset_product_recommend_}{}_tmp: {}", typeId, tmpDelete);
        // 插入“推荐”tab的缓存数据
        for (ScoredEntry<Integer> scoredEntry : scoredEntries) {
            recommendSetTmp.add(scoredEntry.getScore(), scoredEntry.getValue());
        }
        // 删除 “推荐”tab的缓存数据
        RScoredSortedSet<Integer> recommendSet = redissonClient.getScoredSortedSet(recommendKey);
        boolean delete = recommendSet.delete();
        // 将临时数据移动到“推荐”tab
        recommendSetTmp.rename(recommendKey);
        log.info("delete zset_product_recommend_{}: {}", typeId, delete);
        log.info("add zset_product_recommend_{} size: {}", typeId, recommendSet.size());
    }

    private void copyCreateTimeToRecommendHarmonyCache(String typeId){
        // 查询“最新”tab的缓存数据
        RScoredSortedSet<Integer> typeSet = redissonClient.getScoredSortedSet(
                PRODUCT_CREATE_TIME_KEY_PREFIX + FEEDS_HARMONY_SUFFIX + typeId);
        Collection<ScoredEntry<Integer>> scoredEntries = typeSet.entryRange(0, -1);
        log.info("get zset_product_created_time_harmony_{} size: {}", typeId, scoredEntries.size());
        // 临时插入“推荐”tab的缓存数据，用于后续删除
        String recommendKey = PRODUCT_RECOMMEND_KEY_PREFIX + FEEDS_HARMONY_SUFFIX + typeId;
        // 临时key加上{}保证分配的slot是同一个
        RScoredSortedSet<Integer> recommendSetTmp = redissonClient.getScoredSortedSet("{" + recommendKey + "}_tmp");
        boolean tmpDelete = recommendSetTmp.delete();
        log.info("delete {zset_product_recommend_harmony_}{}_tmp: {}", typeId, tmpDelete);
        // 插入“推荐”tab的缓存数据
        for (ScoredEntry<Integer> scoredEntry : scoredEntries) {
            recommendSetTmp.add(scoredEntry.getScore(), scoredEntry.getValue());
        }
        // 删除 “推荐”tab的缓存数据
        RScoredSortedSet<Integer> recommendSet = redissonClient.getScoredSortedSet(recommendKey);
        boolean delete = recommendSet.delete();
        // 将临时数据移动到“推荐”tab
        recommendSetTmp.rename(recommendKey);
        log.info("delete zset_product_recommend_harmony_{}: {}", typeId, delete);
        log.info("add zset_product_recommend_harmony_{} size: {}", typeId, recommendSet.size());
    }

    @Override
    public int getRecommendTabDataSize(String typeId) {
        RScoredSortedSet<Integer> recommendSet =
                redissonClient.getScoredSortedSet(PRODUCT_RECOMMEND_KEY_PREFIX + typeId);
        return recommendSet.size();
    }
}
