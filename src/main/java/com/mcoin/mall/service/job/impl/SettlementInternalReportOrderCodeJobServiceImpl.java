package com.mcoin.mall.service.job.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.mcoin.mall.bean.FookReportSettlementData;
import com.mcoin.mall.component.ExplicitTransaction;
import com.mcoin.mall.dao.FookReportSettlementDataDao;
import com.mcoin.mall.dao.SettingsDao;
import com.mcoin.mall.model.job.SettlementInternalReportOrderCodeJobRequest;
import com.mcoin.mall.service.job.SettlementInternalReportOrderCodeJobService;
import com.mcoin.mall.util.PageUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.ExecutorType;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

import static cn.hutool.core.text.CharSequenceUtil.blankToDefault;
import static com.mcoin.mall.util.JodaTimeUtil.*;
import static java.lang.Integer.parseInt;

@Slf4j
@Service
public class SettlementInternalReportOrderCodeJobServiceImpl implements SettlementInternalReportOrderCodeJobService {
    @Resource
    private ThreadPoolTaskExecutor taskExecutor;
    @Resource
    private ExplicitTransaction longQueryReadOnlyTransaction;
    @Resource
    private ExplicitTransaction batchTransaction;
    @Resource
    private FookReportSettlementDataDao fookReportSettlementDataDao;
    @Resource
    private SettingsDao settingsDao;
    @Resource
    private SqlSessionFactory sqlSessionFactory;
    @Override
    public void doSettlement(SettlementInternalReportOrderCodeJobRequest request) {
        taskExecutor.execute(()->{
            try {
                Date start = getDateWithoutTime(request.getCurrentDate());
                //结算一天的核销数据
                Date uploadEndTime = plusSecondToDate(plusDayToDate(start, 1), -1);
                int insertedCount = longQueryReadOnlyTransaction.invokeWithNewTransaction(()->
                        fookReportSettlementDataDao.selectCount(new LambdaQueryWrapper<FookReportSettlementData>()
                                .between(FookReportSettlementData::getUploadTime, start, uploadEndTime)
                        ));
                if (insertedCount > 0) {
                    int deletedCount = batchTransaction.invokeWithNewTransaction(()->fookReportSettlementDataDao.delete(
                            new LambdaQueryWrapper<FookReportSettlementData>()
                                    .between(FookReportSettlementData::getUploadTime, start, uploadEndTime)
                    ));
                    log.info("已生成{}条核销数据，删除{}条", insertedCount, deletedCount);
                }
                //结算一天的核销数据
                Date startTime = plusDayToDate(start, -1);
                Date endTime = plusSecondToDate(start, -1);
                int pageSize = parseInt(blankToDefault(settingsDao.getValue("site.mpass_settlement_page_size"), "1000"));
                int totalCount = longQueryReadOnlyTransaction.invokeWithNewTransaction(()->
                        fookReportSettlementDataDao.countSettlementData(startTime, endTime));
                log.info("{} ~ {} 之间的核销数据条数：{}", format(startTime), format(endTime), totalCount);
                if (totalCount > 0) {
                    int pageNum = PageUtil.getPageNum(totalCount, pageSize);
                    log.info("数据总数：{}，每页：{}，共{}页", totalCount, pageSize, pageNum);
                    for (int i = 0; i < pageNum; i++) {
                        int offset = i * pageSize;
                        List<FookReportSettlementData> rtList = longQueryReadOnlyTransaction.invokeWithNewTransaction(()->
                                fookReportSettlementDataDao.selectSettlementDataByPage(startTime, endTime, offset, pageSize));
                        log.info("第{}页共{}条核销数据", i+ 1, rtList.size());
                        batchTransaction.invokeWithNewTransaction(()->{
                            try(SqlSession session = sqlSessionFactory.openSession(ExecutorType.BATCH)) {
                                FookReportSettlementDataDao mapper = session.getMapper(FookReportSettlementDataDao.class);
                                rtList.forEach(e->{
                                    e.setUploadTime(start);
                                    mapper.insert(e);
                                });
                                session.commit();
                            }
                        });
                    }
                }
            } catch (Exception e) {
                log.error("结算失败", e);
            }
        });
    }
}
