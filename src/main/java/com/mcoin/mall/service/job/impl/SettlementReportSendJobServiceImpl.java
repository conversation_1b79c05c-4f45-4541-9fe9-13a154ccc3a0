package com.mcoin.mall.service.job.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.mcoin.mall.bean.FookBusiness;
import com.mcoin.mall.bean.FookReportMerchantSettlement;
import com.mcoin.mall.bean.FookReportMerchantSettlementProcesss;
import com.mcoin.mall.component.ExplicitTransaction;
import com.mcoin.mall.constant.IsAlipayPlus;
import com.mcoin.mall.constant.SettlementSend;
import com.mcoin.mall.constant.SettlementUpload;
import com.mcoin.mall.dao.FookBusinessDao;
import com.mcoin.mall.dao.FookReportMerchantSettlementDao;
import com.mcoin.mall.dao.FookReportMerchantSettlementProcesssDao;
import com.mcoin.mall.dao.SettingsDao;
import com.mcoin.mall.exception.BusinessException;
import com.mcoin.mall.model.Response;
import com.mcoin.mall.model.job.SettlementReportSendJobRequest;
import com.mcoin.mall.model.report.SettlementEmailModel;
import com.mcoin.mall.mq.model.SettlementReportSendMessage;
import com.mcoin.mall.service.common.AliyunOSSService;
import com.mcoin.mall.service.job.SettlementReportSendJobService;
import com.mcoin.mall.util.ConfigUtils;
import com.mcoin.mall.util.OssUtil;
import com.mcoin.mall.vo.SettlementReportFileVo;
import freemarker.template.Configuration;
import freemarker.template.Template;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.core.io.FileSystemResource;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.mail.internet.MimeMessage;
import java.io.File;
import java.io.StringWriter;
import java.math.BigDecimal;
import java.time.LocalTime;
import java.util.Date;
import java.util.List;

import static com.mcoin.mall.util.JodaTimeUtil.format;
import static com.mcoin.mall.util.JodaTimeUtil.plusMinuteToDate;
import static java.lang.Integer.parseInt;

@Service
@Slf4j
public class SettlementReportSendJobServiceImpl implements SettlementReportSendJobService {

    @Resource
    private FookReportMerchantSettlementProcesssDao fookReportMerchantSettlementProcesssDao;
    @Resource
    private FookReportMerchantSettlementDao fookReportMerchantSettlementDao;
    @Resource
    private FookBusinessDao fookBusinessDao;
    @Resource
    private RabbitTemplate settlementReportSendTemplate;
    @Resource
    private AliyunOSSService aliyunOSSService;
    @Resource
    private JavaMailSender emailSender;
    @Resource
    private Configuration freeMarkerConfiguration;
    @Resource
    private ExplicitTransaction transaction;
    @Resource
    private SettingsDao settingsDao;

    @Override
    public void triggerSend(SettlementReportSendJobRequest request) {

        int backMinutes = parseInt(ConfigUtils.getProperty("job.settlement.report.send.back.minutes", "0"));
        int maxMinutes = parseInt(ConfigUtils.getProperty("job.settlement.report.send.max.minutes", "4320"));
        int sendMailTime = parseInt(StringUtils.defaultString(settingsDao.getValue("site.sendmail_time"), "1"));
        Date endDate = plusMinuteToDate(request.getCurrentTime(), backMinutes);
        Date startDate = plusMinuteToDate(endDate, -maxMinutes);
        List<FookReportMerchantSettlementProcesss> settlementProcessses =
                fookReportMerchantSettlementProcesssDao.selectList(
                        new LambdaQueryWrapper<FookReportMerchantSettlementProcesss>()
                                .eq(FookReportMerchantSettlementProcesss::getSend, SettlementSend.NO.ordinal())
                                .isNotNull(FookReportMerchantSettlementProcesss::getEmail)
                                .ne(FookReportMerchantSettlementProcesss::getEmail, "")
                                .eq(FookReportMerchantSettlementProcesss::getUpload, SettlementUpload.SUCCESS.ordinal())
                                .between(FookReportMerchantSettlementProcesss::getCreatetime, startDate, endDate)
                                .last("limit " + sendMailTime)
                );
        for (FookReportMerchantSettlementProcesss processs: settlementProcessses) {
            SettlementReportSendMessage message = new SettlementReportSendMessage();
            message.setSettlementProcessId(processs.getId());
            message.setSettlementId(processs.getSettlementid());
            Message msg = new Message(JSON.toJSONBytes(message));
            settlementReportSendTemplate.convertAndSend(msg);
            log.info("发送结算发送报表延时（{}ms）消息：{}", message.getDelayTime(), new String(msg.getBody()));
        }
    }

    @Override
    public void doSend(SettlementReportSendMessage message) {
        LocalTime now = LocalTime.now();
        String sendStart = ConfigUtils.getProperty("job.email.send.start", "9:0");
        String sendEnd = ConfigUtils.getProperty("job.email.send.end", "18:0");
        String[] startHourMinute = sendStart.split(":");
        String[] endHourMinute = sendEnd.split(":");
        LocalTime start = LocalTime.of(Integer.parseInt(startHourMinute[0]), Integer.parseInt(startHourMinute[1]));
        LocalTime end = LocalTime.of(Integer.parseInt(endHourMinute[0]), Integer.parseInt(endHourMinute[1]));
        if (!(now.isAfter(start) && now.isBefore(end))) {
            log.info("当前时间不在{}到{}之间,message:{}", sendStart, sendEnd, message);
            return;
        }
        try {
            // 查询结算信息
            FookReportMerchantSettlement settlement = fookReportMerchantSettlementDao.
                    selectByPrimaryKey(message.getSettlementId());
            if (settlement == null) {
                throw new BusinessException(Response.Code.BAD_REQUEST, "结算信息不存在");
            }
            FookBusiness business = fookBusinessDao.selectByPrimaryKey(settlement.getSellerId());
            if (business == null) {
                throw new BusinessException(Response.Code.BAD_REQUEST, "商户信息不存在");
            }
            FookReportMerchantSettlementProcesss processs = fookReportMerchantSettlementProcesssDao
                    .selectByPrimaryKey(message.getSettlementProcessId());
            if (processs == null || processs.getSend() == null ||
                    SettlementSend.YES.ordinal() == processs.getSend()) {
                throw new BusinessException(Response.Code.BAD_REQUEST, "文件处理记录不存在或发送状态非法");
            }
            if (StringUtils.isBlank(processs.getEmail())) {
                throw new BusinessException(Response.Code.BAD_REQUEST, "收件地址不能为空");
            }
            if (ObjectUtil.isEmpty(settlement.getPdfUrl())) {
                throw new BusinessException(Response.Code.BAD_REQUEST, "PdfUrl为空");
            }
            if (ObjectUtil.isEmpty(settlement.getExcelUrl())) {
                throw new BusinessException(Response.Code.BAD_REQUEST, "ExcelUrl为空");
            }
            if (settlement.getIsA() == IsAlipayPlus.YES.ordinal()
                    && settlement.getBillAmount().compareTo(BigDecimal.ZERO) == 0
                    && settlement.getSettlementAmount().compareTo(BigDecimal.ZERO) == 0
                    && settlement.getCommission().compareTo(BigDecimal.ZERO) == 0
                    && settlement.getMomecoins().compareTo(BigDecimal.ZERO) == 0) {
                log.info("A+券结算金额为0，不发送邮件");
                // 更新發送郵件成功
                transaction.invokeWithNewTransaction(()-> {
                    FookReportMerchantSettlementProcesss updProcess = new FookReportMerchantSettlementProcesss();
                    updProcess.setSendtime(new Date());
                    updProcess.setSend(SettlementSend.NOT_SEND.ordinal());
                    fookReportMerchantSettlementProcesssDao.update(updProcess,
                            new LambdaQueryWrapper<FookReportMerchantSettlementProcesss>()
                                    .eq(FookReportMerchantSettlementProcesss::getId, message.getSettlementProcessId())
                    );
                });
                return;
            }
            SettlementReportFileVo fileVo = OssUtil.getReportOssPath(settlement.getStartTime(), settlement.getIsA(),
                    business.getCode());
            String ossPath = fileVo.getOssPath();
            String savePath = ConfigUtils.getProperty("report.savePath", "files/report/") + ossPath + "/download";
            String pdfPath = ossPath + "/" + settlement.getPdfUrl();
            log.info("downloading pdf file from oss path:{}", pdfPath);
            File pdfFile = aliyunOSSService.downloadFile(pdfPath, savePath, settlement.getPdfUrl());
            if (pdfFile == null) {
                throw new BusinessException(Response.Code.BAD_REQUEST, "pdf文件" + pdfPath + "为空");
            }
            log.info("downloaded pdf file save to:{}", pdfFile.getAbsolutePath());
            String excelPath = ossPath + "/" + settlement.getExcelUrl();
            log.info("downloading excel file from oss path:{}", excelPath);
            File excelFile = aliyunOSSService.downloadFile(excelPath, savePath, settlement.getExcelUrl());
            if (excelFile == null) {
                throw new BusinessException(Response.Code.BAD_REQUEST, "excel文件" + excelPath + "为空");
            }
            log.info("downloaded excel file save to:{}", excelFile.getAbsolutePath());
            String[] emaillist = ObjectUtil.defaultIfBlank(processs.getEmail(), "").split(";");
            String startTime = format(settlement.getStartTime(), "yyyy-MM-dd");
            String endTime = format(settlement.getEndTime(), "yyyy-MM-dd");
            String is_a_open = settlement.getIsA() == 1?"（A+券）":"";
            String subject = business.getCode()  + " " + business.getName() + " - 商戶對賬結算報表" + is_a_open +
                    " (" + startTime + " - " + endTime + ")";
            String file_pdf_type = ".pdf";
            String excel_name_type = "";
            String pdf_name_type = "";
            if (settlement.getPdfUrl().endsWith(".zip")) {
                file_pdf_type = ".zip";
                pdf_name_type = "-PDF";
            }
            String file_excel_type = ".xls";
            if(settlement.getExcelUrl().endsWith(".zip")){
                file_excel_type = ".zip";
                excel_name_type = "-EXCEL";
            }
            String pdfAttachName = business.getCode() + " " + business.getName() + " - 商戶對賬結算報表" +
                    is_a_open + pdf_name_type + " (" + startTime + " - " + endTime + ")" + file_pdf_type;
            String excelAttachName = business.getCode() + " " + business.getName() + " - 商戶對賬結算報表" +
                    is_a_open + excel_name_type + " (" + startTime + " - " + endTime + ")" + file_excel_type;
            Template template = freeMarkerConfiguration.getTemplate("mail/business-settlement.ftl", "UTF-8");
            String html;
            try(StringWriter sw = new StringWriter()){
                template.process(new SettlementEmailModel()
                        .setStartDate(format(settlement.getStartTime(), "yyyy-MM-dd HH:mm:ss"))
                        .setEndDate(format(settlement.getEndTime(), "yyyy-MM-dd HH:mm:ss")), sw);
                html = sw.toString();
            }
            MimeMessage mimeMessage = emailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(mimeMessage, true);
            helper.setFrom(ConfigUtils.getProperty("spring.mail.username"));
            helper.setTo(emaillist);
            helper.setSubject(subject);
            helper.setText(html, true);
            helper.addAttachment(pdfAttachName, new FileSystemResource(pdfFile));
            helper.addAttachment(excelAttachName, new FileSystemResource(excelFile));
            emailSender.send(mimeMessage);
            // 更新發送郵件成功
            transaction.invokeWithNewTransaction(()-> {
                FookReportMerchantSettlementProcesss updProcess = new FookReportMerchantSettlementProcesss();
                updProcess.setSendtime(new Date());
                updProcess.setSend(SettlementSend.YES.ordinal());
                fookReportMerchantSettlementProcesssDao.update(updProcess,
                        new LambdaQueryWrapper<FookReportMerchantSettlementProcesss>()
                                .eq(FookReportMerchantSettlementProcesss::getId, message.getSettlementProcessId())
                );
            });

            boolean delRt = FileUtils.deleteQuietly(pdfFile);
            log.info("删除本地文件：{}, rt:{}", pdfFile.getAbsolutePath(), delRt);
            delRt = FileUtils.deleteQuietly(excelFile);
            log.info("删除本地文件：{}, rt:{}", excelFile.getAbsolutePath(), delRt);

        } catch (Exception e) {
            log.error("發送郵件失敗", e);
            // 更新發送郵件失敗
            transaction.invokeWithNewTransaction(()-> {
                FookReportMerchantSettlementProcesss updProcess = new FookReportMerchantSettlementProcesss();
                updProcess.setSendtime(new Date());
                updProcess.setSend(SettlementSend.ERROR.ordinal());
                fookReportMerchantSettlementProcesssDao.update(updProcess,
                        new LambdaQueryWrapper<FookReportMerchantSettlementProcesss>()
                                .eq(FookReportMerchantSettlementProcesss::getId, message.getSettlementProcessId())
                                .ne(FookReportMerchantSettlementProcesss::getSend, SettlementSend.YES.ordinal())
                );
            });
        }
    }
}
