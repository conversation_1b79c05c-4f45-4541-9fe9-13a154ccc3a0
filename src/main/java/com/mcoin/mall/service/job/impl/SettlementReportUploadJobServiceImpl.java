package com.mcoin.mall.service.job.impl;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.mcoin.mall.bean.FookBusiness;
import com.mcoin.mall.bean.FookReportMerchantSettlement;
import com.mcoin.mall.bean.FookReportMerchantSettlementProcesss;
import com.mcoin.mall.bo.SettlementReportDetailBo;
import com.mcoin.mall.component.ClasspathResourceLoader;
import com.mcoin.mall.component.ExplicitTransaction;
import com.mcoin.mall.config.properties.ReportSettlementTemplateConfiguration;
import com.mcoin.mall.config.properties.ReportSettlementTemplateProperties;
import com.mcoin.mall.constant.SettlementUpload;
import com.mcoin.mall.dao.FookBusinessDao;
import com.mcoin.mall.dao.FookReportMerchantSettlementDao;
import com.mcoin.mall.dao.FookReportMerchantSettlementProcesssDao;
import com.mcoin.mall.exception.BusinessException;
import com.mcoin.mall.model.Response;
import com.mcoin.mall.model.job.SettlementReportUploadJobRequest;
import com.mcoin.mall.model.report.SettlementExcelItemModel;
import com.mcoin.mall.model.report.SettlementExcelReportModel;
import com.mcoin.mall.model.report.SettlementPdfReportModel;
import com.mcoin.mall.mq.model.SettlementReportUploadMessage;
import com.mcoin.mall.service.job.SettlementReportUploadJobService;
import com.mcoin.mall.service.job.SettlementReportUploadService;
import com.mcoin.mall.util.ConfigUtils;
import com.mcoin.mall.util.JodaTimeUtil;
import com.mcoin.mall.util.MoneyUtil;
import com.mcoin.mall.util.OssUtil;
import com.mcoin.mall.vo.SettlementReportFileVo;
import freemarker.template.TemplateException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static cn.hutool.core.util.ObjectUtil.defaultIfEmpty;
import static cn.hutool.core.util.ObjectUtil.defaultIfNull;
import static com.mcoin.mall.service.job.utils.SettlementUtil.onlyKeepMiniOrderNo;
import static com.mcoin.mall.util.JodaTimeUtil.format;
import static com.mcoin.mall.util.JodaTimeUtil.plusMinuteToDate;
import static java.lang.Integer.parseInt;
import static java.math.BigDecimal.ZERO;
@Service
@Slf4j
public class SettlementReportUploadJobServiceImpl implements SettlementReportUploadJobService {

    @Resource
    private FookReportMerchantSettlementProcesssDao fookReportMerchantSettlementProcesssDao;
    @Resource
    private FookReportMerchantSettlementDao fookReportMerchantSettlementDao;
    @Resource
    private FookBusinessDao fookBusinessDao;
    @Resource
    private RabbitTemplate settlementReportUploadTemplate;
    @Resource
    private ExplicitTransaction transaction;
    @Resource
    private ExplicitTransaction longQueryReadOnlyTransaction;

    private static final String SETTLEMENT_XLS = "classpath:templates/excel/business-settlement.xls";
    @Value(SETTLEMENT_XLS)
    private org.springframework.core.io.Resource settlementTemplate;
    @Resource
    private SettlementReportUploadService settlementReportUploadService;
    @Resource
    private ReportSettlementTemplateProperties reportSettlementTemplateProperties;
    @Resource
    private ClasspathResourceLoader classpathResourceLoader;

    @Override
    public void triggerUpload(SettlementReportUploadJobRequest request) {
        int backMinutes = parseInt(ConfigUtils.getProperty("job.settlement.report.upload.back.minutes", "-120"));
        int maxMinutes = parseInt(ConfigUtils.getProperty("job.settlement.report.upload.max.minutes", "4320"));
        Date endDate = plusMinuteToDate(request.getCurrentTime(), backMinutes);
        Date startDate = plusMinuteToDate(endDate, -maxMinutes);
        List<FookReportMerchantSettlementProcesss> settlementProcessses =
                fookReportMerchantSettlementProcesssDao.selectList(
                        new LambdaQueryWrapper<FookReportMerchantSettlementProcesss>()
                            .in(FookReportMerchantSettlementProcesss::getUpload,
                                    SettlementUpload.NOT.getStatus(), SettlementUpload.UPLOADING.getStatus()
                            )
                            .between(FookReportMerchantSettlementProcesss::getCreatetime, startDate, endDate)
        );
        for (FookReportMerchantSettlementProcesss processs: settlementProcessses) {
            SettlementReportUploadMessage message = new SettlementReportUploadMessage();
            message.setSettlementProcessId(processs.getId());
            message.setSettlementId(processs.getSettlementid());
            Message msg = new Message(JSON.toJSONBytes(message));
            settlementReportUploadTemplate.convertAndSend(msg);
            log.info("发送结算上传报表延时（{}ms）消息：{}", message.getDelayTime(), new String(msg.getBody()));
        }
    }

    @Override
    public void doUpload(SettlementReportUploadMessage message) {
        // 查询结算信息
        FookReportMerchantSettlement settlement = fookReportMerchantSettlementDao.
                selectByPrimaryKey(message.getSettlementId());
        if (settlement == null) {
            throw new BusinessException(Response.Code.BAD_REQUEST, "结算信息不存在");
        }
        FookReportMerchantSettlementProcesss processs = fookReportMerchantSettlementProcesssDao
                .selectByPrimaryKey(message.getSettlementProcessId());
        if (processs == null || processs.getUpload() == null ||
                SettlementUpload.SUCCESS.getStatus() == processs.getUpload()) {
            throw new BusinessException(Response.Code.BAD_REQUEST, "文件处理记录不存在或上传状态非法");
        }
        FookBusiness business = fookBusinessDao.selectByPrimaryKey(settlement.getSellerId());
        if (business == null) {
            throw new BusinessException(Response.Code.BAD_REQUEST, "商户信息不存在");
        }
        // 更新状态
        transaction.invokeWithNewTransaction(()->{
            FookReportMerchantSettlementProcesss updProcess = new FookReportMerchantSettlementProcesss();
            updProcess.setUpload(SettlementUpload.UPLOADING.getStatus());
            updProcess.setUploadtime(new Date());
            int rt = fookReportMerchantSettlementProcesssDao.update(updProcess,
                    new LambdaQueryWrapper<FookReportMerchantSettlementProcesss>()
                            .eq(FookReportMerchantSettlementProcesss::getId, message.getSettlementProcessId())
                            .in(FookReportMerchantSettlementProcesss::getUpload,
                                    SettlementUpload.NOT.getStatus(), SettlementUpload.UPLOADING.getStatus())
            );
            if (rt != 1) {
                throw new BusinessException(Response.Code.BAD_REQUEST, "更新上传状态为处理中失败");
            }
        });
        SettlementReportFileVo fileVo = OssUtil.getReportOssPath(settlement.getStartTime(), settlement.getIsA(),
                business.getCode());
        String ossPath = fileVo.getOssPath();
        String filename = fileVo.getFilename();
        // 查询报表明细
        List<SettlementReportDetailBo> reportDetailBos = longQueryReadOnlyTransaction.invokeWithNewTransaction(()->
                fookReportMerchantSettlementDao.selectSettlementReportDetail(settlement.getId()));
        // 设置开始和结束时间
        BigDecimal billAmountSum = ZERO;
        BigDecimal commissionSum = ZERO;
        BigDecimal merchantSettleAmountSum = ZERO;
        for (SettlementReportDetailBo detailBo: reportDetailBos) {
            detailBo.setStartTime(settlement.getStartTime());
            detailBo.setEndTime(settlement.getEndTime());
            detailBo.setCommission(defaultIfNull(detailBo.getCommission(), ZERO).negate());
            billAmountSum = billAmountSum.add(defaultIfNull(detailBo.getBillAmount(), ZERO));
            commissionSum = commissionSum.add(defaultIfNull(detailBo.getCommission(), ZERO));
            merchantSettleAmountSum = merchantSettleAmountSum.add(defaultIfNull(detailBo.getMerchantSettleAmount(), ZERO));
        }
        // 创建pdf
        String pdfUrl = null;
        try {
            pdfUrl = createPdfAndUploadOss(settlement, business, billAmountSum, merchantSettleAmountSum,
                    reportDetailBos, ossPath, filename);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        // 创建excel
        String excelUrl = null;
        try {
            excelUrl = createExcelAndUploadOss(settlement, business, billAmountSum, commissionSum,
                    merchantSettleAmountSum, reportDetailBos, ossPath, filename);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        log.info("更新数据表状态和路径,pdfUrl:{}, excelUrl:{}", pdfUrl, excelUrl);
        String finalPdfUrl = pdfUrl;
        String finalExcelUrl = excelUrl;
        transaction.invokeWithNewTransaction(()->{
            FookReportMerchantSettlement updSettlement = new FookReportMerchantSettlement();
            updSettlement.setPdfUrl(finalPdfUrl);
            updSettlement.setExcelUrl(finalExcelUrl);
            fookReportMerchantSettlementDao.update(updSettlement,
                    new LambdaQueryWrapper<FookReportMerchantSettlement>()
                            .eq(FookReportMerchantSettlement::getId, settlement.getId())
            );
            FookReportMerchantSettlementProcesss updProcess = new FookReportMerchantSettlementProcesss();
            updProcess.setUploadtime(new Date());
            updProcess.setUpload(SettlementUpload.SUCCESS.getStatus());
            fookReportMerchantSettlementProcesssDao.update(updProcess,
                    new LambdaQueryWrapper<FookReportMerchantSettlementProcesss>()
                            .eq(FookReportMerchantSettlementProcesss::getId, message.getSettlementProcessId())
            );
        });
    }

    private String createPdfAndUploadOss(FookReportMerchantSettlement settlement, FookBusiness business,
                                       BigDecimal billAmountSum,
                                       BigDecimal merchantSettleAmountSum,
                                       List<SettlementReportDetailBo> reportDetailBos,
                                       String ossPath,
                                       String filename) throws IOException, TemplateException {
        ReportSettlementTemplateConfiguration configuration = getTpl(business);
        String pdfPath = defaultIfEmpty(getPdfTplPath(configuration), "pdf/business-settlement.ftl");
        log.info("pdf template path: {}", pdfPath);
        return settlementReportUploadService.doCreatePdfAndUploadOss(settlement.getId(), reportDetailBos, ossPath, filename,
                pdfPath, (detailBos, pageCountStart, isLastPage)->{
                    // 生成pdf
                    Integer is_a_open = defaultIfNull(settlement.getIsA(), 0);
                    String business_name = business.getName() + (is_a_open==1?"（A+券）":"");
                    String head_email = business.getHeadEmail();
                    String code = business.getCode();
                    // 只保留小程序订单
                    onlyKeepMiniOrderNo(configuration, detailBos);
                    return new SettlementPdfReportModel()
                            .setIsA(is_a_open)
                            .setCode(code)
                            .setCurrentDate(new Date())
                            .setBusinessName(business_name)
                            .setHeadEmail(head_email)
                            .setStartTime(settlement.getStartTime())
                            .setEndTime(settlement.getEndTime())
                            .setPageCountStart(pageCountStart)
                            .setBillAmountSum(billAmountSum)
                            .setMerchantSettleAmountSum(merchantSettleAmountSum)
                            .setIsLastPage(isLastPage?1:0)
                            .setDataList(detailBos);
                });
    }

    private String createExcelAndUploadOss(FookReportMerchantSettlement settlement, FookBusiness business,
                                           BigDecimal billAmountSum,
                                           BigDecimal commissionSum,
                                           BigDecimal merchantSettleAmountSum,
                                           List<SettlementReportDetailBo> reportDetailBos,
                                           String ossPath,
                                           String filename) throws IOException {
        ReportSettlementTemplateConfiguration configuration = getTpl(business);
        org.springframework.core.io.Resource excelTpl = defaultIfNull(getExcelTplResource(configuration), settlementTemplate);
        log.info("excel template path: {}", excelTpl.getURI());
        return settlementReportUploadService.doCreateExcelAndUploadOss(settlement.getId(), reportDetailBos, ossPath,
                filename, excelTpl, (detailBos, sn, isLastPage)->{
                    Integer is_a_open = defaultIfNull(settlement.getIsA(), 0);
                    String business_name = business.getName() + (is_a_open == 1 ? "（A+券）" : "");
                    String head_email = business.getHeadEmail();
                    String code = business.getCode();
                    // 只保留小程序订单
                    onlyKeepMiniOrderNo(configuration, detailBos);
                    SettlementExcelReportModel excelReport = new SettlementExcelReportModel()
                            .setCode(code)
                            .setCurrentDate(JodaTimeUtil.format(new Date(), "yyyy-MM-dd"))
                            .setBusinessName(business_name)
                            .setHeadEmail(head_email);
                    List<SettlementExcelItemModel> excelItems = getExcelItem(sn, detailBos);
                    // 最后一页
                    if (isLastPage) {
                        excelItems.add(new SettlementExcelItemModel()
                                .setSn(null)
                                .setType(null)
                                .setOrderNo(null)
                                .setOrderTransaction(null)
                                .setVoucherCode("總金額:")
                                .setBillAmount(defaultIfNull(billAmountSum, ZERO))
                                .setCommission(defaultIfNull(commissionSum, ZERO))
                                .setMerchantSettleAmount(defaultIfNull(merchantSettleAmountSum, ZERO))
                                .setUseTime(null)
                                .setStoreName(null)
                                .setVoucherName(null)
                                .setDeliveryType(null)
                                .setProductPrice(null)
                                .setStartTime(null)
                                .setEndTime(null)
                                .setStoreMid(null)
                                .setTrackingNo(null)
                                .setBusinessRedeemTime(null)
                        );
                    }
                    excelReport.setDataList(excelItems);
                    return excelReport;
        });
    }

    private List<SettlementExcelItemModel> getExcelItem(int sn, List<SettlementReportDetailBo> reportDetailBos){
        List<SettlementExcelItemModel> excelItems = new ArrayList<>(reportDetailBos.size());
        for (SettlementReportDetailBo detailBo: reportDetailBos) {
            String typeStr = "--";
            Integer type = defaultIfNull(detailBo.getType(), -1);
            if (type == 1) {
                typeStr = "福利";
            } else if (type == 2) {
                typeStr = "預約";
            } else if (type == 3) {
                typeStr = "特殊福利";
            } else if (type == 4) {
                typeStr = "實物鏈路";
            }
            String deliveryTypeStr = "--";
            Integer deliveryType = defaultIfNull(detailBo.getDeliveryType(), -1);
            if (deliveryType == 1) {
                deliveryTypeStr = "物流";
            } else if (deliveryType == 2) {
                deliveryTypeStr = "自提";
            } else if (deliveryType == 3) {
                deliveryTypeStr = "同城";
            } else if (deliveryType == 4) {
                deliveryTypeStr = "虛擬";
            }
            excelItems.add(new SettlementExcelItemModel()
                    .setSn(sn++)
                    .setType(typeStr)
                    .setOrderNo(detailBo.getOrderNo())
                    .setOrderTransaction(detailBo.getOrderTransaction())
                    .setVoucherCode(detailBo.getVoucherCode())
                    .setBillAmount(defaultIfNull(detailBo.getBillAmount(), ZERO))
                    .setCommission(defaultIfNull(detailBo.getCommission(), ZERO))
                    .setMerchantSettleAmount(defaultIfNull(detailBo.getMerchantSettleAmount(), ZERO))
                    .setUseTime(format(detailBo.getUseTime()))
                    .setStoreName(detailBo.getStoreName())
                    .setVoucherName(detailBo.getVoucherName())
                    .setDeliveryType(deliveryTypeStr)
                    .setProductPrice(detailBo.getProductPrice() == null?"--":MoneyUtil.format(detailBo.getProductPrice()))
                    .setStartTime(format(detailBo.getStartTime()))
                    .setEndTime(format(detailBo.getEndTime()))
                    .setStoreMid(defaultIfEmpty(detailBo.getStoreMid(), "--"))
                    .setTrackingNo(detailBo.getTrackingNo())
                    .setBusinessRedeemTime(DateUtil.formatDateTime(detailBo.getBusinessRedeemTime()))
            );
        }
        return excelItems;
    }

    private ReportSettlementTemplateConfiguration getTpl(FookBusiness business){
        log.info("templates:{}", JSON.toJSONString(reportSettlementTemplateProperties.getTemplate()));
        return reportSettlementTemplateProperties.getTemplate().get(business.getId() + "");
    }

    private String getPdfTplPath(ReportSettlementTemplateConfiguration config){
        if (config != null && StringUtils.isNotBlank(config.getPdfPath())) {
            return config.getPdfPath();
        }
        return null;
    }

    private org.springframework.core.io.Resource getExcelTplResource(ReportSettlementTemplateConfiguration config){
        if (config != null && StringUtils.isNotBlank(config.getExcelPath())) {
            return classpathResourceLoader.getResource(config.getExcelPath());
        }
        return null;
    }
}
