package com.mcoin.mall.service.job.impl;

import com.google.common.collect.Lists;
import com.itextpdf.html2pdf.ConverterProperties;
import com.itextpdf.html2pdf.HtmlConverter;
import com.itextpdf.html2pdf.resolver.font.DefaultFontProvider;
import com.itextpdf.io.font.FontProgramFactory;
import com.itextpdf.layout.font.FontProvider;
import com.mcoin.mall.bo.SettlementReportDetailBo;
import com.mcoin.mall.dao.SettingsDao;
import com.mcoin.mall.service.common.AliyunOSSService;
import com.mcoin.mall.service.job.SettlementReportUploadService;
import com.mcoin.mall.util.ConfigUtils;
import com.mcoin.mall.util.JodaTimeUtil;
import com.mcoin.mall.util.ZipUtils;
import freemarker.template.Configuration;
import freemarker.template.Template;
import freemarker.template.TemplateException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.jxls.common.Context;
import org.jxls.transform.poi.PoiTransformer;
import org.jxls.util.JxlsHelper;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.*;
import java.nio.file.Files;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static cn.hutool.core.util.ObjectUtil.defaultIfEmpty;
import static java.lang.Integer.parseInt;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class SettlementReportUploadServiceImpl implements SettlementReportUploadService {

    private static final String FONT_TTF = "classpath:fonts/SourceHanSerifHK-VF.ttf";
    @Value(FONT_TTF)
    private org.springframework.core.io.Resource defaultFont;
    @Resource
    private SettingsDao settingsDao;
    @Resource
    private Configuration freeMarkerConfiguration;
    @Resource
    private AliyunOSSService aliyunOSSService;

    @Override
    public <V> String doCreatePdfAndUploadOss(Integer settlementId, List<SettlementReportDetailBo> reportDetailBos,
                                              String ossPath, String filename, String pdfTemplatePath,
                                              PdfModelCallable<V> pdfModelCallable) throws IOException, TemplateException {
        // 创建存储路径
        String savePath = ConfigUtils.getProperty("report.savePath", "files/report/") + ossPath;
        File savePathFile = new File(savePath);
        if (!savePathFile.exists()) {
            boolean rt = savePathFile.mkdirs();
            log.info("创建存储路径:{}, rt: {}", savePath, rt);
        }
        // 分页
        int build_merchant_report_count = parseInt(defaultIfEmpty(settingsDao.getValue("site.build_merchant_report_count"),"1000"));
        log.info("BusinessSettlementPdf 商户结算id:{}, 当前商户的总条数:{}, 开始数据切换, 分页数量：{}",
                settlementId, reportDetailBos.size(), build_merchant_report_count);
        int file_sn = 1;
        int pageCountStart = 0;
        String finalFileName = null;
        File finalFile = null;
        List<File> zipFiles = new ArrayList<>();
        List<List<SettlementReportDetailBo>> detailBoPages =
                Lists.partition(reportDetailBos, build_merchant_report_count);
        log.info("数据切割分页数：{}", detailBoPages.size());
        // 如果切割分页数为0则需要创建为包含一个空数据的分页，保证生成空文件
        if (detailBoPages.isEmpty()) {
            detailBoPages = new ArrayList<>(1);
            detailBoPages.add(Collections.emptyList());
        }

        for (List<SettlementReportDetailBo> detailBos: detailBoPages) {
            V pdfReport = pdfModelCallable.call(detailBos, pageCountStart, file_sn == detailBoPages.size());
            Template template = freeMarkerConfiguration.getTemplate(pdfTemplatePath, "UTF-8");
            String html = null;
            try(StringWriter sw = new StringWriter()){
                template.process(pdfReport, sw);
                html = sw.toString();
            }
            String pdfFilename =  filename + "_" + file_sn + ".pdf";
            String pdfFilePath = savePath + "/" + pdfFilename;
            log.info("创建PDF报表，文件：{}", pdfFilePath);
            File file = new File(pdfFilePath);
            if(file.exists()) {
                boolean delRt = FileUtils.deleteQuietly(file);
                log.info("删除已有文件：{}, rt:{}", file.getAbsolutePath(), delRt);
            }
            try (OutputStream outputStream = Files.newOutputStream(file.toPath());
                 InputStream inputStream = new ByteArrayInputStream(html.getBytes())) {
                File fontFile = new File("fonts", "SourceHanSerifHK-VF.ttf");
                if (!fontFile.exists()) {
                    synchronized (this) {
                        if (!fontFile.getParentFile().exists()) {
                            boolean mkdirsRt = fontFile.getParentFile().mkdirs();
                            log.info("创建文件夹：{} is {}", fontFile.getParentFile().getAbsolutePath(), mkdirsRt);
                        }
                        FileUtils.copyToFile(defaultFont.getInputStream(), fontFile);
                        log.info("复制字体文件到：{} ", fontFile.getAbsolutePath());
                    }
                }
                ConverterProperties converterProperties = new ConverterProperties();
                FontProvider pro = new DefaultFontProvider();
                pro.addFont(FontProgramFactory.createFont(fontFile.getAbsolutePath()));
                converterProperties.setFontProvider(pro);
                HtmlConverter.convertToPdf(inputStream, outputStream, converterProperties);
            }
            // 分页计算
            file_sn = file_sn + 1;
            pageCountStart = (file_sn - 1) * build_merchant_report_count;
            log.info("条目索引：{}，第{}页，生成文件路径：{}", pageCountStart, (file_sn - 1), file.getAbsolutePath());
            // 最终文件
            finalFileName = pdfFilename;
            finalFile = file;
            zipFiles.add(file);
        }
        // 上传文件到oss
        if (zipFiles.size() > 1) {
            // 压缩zip
            String zipName = filename + "_pdf.zip";
            File zipFile = new File(savePath, zipName);
            if (zipFile.exists()) {
                boolean delRt = FileUtils.deleteQuietly(zipFile);
                log.info("删除已有文件：{}, rt:{}", zipFile.getAbsolutePath(), delRt);
            }
            String password = defaultIfEmpty(settingsDao.getValue("site.merchant_zip_password"),"123321");
            ZipUtils.compress(zipFile, zipFiles, password);
            finalFileName = zipName;
            finalFile = zipFile;
        }
        String ossObjectName = ossPath + "/" + finalFileName;
        log.info("上传文件到oss: {}", ossObjectName);
        aliyunOSSService.uploadFile(finalFile, ossObjectName);
        boolean delRt = FileUtils.deleteQuietly(finalFile);
        @SuppressWarnings("java:S2259")
        String absolutePath = finalFile.getAbsolutePath();
        log.info("删除本地文件：{}, rt:{}", absolutePath, delRt);
        if (zipFiles.size() > 1) {
            for (File zip: zipFiles) {
                delRt = FileUtils.deleteQuietly(zip);
                log.info("删除本地文件：{}, rt:{}", zip.getAbsolutePath(), delRt);
            }
        }
        return finalFileName;
    }

    @Override
    public <V> String doCreateExcelAndUploadOss(Integer settlementId, List<SettlementReportDetailBo> reportDetailBos,
                                                String ossPath, String filename,
                                                org.springframework.core.io.Resource settlementTemplate,
                                                ExcelModelCallable<V> pdfModelCallable) throws IOException {
        // 创建存储路径
        String savePath = ConfigUtils.getProperty("report.savePath", "files/report/") + ossPath;
        File savePathFile = new File(savePath);
        if (!savePathFile.exists()) {
            boolean rt = savePathFile.mkdirs();
            log.info("创建存储路径:{}, rt: {}", savePath, rt);
        }
        // 分页
        int build_merchant_report_count = parseInt(defaultIfEmpty(settingsDao.getValue("site.build_merchant_report_count"),"1000"));
        log.info("BusinessSettlementExcel 商户结算id:{}, 当前商户的总条数:{}, 开始数据切换, 分页数量：{}",
                settlementId, reportDetailBos.size(), build_merchant_report_count);
        int file_sn = 1;
        int pageCountStart = 0;
        String finalFileName = null;
        File finalFile = null;
        List<File> zipFiles = new ArrayList<>();
        List<List<SettlementReportDetailBo>> detailBoPages =
                Lists.partition(reportDetailBos, build_merchant_report_count);
        log.info("数据切割分页数：{}", detailBoPages.size());
        // 如果切割分页数为0则需要创建为包含一个空数据的分页，保证生成空文件
        if (detailBoPages.isEmpty()) {
            detailBoPages = new ArrayList<>(1);
            detailBoPages.add(Collections.emptyList());
        }
        for (List<SettlementReportDetailBo> detailBos: detailBoPages) {

            String excelFilename = filename + "_" + file_sn + ".xls";
            String excelFilePath = savePath + "/" + excelFilename;
            log.info("创建Excel报表，文件：{}", excelFilePath);
            File file = new File(excelFilePath);
            if (file.exists()) {
                boolean delRt = FileUtils.deleteQuietly(file);
                log.info("删除已有文件：{}, rt:{}", file.getAbsolutePath(), delRt);
            }
            int dateSn = (file_sn - 1) * build_merchant_report_count + 1;
            V excelReport = pdfModelCallable.call(detailBos, dateSn, file_sn == detailBoPages.size());
            try (InputStream in = settlementTemplate.getInputStream();
                 OutputStream out = Files.newOutputStream(file.toPath())) {
                Context context = PoiTransformer.createInitialContext();
                context.putVar("data", excelReport);
                Workbook workbook = WorkbookFactory.create(in);
                workbook.setSheetName(0, "export-" + JodaTimeUtil.getFullTime());
                PoiTransformer transformer = PoiTransformer.createTransformer(workbook);
                transformer.setOutputStream(out);
                JxlsHelper.getInstance().setUseFastFormulaProcessor(false).processTemplate(context, transformer);
            }
            log.info("条目索引：{}，第{}页，生成文件路径：{}", pageCountStart, file_sn, file.getAbsolutePath());
            // 分页计算
            pageCountStart = (file_sn - 1) * build_merchant_report_count;
            file_sn = file_sn + 1;
            // 最终文件
            finalFileName = excelFilename;
            finalFile = file;
            zipFiles.add(file);
        }
        // 上传文件到oss
        if (zipFiles.size() > 1) {
            // 压缩zip
            String zipName = filename + "_excel.zip";
            File zipFile = new File(savePath, zipName);
            if (zipFile.exists()) {
                boolean delRt = FileUtils.deleteQuietly(zipFile);
                log.info("删除已有文件：{}, rt:{}", zipFile.getAbsolutePath(), delRt);
            }
            String password = defaultIfEmpty(settingsDao.getValue("site.merchant_zip_password"),"123321");
            ZipUtils.compress(zipFile, zipFiles, password);
            finalFileName = zipName;
            finalFile = zipFile;
        }
        String ossObjectName = ossPath + "/" + finalFileName;
        log.info("上传文件到oss: {}", ossObjectName);
        aliyunOSSService.uploadFile(finalFile, ossObjectName);
        boolean delRt = FileUtils.deleteQuietly(finalFile);
        @SuppressWarnings("java:S2259")
        String absolutePath = finalFile.getAbsolutePath();
        log.info("删除本地文件：{}, rt:{}", absolutePath, delRt);
        if (zipFiles.size() > 1) {
            for (File zip: zipFiles) {
                delRt = FileUtils.deleteQuietly(zip);
                log.info("删除本地文件：{}, rt:{}", zip.getAbsolutePath(), delRt);
            }
        }
        return finalFileName;
    }
}
