package com.mcoin.mall.service.job.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.mcoin.mall.bean.*;
import com.mcoin.mall.bo.*;
import com.mcoin.mall.component.ExplicitTransaction;
import com.mcoin.mall.constant.SettlementSend;
import com.mcoin.mall.dao.*;
import com.mcoin.mall.exception.BusinessException;
import com.mcoin.mall.model.Response;
import com.mcoin.mall.model.job.TradingCycleCreateRequest;
import com.mcoin.mall.model.job.TradingDataCreateFileRequest;
import com.mcoin.mall.model.job.TradingDataRequest;
import com.mcoin.mall.model.job.TradingDataSendEmailRequest;
import com.mcoin.mall.model.report.TradingEmailModel;
import com.mcoin.mall.model.report.TradingExcelModel;
import com.mcoin.mall.service.common.AliyunOSSService;
import com.mcoin.mall.service.job.TradeDataJobService;
import com.mcoin.mall.util.ConfigUtils;
import com.mcoin.mall.util.JodaTimeUtil;
import com.mcoin.mall.util.OssUtil;
import freemarker.template.Configuration;
import freemarker.template.Template;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.jxls.common.Context;
import org.jxls.transform.poi.PoiTransformer;
import org.jxls.util.JxlsHelper;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.FileSystemResource;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.mail.internet.MimeMessage;
import java.io.*;
import java.math.BigDecimal;
import java.nio.file.Files;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.temporal.ChronoField;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.stream.IntStream;

import static com.mcoin.mall.util.JodaTimeUtil.*;
import static java.lang.Integer.parseInt;
import static java.math.BigDecimal.ZERO;
import static org.apache.commons.lang3.ObjectUtils.defaultIfNull;
import static org.apache.commons.lang3.StringUtils.defaultIfBlank;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class TradeDataJobServiceImpl implements TradeDataJobService {
    @Resource
    private FookMacaupassUserLoginLog2Dao fookMacaupassUserLoginLog2Dao;
    @Resource
    private FookTradingDao fookTradingDao;
    @Resource
    private FookTradingSendEmailProcessDao fookTradingSendEmailProcessDao;
    @Resource
    private FookTradingDataDao fookTradingDataDao;
    @Resource
    private SettingsDao settingsDao;
    @Resource
    private FookTradingWeekDao fookTradingWeekDao;
    @Resource
    private AliyunOSSService aliyunOSSService;
    private static final String TRADE_TEMPLATE = "classpath:templates/excel/trade.xls";
    @Value(TRADE_TEMPLATE)
    private org.springframework.core.io.Resource tradeTemplate;
    @Resource
    private ExplicitTransaction transaction;
    @Resource
    private ExplicitTransaction batchTransaction;
    @Resource
    private FookTradingDataClassDao fookTradingDataClassDao;
    @Resource
    private JavaMailSender emailSender;
    @Resource
    private Configuration freeMarkerConfiguration;

    private static final int SEND_DAY = 30;

    @Override
    public void doCreateTradingData(TradingDataRequest request) {
        batchTransaction.invokeWithNewTransaction(()->{
            String kernelOrderreturn = settingsDao.getValueDirect("site.kernel_orderreturn");
            String kernelReportorder = settingsDao.getValueDirect("site.kernel_reportorder");
            if ("1".equals(kernelOrderreturn) && "1".equals(kernelReportorder)) {
                Date curDate = getDateWithoutTime(request.getCurrentDate());
                Date startTime = plusDayToDate(curDate, -1);
                Date endTime = plusSecondToDate(curDate, -1);
                //當天進入用戶數(總)
                int userNum = fookMacaupassUserLoginLog2Dao.countDistinctUserId(startTime, endTime);

                //交易統計
                TradingDataBo tradingDataBo = fookTradingDao.selectTradingData(startTime, endTime);
                tradingDataBo = defaultIfNull(tradingDataBo, new TradingDataBo());

                //核銷統計
                TradingSettlementDataBo tradingSettlementDataBo = fookTradingDao.selectTradingSettlementData(startTime, endTime);
                tradingSettlementDataBo = defaultIfNull(tradingSettlementDataBo, new TradingSettlementDataBo());

                //退款統計
                TradingRefundDataBo tradingRefundDataBo = fookTradingDao.selectTradingRefundData(startTime, endTime);
                tradingRefundDataBo = defaultIfNull(tradingRefundDataBo, new TradingRefundDataBo());

                //熱銷福利(金額最大)
                TradingProductMaxDataBo tradingProductMaxDataBo = fookTradingDao.selectTradingProductMaxData(startTime, endTime);
                tradingProductMaxDataBo = defaultIfNull(tradingProductMaxDataBo, new TradingProductMaxDataBo());

                //熱銷福利(數量最大)
                TradingProductHotDataBo tradingProductHotDataBo = fookTradingDao.selectTradingProductHotData(startTime, endTime);
                tradingProductHotDataBo = defaultIfNull(tradingProductHotDataBo, new TradingProductHotDataBo());

                FookTradingData tradingData = new FookTradingData();
                tradingData.setStatisticsDay(startTime);
                tradingData.setUserNum(userNum);
                //當天進入用戶數(有進行支付的)
                tradingData.setUserPayNum(tradingDataBo.getUserPayNum());
                //當天進入用戶數(無進行支付的)
                tradingData.setUseNoPayNum(userNum - tradingDataBo.getUserPayNum());
                //交易商戶數量
                tradingData.setBusinessNum(tradingDataBo.getTradingBusinessNum());
                //交易筆數
                tradingData.setTradingNum(tradingDataBo.getTradingNum());
                //交易金額(mcoin積分轉換金額)
                tradingData.setIntegralConversionAmount(defaultIfNull(tradingDataBo.getIntegralConversionAmount(), ZERO));
                //交易金額(客戶實際付款)
                tradingData.setPaymentAmount(defaultIfNull(tradingDataBo.getPaymentAmountSum(), ZERO));
                //mcoin積分轉換金額加客戶實際付款金額
                tradingData.setPaymentSum(defaultIfNull(tradingDataBo.getTradingTotalAmount(), ZERO));
                // 平台补贴金额
                tradingData.setSubsidyAmount(defaultIfNull(tradingDataBo.getSubsidyAmount(), ZERO));
                //核銷商戶數量
                tradingData.setSettlementBusinessNum(defaultIfNull(tradingSettlementDataBo.getSettlementBusinessNum(), 0));
                //核銷筆數
                tradingData.setSettlementNum(defaultIfNull(tradingSettlementDataBo.getSettlementNum(), 0));
                //核銷金額(mCoin轉化金額)
                tradingData.setSettlementConversionAmount(defaultIfNull(tradingSettlementDataBo.getMomecoinsamountSum(), ZERO));
                //核銷金額(MPay支付的金額)
                tradingData.setSettlementPaymentAmount(defaultIfNull(tradingSettlementDataBo.getUserpaymentamountSum(), ZERO));
                //核銷總金額 (核銷mCoin轉化金額加核銷MPay支付的金額)
                tradingData.setSettlementAmountSum(defaultIfNull(tradingSettlementDataBo.getBillamountSum(), ZERO));
                //退款金額(mCoin轉化金額)
                tradingData.setRefundConversionAmount(defaultIfNull(tradingRefundDataBo.getRefundConversionAmount(), ZERO));
                //退款金額(MPay支付的金額)
                tradingData.setRefundPaymentAmount(defaultIfNull(tradingRefundDataBo.getRefundAmount(), ZERO));
                //退款總金額(退款mCoin轉化金額加退款MPay支付的金額)
                tradingData.setRefundAmountSum(defaultIfNull(tradingRefundDataBo.getOrderAmount(), ZERO).toPlainString());
                //退款筆數
                tradingData.setRefundNum(defaultIfNull(tradingRefundDataBo.getTradingNum(), 0));
                // 退款补贴金额
                tradingData.setRefundSubsidyAmount(defaultIfNull(tradingRefundDataBo.getRefundSubsidyAmount(), ZERO));
                //當天銷售商品金額最多的福利
                tradingData.setProductIdHot(defaultIfNull(tradingProductMaxDataBo.getProdcutid(), null));
                //當天銷售商品金額最多的福利名稱
                tradingData.setProductTitleHot(defaultIfNull(tradingProductMaxDataBo.getProductTitle(), null));
                //熱銷商品銷售金額
                tradingData.setProductSalesAmount(defaultIfNull(tradingProductMaxDataBo.getOrderAmountSum(), ZERO));
                //當天銷售商品數量最多的福利
                tradingData.setProductIdMax(defaultIfNull(tradingProductHotDataBo.getProdcutid(), null));
                //當天銷售商品數量最多的福利名稱
                tradingData.setProductTitleMax(defaultIfNull(tradingProductHotDataBo.getProductTitle(), null));
                //熱銷商品銷售數量
                tradingData.setProductNum(defaultIfNull(tradingProductHotDataBo.getProductNum(), 0).toString());
                //創建時間
                tradingData.setCreatedAt(new Date());
                // 差入库
                fookTradingDataDao.insert(tradingData);
                // 更新标识
                settingsDao.updateByKey("site.kernel_orderreturn", "0");
                settingsDao.updateByKey("site.kernel_reportorder", "0");
                settingsDao.updateByKey("site.kernel_trading_data", "1");
            } else {
                settingsDao.updateByKey("site.kernel_trading_data", "0");
                log.info("交易統計數據缺失");
            }
        });
    }

    @Override
    public void doCreateTradingCycle(TradingCycleCreateRequest request) {
        Date startTime = null;
        Date endTime = null;
        Date last_week_start = null;
        Date last_week_end = null;
        boolean trading = false;
        int data_send_cycle = parseInt(settingsDao.getValueDirect("site.trading_data_send_cycle"));

        // 检查当前日期是否是本周的第一天
        LocalDate today = request.getCurrentDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate startOfWeek = today.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY));

        // 获取上一周的周日（本周一的前七天）
        LocalDate lastWeekSunday = startOfWeek.minusWeeks(1);
        // 获取上一周的周一（上周日的后一天）
        LocalDate lastWeekMonday = lastWeekSunday.plusDays(1);
        // 获取上一周的周日（上周一的前六天）
        LocalDate lastWeekEnd = lastWeekMonday.minusDays(6);
        switch (data_send_cycle) {
            case 1:
                startTime = JodaTimeUtil.plusDayToDate(request.getCurrentDate(), -SEND_DAY);
                endTime = JodaTimeUtil.plusSecondToDate(request.getCurrentDate(), -1);
                trading = true;

                last_week_start = JodaTimeUtil.plusDayToDate(request.getCurrentDate(), -1);
                last_week_end = JodaTimeUtil.plusSecondToDate(request.getCurrentDate(), -1);
                break;
            case 2:
                startTime = JodaTimeUtil.plusDayToDate(request.getCurrentDate(), -SEND_DAY);
                endTime = JodaTimeUtil.plusSecondToDate(request.getCurrentDate(), -1);
                if (today.equals(startOfWeek)) {
                    trading = true;
                }
                last_week_start = JodaTimeUtil.toDate(lastWeekMonday);
                last_week_end = JodaTimeUtil.toDate(lastWeekEnd);
                break;
            case 3:
                startTime = JodaTimeUtil.plusDayToDate(request.getCurrentDate(), -SEND_DAY);
                endTime = JodaTimeUtil.plusSecondToDate(request.getCurrentDate(), -1);
                // 获取今天是本月的第几天
                int dayOfMonth = today.get(ChronoField.DAY_OF_MONTH);
                if (dayOfMonth == 1) {
                    trading = true;
                }
                last_week_start = JodaTimeUtil.toDate(lastWeekMonday);
                last_week_end = JodaTimeUtil.toDate(lastWeekEnd);
                break;
        }
        if (trading) {
            int tradingDataCount = fookTradingDao.selectCount(new LambdaQueryWrapper<FookTrading>()
                    .eq(FookTrading::getStartTime, startTime)
                    .eq(FookTrading::getEndTime, endTime)
                    .eq(FookTrading::getCycleType, data_send_cycle)
            );
            if (tradingDataCount == 0) {
                Date finalStartTime = startTime;
                Date finalEndTime = endTime;
                Date finalLast_week_start = last_week_start;
                Date finalLast_week_end = last_week_end;
                batchTransaction.invokeWithNewTransaction(()->{
                    FookTrading insertTrading = new FookTrading();
                    insertTrading.setStartTime(finalStartTime);
                    insertTrading.setEndTime(finalEndTime);
                    insertTrading.setCycleType(data_send_cycle);
                    insertTrading.setSendDay(SEND_DAY);
                    insertTrading.setCreatedAt(new Date());
                    fookTradingDao.insert(insertTrading);

                    List<FookTradingData> tradingDataList = fookTradingDataDao.selectList(new LambdaQueryWrapper<FookTradingData>()
                            .between(FookTradingData::getStatisticsDay, finalStartTime, finalEndTime)

                    );
                    if (!tradingDataList.isEmpty()) {
                        for (FookTradingData tradingData: tradingDataList) {
                            FookTradingDataClass tradingDataClass = new FookTradingDataClass();
                            tradingDataClass.setTradingId(insertTrading.getId());
                            tradingDataClass.setTradingDataId(tradingData.getId());
                            fookTradingDataClassDao.insertSelective(tradingDataClass);
                        }
                    }
                    FookTradingSendEmailProcess emailProcess = new FookTradingSendEmailProcess();
                    emailProcess.setCreatetime(new Date());
                    emailProcess.setEmailMain(settingsDao.getValueDirect("site.trading_data_email_main"));
                    emailProcess.setEmail(settingsDao.getValueDirect("site.trading_data_email"));
                    emailProcess.setSend(SettlementSend.NO.ordinal());
                    emailProcess.setTradingId(insertTrading.getId());
                    fookTradingSendEmailProcessDao.insert(emailProcess);

                    List<TradingProductHotDataBo> hotDataBos = fookTradingDao
                            .selectTradingProductHotDataList(finalLast_week_start, finalLast_week_end);
                    for (TradingProductHotDataBo hotDataBo: hotDataBos) {
                        FookTradingWeek tradingWeek = new FookTradingWeek();
                        tradingWeek.setProductId(hotDataBo.getProdcutid());
                        tradingWeek.setProductTitle(hotDataBo.getProductTitle());
                        tradingWeek.setPruductNum(hotDataBo.getProductNum());
                        tradingWeek.setOrderAmount(hotDataBo.getOrderAmountSum());
                        tradingWeek.setTradingId(insertTrading.getId());
                        fookTradingWeekDao.insertSelective(tradingWeek);
                    }
                    settingsDao.updateByKey("site.kernel_trading_data", "0");
                });
            }
        }
    }

    @Override
    public void doCreateTrade2Excel(TradingDataCreateFileRequest request){
        FookTradingSendEmailProcess process = fookTradingSendEmailProcessDao.selectOne(new LambdaQueryWrapper<FookTradingSendEmailProcess>()
                .eq(FookTradingSendEmailProcess::getIfUpload, 0)
                .last("limit 1"));
        if (process != null){
            FookTrading trading = fookTradingDao.selectByPrimaryKey(process.getTradingId());
            if (trading != null){
                List<FookTradingData> dataList = fookTradingDataDao.selectTradingDataByTradingId(trading.getId());
                List<TradingExcelModel> tradingExcelModels = new ArrayList<>();
                if (CollectionUtils.isNotEmpty(dataList)){
                    for (FookTradingData data : dataList) {
                        TradingExcelModel excelModel = new TradingExcelModel();
                        if (data.getStatisticsDay() != null){
                            excelModel.setStatisticsDay(JodaTimeUtil.format(data.getStatisticsDay(), JodaTimeUtil.MCOIN_CN));
                        } else {
                            excelModel.setStatisticsDay("");
                        }
                        excelModel.setUserNum(data.getUserNum());
                        excelModel.setUserPayNum(data.getUserPayNum());
                        excelModel.setUseNoPayNum(data.getUseNoPayNum());
                        excelModel.setBusinessNum(data.getBusinessNum());
                        excelModel.setTradingNum(data.getTradingNum());
                        excelModel.setIntegralConversionAmount(data.getIntegralConversionAmount());
                        excelModel.setPaymentAmount(data.getPaymentAmount());
                        excelModel.setPaymentSum(data.getPaymentSum());
                        excelModel.setSettlementBusinessNum(data.getSettlementBusinessNum());
                        excelModel.setSettlementNum(data.getSettlementNum());
                        excelModel.setSettlementConversionAmount(data.getSettlementConversionAmount());
                        excelModel.setSettlementPaymentAmount(data.getSettlementPaymentAmount());
                        excelModel.setSettlementAmountSum(data.getSettlementAmountSum());
                        excelModel.setRefundConversionAmount(data.getRefundConversionAmount());
                        excelModel.setRefundPaymentAmount(data.getRefundPaymentAmount());
                        excelModel.setRefundAmountSum(new BigDecimal(defaultIfBlank(data.getRefundAmountSum(), "0")));
                        excelModel.setRefundNum(data.getRefundNum());
                        excelModel.setProductIdHot(data.getProductIdHot());
                        excelModel.setProductTitleHot(data.getProductTitleHot());
                        excelModel.setProductSalesAmount(data.getProductSalesAmount());
                        excelModel.setProductIdMax(data.getProductIdMax());
                        excelModel.setProductTitleMax(data.getProductTitleMax());
                        excelModel.setProductNum(Integer.parseInt(defaultIfBlank(data.getProductNum(), "0")));
                        excelModel.setSubsidyAmount(data.getSubsidyAmount());
                        excelModel.setRefundSubsidyAmount(data.getRefundSubsidyAmount());
                        tradingExcelModels.add(excelModel);
                    }
                }
                //{"defalut":"1","options":{"1":"每日","2":"每周","3":"每月"}}
                int sendCycle = parseInt(defaultIfBlank(settingsDao.getValueDirect("site.trading_data_send_cycle"), "2"));
                String sheetName = "熱銷商品周排名";
                if (sendCycle == 1){
                    sheetName = "熱銷商品日排名";
                }
                List<FookTradingWeek> tradingWeekList =  fookTradingWeekDao.selectByTradingId(process.getTradingId());
                if(CollectionUtils.isNotEmpty(tradingWeekList)){
                    IntStream.range(0,tradingWeekList.size()).forEach(index->{
                        tradingWeekList.get(index).setIndex(index+1);
                    });
                }
                // 创建存储路径
                String ossPath = OssUtil.getTradingDataOssPath();
                String savePath = ConfigUtils.getProperty("report.savePath", "files/report/") + ossPath;
                File savePathFile = new File(savePath);
                if (!savePathFile.exists()) {
                    boolean rt = savePathFile.mkdirs();
                    log.info("创建存储路径:{}, rt: {}", savePath, rt);
                }
                String fineName = "mCoin交易數據分析" + format(request.getCurrentDate(), YYYYMMDD)+".xls";

                //寫入文件
                String filePath = savePath + "/" + fineName;
                log.info("寫入文件: {}", filePath);
                File file = null;
                try {
                    file = exportMultSheetExcel(tradingWeekList, tradingExcelModels, sheetName, filePath);
                } catch (IOException e) {
                    throw new RuntimeException(e);
                }

                //上傳文件
                String ossObjectName = ossPath + "/" + fineName;
                log.info("上传文件到oss: {}", ossObjectName);
                aliyunOSSService.uploadFile(file, ossObjectName);
                transaction.invokeWithNewTransaction(()->{
                    Date nowDate = new Date();
                    FookTrading tradingUpdate = new FookTrading();
                    tradingUpdate.setId(process.getTradingId());
                    tradingUpdate.setFileOssLink(fineName);
                    tradingUpdate.setUpdatedAt(nowDate);
                    //導出成功以後，更新
                    fookTradingDao.updateByPrimaryKeySelective(tradingUpdate);

                    FookTradingSendEmailProcess processUpdate = new FookTradingSendEmailProcess();
                    processUpdate.setId(process.getId());
                    processUpdate.setIfUpload(1);
                    processUpdate.setUploadTime(nowDate);
                    fookTradingSendEmailProcessDao.updateByPrimaryKeySelective(processUpdate);
                });
                if (file != null){
                    boolean delRt = FileUtils.deleteQuietly(file);
                    log.info("删除本地文件：{}, rt:{}", file.getAbsolutePath(), delRt);
                }
            }
        }
    }

    @Override
    public void sendTradingEmail(TradingDataSendEmailRequest request) {
        FookTradingSendEmailProcess process = fookTradingSendEmailProcessDao.selectOne(new LambdaQueryWrapper<FookTradingSendEmailProcess>()
                .eq(FookTradingSendEmailProcess::getIfUpload, 1)
                .eq(FookTradingSendEmailProcess::getSend, 0)
                .last("limit 1"));
        if (process == null) {
            log.info("无交易统计数据邮件要发送");
            return;
        }
        FookTrading trading = fookTradingDao.selectByPrimaryKey(process.getTradingId());
        if (trading == null) {
            log.warn("无交易统计数据邮件要发送");
            return;
        }
        if (StringUtils.isBlank(process.getEmailMain())) {
            log.info("交易统计数据邮件地址（EmailMain）为空");
            return;
        }
        try {
            String ossPath = OssUtil.getTradingDataOssPath();
            String savePath = ConfigUtils.getProperty("report.savePath", "files/report/") + ossPath + "/download";
            String fileOssPath = ossPath + "/" + trading.getFileOssLink();
            log.info("downloading trading file from oss path:{}", fileOssPath);
            File tradingFile = aliyunOSSService.downloadFile(fileOssPath, savePath, trading.getFileOssLink());
            if (tradingFile == null) {
                throw new BusinessException(Response.Code.BAD_REQUEST, "trading data 文件" + fileOssPath + "为空");
            }
            log.info("downloaded pdf file save to:{}", tradingFile.getAbsolutePath());

            Template template = freeMarkerConfiguration.getTemplate("mail/trading-data.ftl", "UTF-8");
            String html;
            try(StringWriter sw = new StringWriter()){
                template.process(new TradingEmailModel()
                        .setStartTime(format(trading.getStartTime(), YYYY_MM_DD))
                        .setEndTime(format(trading.getEndTime(), YYYY_MM_DD))
                        .setDateNum(SEND_DAY), sw);
                html = sw.toString();
            }

            MimeMessage mimeMessage = emailSender.createMimeMessage();
            MimeMessageHelper helper =  new MimeMessageHelper(mimeMessage, true);
            helper.setFrom(ConfigUtils.getProperty("spring.mail.username"));
            helper.setTo(ObjectUtil.defaultIfBlank(process.getEmailMain(), "").split(";"));
            helper.setCc(ObjectUtil.defaultIfBlank(process.getEmail(), "").split(";"));
            helper.setSubject("mCoin交易統計數據");
            helper.setText(html, true);
            helper.addAttachment(trading.getFileOssLink(), new FileSystemResource(tradingFile));
            emailSender.send(mimeMessage);
            // 更新发送email状态
            transaction.invokeWithRequiredTransaction(()->{
                FookTradingSendEmailProcess sendEmailProcess = new FookTradingSendEmailProcess();
                sendEmailProcess.setSend(SettlementSend.YES.ordinal());
                sendEmailProcess.setSendtime(new Date());
                fookTradingSendEmailProcessDao.update(sendEmailProcess, new LambdaUpdateWrapper<FookTradingSendEmailProcess>()
                        .eq(FookTradingSendEmailProcess::getId, process.getId())
                );
            });
        } catch (Exception e) {
            // 更新发送email状态
            transaction.invokeWithRequiredTransaction(()->{
                FookTradingSendEmailProcess sendEmailProcess = new FookTradingSendEmailProcess();
                sendEmailProcess.setSend(SettlementSend.ERROR.ordinal());
                sendEmailProcess.setSendtime(new Date());
                fookTradingSendEmailProcessDao.update(sendEmailProcess, new LambdaUpdateWrapper<FookTradingSendEmailProcess>()
                        .eq(FookTradingSendEmailProcess::getId, process.getId())
                );
            });
            throw new RuntimeException(e);
        }
    }

    public File exportMultSheetExcel(List<FookTradingWeek> tradingWeekList,
                                     List<TradingExcelModel> dataList,
                                     String sheetName, String filePath) throws IOException {
        File file = new File(filePath);
        if (file.exists()) {
            boolean delRt = FileUtils.deleteQuietly(file);
            log.info("删除已有文件：{}, rt:{}", file.getAbsolutePath(), delRt);
        }
        try (InputStream in = tradeTemplate.getInputStream();
             OutputStream out = Files.newOutputStream(file.toPath())) {
            Context context = new Context();
            Map<String,Object> map = new HashMap<>();
            map.put("b",dataList);
            context.putVar("map",map);

            Map<String,Object> map2 = new HashMap<>();
            map2.put("a",sheetName);
            map2.put("b",tradingWeekList);
            context.putVar("map2",map2);
            Workbook workbook = WorkbookFactory.create(in);
            workbook.setSheetName(1, sheetName);
            PoiTransformer transformer = PoiTransformer.createTransformer(workbook);
            transformer.setOutputStream(out);
            JxlsHelper.getInstance().processTemplate(context, transformer);
        }
        return file;
    }
}
