package com.mcoin.mall.service.job.utils;

import com.mcoin.mall.bean.FookReportOrdercodeSettlement;
import com.mcoin.mall.bo.SettlementAmountBo;
import com.mcoin.mall.bo.SettlementReportDetailBo;
import com.mcoin.mall.config.properties.ReportSettlementTemplateConfiguration;
import com.mcoin.mall.config.properties.ReportSettlementTemplateType;
import com.mcoin.mall.constant.PlatformOrderTypeEnum;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static java.math.BigDecimal.ZERO;

public class SettlementUtil {

    public static SettlementAmountBo getSettlementAmountBo(List<FookReportOrdercodeSettlement> ordercodeSettlements){
        SettlementAmountBo amountBo = new SettlementAmountBo();
        amountBo.setTotalAmount(ordercodeSettlements.stream().map(FookReportOrdercodeSettlement::getBillamount)
                .reduce(ZERO, BigDecimal::add));
        amountBo.setTotalSettlement(ordercodeSettlements.stream().map(FookReportOrdercodeSettlement::getMerchantsettleamount)
                .reduce(ZERO, BigDecimal::add));
        amountBo.setTotalCommission(ordercodeSettlements.stream().map(FookReportOrdercodeSettlement::getCommission)
                .reduce(ZERO, BigDecimal::add));
        amountBo.setTotalMomecoins(ordercodeSettlements.stream().map(FookReportOrdercodeSettlement::getMomecoinsamount)
                .reduce(ZERO, BigDecimal::add));
        amountBo.setTotalSubsidyAmount(ordercodeSettlements.stream().map(FookReportOrdercodeSettlement::getSubsidyAmount)
                .reduce(ZERO, BigDecimal::add));
        return amountBo;
    }


    public static Map<Integer, SettlementAmountBo>
    getSettlementAmountBosGroupByStoreId(List<FookReportOrdercodeSettlement> ordercodeSettlements){
        Map<Integer, SettlementAmountBo> map = new HashMap<>();
        Map<Integer, List<FookReportOrdercodeSettlement>> groupBySettlements = ordercodeSettlements.stream()
                .collect(Collectors.groupingBy(FookReportOrdercodeSettlement::getStoreid));
        groupBySettlements.forEach((k,v)->{
            map.put(k, getSettlementAmountBo(v));
        });
        return map;
    }


    /**
     * 处理小程序订单号，只保留小程序订单
     * @param configuration
     * @param reportDetailBos
     */

    public static void onlyKeepMiniOrderNo(ReportSettlementTemplateConfiguration configuration,
                                           List<SettlementReportDetailBo> reportDetailBos){
        // 处理小程序模板订单号
        if (configuration != null && configuration.getType() ==
                ReportSettlementTemplateType.MINI_ORDER_SETTLEMENT) {
            reportDetailBos.forEach(e->{
                if (e.getType() != PlatformOrderTypeEnum.MINI_ORDER.getType()) {
                    e.setOrderTransaction(null);
                }
            });
        }
    }
}
