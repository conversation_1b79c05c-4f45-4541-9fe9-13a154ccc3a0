package com.mcoin.mall.service.pay;

import com.mcoin.mall.bean.FookPayLog;
import com.mcoin.mall.client.model.PayLogRequest;
import com.mcoin.mall.client.model.RefundSuccessLogRequest;

public interface PayBaseService {

    void uploadPaySuccess(FookPayLog log);

    void uploadPayToTaskCenter(PayLogRequest req);

    void uploadRefundSuccess(FookPayLog payLog, String refundScene);

    void uploadRefundToTaskCenter(RefundSuccessLogRequest request);
}
