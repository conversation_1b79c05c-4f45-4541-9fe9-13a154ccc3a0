package com.mcoin.mall.service.pay;

import com.mcoin.mall.model.*;

public interface PayService {

    ObtainPayResponse getObtainPay(ObtainPayRequest request);
    ApplyBuyResponse applyBuy(ApplyBuyRequest request);
    SnapUpSuccessResponse updStockAndSnapUpSuccess(SnapUpSuccessRequest request);
    CreateOrderResponse createOrder(CreateOrderRequest request);
    void productProtocol(ProductProtocolRequest request);

    PopUpCashierResponse updPopUpCashier(Integer orderId);
    QueryPayResponse queryPay(Integer orderId);
    CancelPayResponse updCancelPay(Integer orderId);

    void updMPayCallback(MPayCallbackRequest request);

}
