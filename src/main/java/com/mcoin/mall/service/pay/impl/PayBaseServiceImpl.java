package com.mcoin.mall.service.pay.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.StrFormatter;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Maps;
import com.mcoin.mall.bean.*;
import com.mcoin.mall.client.TaskUploadClient;
import com.mcoin.mall.client.model.PayLogRequest;
import com.mcoin.mall.client.model.PayLogResponse;
import com.mcoin.mall.client.model.RefundSuccessLogRequest;
import com.mcoin.mall.client.model.SignReqDto;
import com.mcoin.mall.constant.MqLocalResourceType;
import com.mcoin.mall.constant.RefundSceneEnum;
import com.mcoin.mall.dao.*;
import com.mcoin.mall.exception.BusinessException;
import com.mcoin.mall.exception.RetryException;
import com.mcoin.mall.model.Response;
import com.mcoin.mall.mq.model.PaySuccessUploadMessage;
import com.mcoin.mall.mq.model.RefundSuccessMessage;
import com.mcoin.mall.service.base.MqLocalService;
import com.mcoin.mall.service.pay.PayBaseService;
import com.mcoin.mall.util.ConfigUtils;
import com.mcoin.mall.util.TaskSignUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;

import static com.mcoin.mall.util.SentinelUtils.handleBlockException;

@Service
@Slf4j
public class PayBaseServiceImpl implements PayBaseService {

    @Resource
    private FookPlatformOrderDao fookPlatformOrderDao;
    @Resource
    private FookPlatformOrderinfoDao fookPlatformOrderinfoDao;

    @Resource
    private FookMacaupassUserDao fookMacaupassUserDao;

    @Resource
    private TaskUploadClient taskUploadClient;

    @Resource
    private ApplicationContext applicationContext;

    @Resource
    private MqLocalService mqLocalService;

    @Resource
    private FookPlatformOrdercodeDao fookPlatformOrdercodeDao;

    @Resource
    private FookPlatformOrderrefundDao fookPlatformOrderrefundDao;

    @Resource
    private FookBusinessDao fookBusinessDao;

    @Resource
    private FookPayLogDao fookPayLogDao;

    @Override
    public void uploadPaySuccess(FookPayLog payLog) {
        FookPlatformOrder order = fookPlatformOrderDao.selectByPrimaryKey(payLog.getOrderid());
        FookPlatformOrderinfo orderInfo = this.fookPlatformOrderinfoDao
                .selectOne(new LambdaQueryWrapper<FookPlatformOrderinfo>()
                        .eq(FookPlatformOrderinfo::getOrderid, payLog.getOrderid())
                        .last("limit 1"));
        FookMacaupassUser macaupassUser = fookMacaupassUserDao.selectOne(new LambdaQueryWrapper<FookMacaupassUser>()
                .eq(FookMacaupassUser::getUserId, payLog.getUid())
                .last("limit 1"));
        FookBusiness business = this.fookBusinessDao.selectByPrimaryKey(order.getSellerid());
        PayLogRequest req = new PayLogRequest();
        PayLogRequest.Param param = new PayLogRequest.Param();
        req.setParam(param);
        param.setSystem_name("taskCenter");
        param.setTran_time(DateUtil.format(order.getPaymentTime(), DatePattern.PURE_DATETIME_PATTERN));
        // 实际付款金额(不包含积分抵扣）
        param.setTran_value(order.getTotalAmount().toPlainString());
        // 订单金额（包含积分）
        param.setOriginal_ordamt(order.getOrderAmount().toPlainString());
        param.setCust_id(Convert.toStr(macaupassUser.getCustomid()));
        param.setUser_name(macaupassUser.getPhone());
        param.setCostrec_no(order.getOrderNo());
        param.setMer_no(business.getCode());
        param.setMer_face_no("");
        param.setPoint_no("");
        param.setData_source("mCoin");
        PayLogRequest.ReqData reqData = new PayLogRequest.ReqData();
        req.setData(reqData);
        reqData.setTelephone_no(macaupassUser.getPhone());
        reqData.setCust_id(macaupassUser.getCustomid());
        reqData.setTran_time(DateUtil.format(order.getPaymentTime(), DatePattern.PURE_DATETIME_PATTERN));
        reqData.setTran_value(order.getTotalAmount().toPlainString());
        reqData.setOriginal_ordamt(order.getOrderAmount().toPlainString());
        reqData.setTran_currency("MOP");
        reqData.setMer_no(business.getCode());
        reqData.setProduct_id(Convert.toStr(orderInfo.getProdcutid()));
        reqData.setData_source("mCoin");

        FookMqLocal record = new FookMqLocal();
        Date now = new Date();
        record.setNextRetry(DateUtil.offsetMinute(now, 1));
        record.setTryCount(0);
        record.setMaxTryCount(3);
        record.setTemplateName("taskUploadTemplate");
        record.setResourceId(Convert.toStr(payLog.getId()));
        record.setResourceType(MqLocalResourceType.PAY_LOG.getType());
        PaySuccessUploadMessage msg = new PaySuccessUploadMessage();
        msg.setRequest(req);
        msg.setDelayMax(3);
        msg.setMessageType("paySuccessUpload");
        record.setMessageBody(JSONUtil.toJsonStr(msg));
        this.mqLocalService.saveMqLocal(record);
        msg.setMqLocalId(record.getId());
        applicationContext.publishEvent(msg);
    }

    public void uploadPayToTaskCenter(PayLogRequest req) {
        Map<String, Object> body = BeanUtil.beanToMap(req, Maps.newHashMap(), false, false);
        SignReqDto reqDto = null;
        try {
            reqDto = TaskSignUtils.signRequestToSignReqDto(body);
        } catch (Exception e) {
            log.error("Upload pay log sign error {}  ", JSON.toJSONString(req));
            log.error("Upload pay log sign error ", e);
            throw new RetryException();
        }
        SignReqDto finalReqDto = reqDto;
        String result = handleBlockException(() -> this.taskUploadClient.transactionalTaskNotify(finalReqDto),
                "flowBlocked");
        if ("flowBlocked".equals(result)) {
            log.error("flowBlocked, will retry,result is {} ", result);
            throw new RetryException();
        }
        if (StringUtils.isNotBlank(result)) {
            boolean signResult = TaskSignUtils.verifSignResponse(result);
            if (!signResult) {
                throw new BusinessException(Response.Code.UNKNOWN_ERROR,
                        StrFormatter.format("/uploadPayToTaskCenter verify response sign error, result is {}", result));
            }
            PayLogResponse resp = JSON.parseObject(result, PayLogResponse.class);
            Set<String> retryableCodes = CollUtil.newHashSet("9000", "9001", "9002", "9999");
            if ("0000".equals(resp.getCode())) {
                log.info("uploadPayToTaskCenter success {}", resp.getMsg());
            } else if (retryableCodes.contains(resp.getCode())) {
                log.error("Uploaded pay log error, will retry,result is {} ", result);
                throw new RetryException();
            } else {
                log.error("Uploaded pay log error,do not retry,result is {} ", result);
                String message = StrFormatter.format("request /transactionalTaskNotify error, result is ", result);
                throw new BusinessException(Response.Code.UNKNOWN_ERROR, message);
            }
        }
    }

    @Override
    public void uploadRefundSuccess(FookPayLog payLog, String refundScene) {
        FookPlatformOrder order = fookPlatformOrderDao.selectByPrimaryKey(payLog.getOrderid());
        if (isSellerInBlackList(order.getSellerid())) {
            log.info("The seller {} is in black list, do not upload refund record to task center.",
                    order.getSellerid());
            return;
        }

        FookPlatformOrderrefund refund = this.fookPlatformOrderrefundDao.selectByPrimaryKey(payLog.getRefundId());

        // 检查 refundScene 是否为 'INVALID_ORDER'
        if (RefundSceneEnum.INVALID_ORDER.getCode().equals(refundScene)) {
            log.info("RefundScene is INVALID_ORDER, Current scene: {}", refundScene);

            if(null == order.getPaymentTime()) {
                log.info("Payment time is null, skip uploading refund success. {}", order.getOrderNo());
                return;
            }
            // 查询支付记录，条件orderid+操作名="pay"+status=1
            FookPayLog payRecord = fookPayLogDao.selectOne(new LambdaQueryWrapper<FookPayLog>()
                    .eq(FookPayLog::getOrderid, payLog.getOrderid())
                    .eq(FookPayLog::getOparemtion, "pay")
                    .eq(FookPayLog::getStatus, 1)
                    .last("limit 1"));

            // 如果条件不符合则返回
            if (payRecord == null) {
                log.info("No valid pay record found for order {}, skip uploading refund success.", payLog.getOrderid());
                return;
            }
        }

        FookMacaupassUser macaupassUser = fookMacaupassUserDao.selectOne(new LambdaQueryWrapper<FookMacaupassUser>()
                .eq(FookMacaupassUser::getUserId, payLog.getUid())
                .last("limit 1"));
        FookBusiness business = this.fookBusinessDao.selectByPrimaryKey(order.getSellerid());

        // 查询所有满足条件的退款记录，条件orderid+platform_deal_status=3
        List<FookPlatformOrderrefund> allRefunds = this.fookPlatformOrderrefundDao
                .selectList(new LambdaQueryWrapper<FookPlatformOrderrefund>()
                        .eq(FookPlatformOrderrefund::getOrderid, payLog.getOrderid())
                        .eq(FookPlatformOrderrefund::getPlatformDealStatus, 3));

        // 实际付款金额(不包含积分抵扣)
        BigDecimal totalAmount = ObjectUtils.defaultIfNull(order.getTotalAmount(), BigDecimal.ZERO);
        // 订单金额（包含积分）
        BigDecimal orderAmount = ObjectUtils.defaultIfNull(order.getOrderAmount(), BigDecimal.ZERO);
        // 退款实际金额（不包含积分抵扣）
        BigDecimal actualRefundAmount = ObjectUtils.defaultIfNull(refund.getActualRefundAmount(), BigDecimal.ZERO);
        // 退款积分抵扣金额
        BigDecimal refundScoreAmount = ObjectUtils.defaultIfNull(refund.getRefundScore(), BigDecimal.ZERO);
        // 退款订单金额（包含积分）
        BigDecimal refundOriginalOrdamt = actualRefundAmount.add(refundScoreAmount);

        // 计算所有退款记录的总金额（不含积分）
        BigDecimal sumActualRefundAmount = allRefunds.stream()
                .map(r -> ObjectUtils.defaultIfNull(r.getActualRefundAmount(), BigDecimal.ZERO))
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 计算所有退款记录的总金额（含积分）
        BigDecimal sumTotalRefundAmount = allRefunds.stream()
                .map(r -> ObjectUtils.defaultIfNull(r.getActualRefundAmount(), BigDecimal.ZERO)
                        .add(ObjectUtils.defaultIfNull(r.getRefundScore(), BigDecimal.ZERO)))
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 剩余未退金额（不包含积分）
        BigDecimal leftTranValue = totalAmount.subtract(sumActualRefundAmount);

        // 剩余未退金额（包含积分）
        BigDecimal leftOriginalOrdamt = orderAmount.subtract(sumTotalRefundAmount);

        // 是否最後一筆退款0：是，1：否
        String isAllRefund = "0";
        if (BigDecimal.ZERO.compareTo(leftOriginalOrdamt) != 0) {
            isAllRefund = "1";
        }
        RefundSuccessLogRequest req = new RefundSuccessLogRequest();
        RefundSuccessLogRequest.Param param = new RefundSuccessLogRequest.Param();
        req.setParam(param);
        param.setSystem_name("taskCenter");
        param.setCostrec_no(Convert.toStr(order.getOrderNo()));
        param.setTran_time(DateUtil.format(order.getPaymentTime(), DatePattern.PURE_DATETIME_PATTERN));
        // 实际付款金额
        param.setTran_value(totalAmount.toPlainString());
        // 总共订单金额(包含积分抵扣)
        param.setOriginal_ordamt(orderAmount.toPlainString());
        param.setLeft_tran_value(leftTranValue.toPlainString());
        param.setLeft_original_ordamt(leftOriginalOrdamt.toPlainString());
        param.setRefund_value(actualRefundAmount.toPlainString());
        param.setRefund_original_ordamt(refundOriginalOrdamt.toPlainString());
        param.setCust_id(Convert.toStr(macaupassUser.getCustomid()));
        param.setUser_name(macaupassUser.getPhone());
        param.setRefund_costrec_no(refund.getRefundOrderno());
        param.setRefund_time(DateUtil.format(refund.getRefundTime(), DatePattern.PURE_DATETIME_PATTERN));
        param.setIs_all_refund(isAllRefund);
        param.setMer_no(business.getCode());
        param.setMer_face_no("");
        param.setPoint_no("");
        param.setData_source("mCoin");
        FookMqLocal record = new FookMqLocal();
        Date now = new Date();
        record.setNextRetry(DateUtil.offsetMinute(now, 1));
        record.setTryCount(0);
        record.setMaxTryCount(3);
        record.setTemplateName("taskUploadTemplate");
        record.setResourceId(Convert.toStr(payLog.getId()));
        record.setResourceType(MqLocalResourceType.PAY_LOG.getType());
        RefundSuccessMessage msg = new RefundSuccessMessage();
        msg.setRequest(req);
        msg.setDelayMax(3);
        msg.setMessageType("refundSuccessUpload");
        record.setMessageBody(JSON.toJSONString(msg));
        this.mqLocalService.saveMqLocal(record);
        msg.setMqLocalId(record.getId());
        applicationContext.publishEvent(msg);
    }

    public void uploadRefundToTaskCenter(RefundSuccessLogRequest req) {
        if (StringUtils.isNotBlank(req.getParam().getMer_no())) {
            FookBusiness bus = this.fookBusinessDao.selectOne(new LambdaQueryWrapper<FookBusiness>()
                    .eq(FookBusiness::getCode, req.getParam().getMer_no())
                    .last("LIMIT 1"));
            if (null != bus && isSellerInBlackList(bus.getId())) {
                log.info("The seller {} is in black list, do not upload refund record to task center.", bus.getId());
                return;
            }
        }
        Map<String, Object> body = BeanUtil.beanToMap(req, Maps.newHashMap(), false, false);
        SignReqDto reqDto = null;
        try {
            reqDto = TaskSignUtils.signRequestToSignReqDto(body);
        } catch (Exception e) {
            log.error("Upload refund log sign error {}  ", JSON.toJSONString(req));
            log.error("Upload refund log sign error ", e);
            throw new RetryException();
        }
        SignReqDto finalReqDto = reqDto;
        String result = handleBlockException(() -> this.taskUploadClient.refundTaskNotify(finalReqDto), "flowBlocked");
        if ("flowBlocked".equals(result)) {
            log.error("flowBlocked, will retry,result is {} ", result);
            throw new RetryException();
        }
        if (StringUtils.isNotBlank(result)) {
            boolean signResult = TaskSignUtils.verifSignResponse(result);
            if (!signResult) {
                throw new BusinessException(Response.Code.UNKNOWN_ERROR,
                        StrFormatter.format("/refundTaskNotify verify response sign error, result is {}", result));
            }
            PayLogResponse resp = JSON.parseObject(result, PayLogResponse.class);
            Set<String> retryableCodes = CollUtil.newHashSet("2012", "9000", "9001", "9002", "9999");
            if ("0000".equals(resp.getCode())) {
                log.info("uploadRefundToTaskCenter success {}", resp.getMsg());
            } else if (retryableCodes.contains(resp.getCode())) {
                log.error("Upload pay log error, will retry,result is {} ", result);
                throw new RetryException();
            } else {
                log.error("Uploaded pay log error,do not retry,result is {} ", result);
                String message = StrFormatter.format("request /refundTaskNotify error, result is {}", result);
                throw new BusinessException(Response.Code.UNKNOWN_ERROR, message);
            }

        }
    }

    public static boolean isSellerInBlackList(Integer sellerId) {
        String property = ConfigUtils.getProperty("task.unupload.uploadRefundSuccess.sellers");
        if (StringUtils.isNotBlank(property)) {
            String[] split = property.split(",");
            boolean b = Arrays.stream(split).anyMatch(s -> Integer.valueOf(s).equals(sellerId));
            if (b) {
                return true;
            }
        }
        return false;
    }
}
