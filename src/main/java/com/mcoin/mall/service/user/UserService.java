package com.mcoin.mall.service.user;

import com.mcoin.mall.bo.UserInfoBo;
import com.mcoin.mall.model.*;
import com.mcoin.mall.model.api.ApiCollectCntResponse;
import com.mcoin.mall.model.api.ApiCollectionCntRequest;

public interface UserService {

    MyCouponOtherResponse myCouponOther(MyCouponOtherCtx ctx);

    MyCouponResponse myCoupon(MyCouponCtx ctx);

    CollectionResponse saveCollection(CollectionRequest request, Integer userId);

    CouponsResponse getCoupons(CouponsCtx ctx);

    UserInfoBo getUserInfoByCustomId(String customId);

    CollectionListResponse getCollectionList(CollectionListRequest request);
    ApiCollectCntResponse getCollections(ApiCollectionCntRequest request, Integer userId);
}
