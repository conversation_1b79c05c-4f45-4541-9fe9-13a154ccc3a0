package com.mcoin.mall.service.user.impl;

import static com.mcoin.mall.service.common.CacheService.CACHE_USER_INFO;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.mcoin.mall.bean.Coupons;
import com.mcoin.mall.bean.CouponsCnt;
import com.mcoin.mall.bean.CouponsQuery;
import com.mcoin.mall.bean.FookBusiness;
import com.mcoin.mall.bean.FookBusinessProduct;
import com.mcoin.mall.bean.FookBusinessProductTranslations;
import com.mcoin.mall.bean.FookBusinessStoreProduct;
import com.mcoin.mall.bean.FookMacaupassUser;
import com.mcoin.mall.bean.FookPlatformOrder;
import com.mcoin.mall.bean.FookPlatformOrdercode;
import com.mcoin.mall.bean.FookPlatformOrderinfo;
import com.mcoin.mall.bean.FookPlatformUsercollection;
import com.mcoin.mall.bean.FookPlatformUserinfo;
import com.mcoin.mall.bean.FookStores;
import com.mcoin.mall.bean.MyCouponOther;
import com.mcoin.mall.bean.MyCouponOtherCnt;
import com.mcoin.mall.bean.MyCouponOtherQuery;
import com.mcoin.mall.bean.UsercollectionCnt;
import com.mcoin.mall.bo.ActiveZoneStoreBo;
import com.mcoin.mall.bo.UserInfoBo;
import com.mcoin.mall.component.ContextHolder;
import com.mcoin.mall.constant.ClientSourceEnum;
import com.mcoin.mall.constant.CollectionType;
import com.mcoin.mall.dao.FookActiveZoneDao;
import com.mcoin.mall.dao.FookBusinessDao;
import com.mcoin.mall.dao.FookBusinessProductDao;
import com.mcoin.mall.dao.FookBusinessProductTranslationsDao;
import com.mcoin.mall.dao.FookBusinessStoreProductDao;
import com.mcoin.mall.dao.FookMacaupassUserDao;
import com.mcoin.mall.dao.FookPlatformOrderDao;
import com.mcoin.mall.dao.FookPlatformOrdercodeDao;
import com.mcoin.mall.dao.FookPlatformOrderinfoDao;
import com.mcoin.mall.dao.FookPlatformUsercollectionDao;
import com.mcoin.mall.dao.FookPlatformUserinfoDao;
import com.mcoin.mall.dao.FookStoresDao;
import com.mcoin.mall.exception.BusinessException;
import com.mcoin.mall.mapping.BusinessProductMapping;
import com.mcoin.mall.mapping.FookPlatformOrderinfoMapping;
import com.mcoin.mall.mapping.FookStoreMapping;
import com.mcoin.mall.model.CollectionListRequest;
import com.mcoin.mall.model.CollectionListResponse;
import com.mcoin.mall.model.CollectionRequest;
import com.mcoin.mall.model.CollectionResponse;
import com.mcoin.mall.model.CouponsCtx;
import com.mcoin.mall.model.CouponsRequest;
import com.mcoin.mall.model.CouponsResponse;
import com.mcoin.mall.model.MyCouponCtx;
import com.mcoin.mall.model.MyCouponOtherCtx;
import com.mcoin.mall.model.MyCouponOtherRequest;
import com.mcoin.mall.model.MyCouponOtherResponse;
import com.mcoin.mall.model.MyCouponRequest;
import com.mcoin.mall.model.MyCouponResponse;
import com.mcoin.mall.model.Page;
import com.mcoin.mall.model.Response;
import com.mcoin.mall.model.SearchProductResponse;
import com.mcoin.mall.model.api.ApiCollectCntResponse;
import com.mcoin.mall.model.api.ApiCollectionCntRequest;
import com.mcoin.mall.service.user.UserService;
import com.mcoin.mall.util.ConfigUtils;
import com.mcoin.mall.util.CouponDetailUrlUtil;
import com.mcoin.mall.util.HtmlUtil;
import com.mcoin.mall.util.McoinMall;
import com.mcoin.mall.util.OssUtil;
import com.mcoin.mall.util.PageUtil;
import com.mcoin.mall.util.StoreUtil;
import com.mcoin.mall.util.StrUtil;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class UserServiceImpl implements UserService {

    @Resource
    private FookPlatformOrdercodeDao fookPlatformOrdercodeDao;

    @Resource
    private FookPlatformOrderDao fookPlatformOrderDao;

    @Resource
    private FookPlatformOrderinfoDao fookPlatformOrderinfoDao;

    @Resource
    private FookStoresDao fookStoresDao;

    @Resource
    private FookBusinessStoreProductDao fookBusinessStoreProductDao;

    @Resource
    private FookBusinessProductTranslationsDao fookBusinessProductTranslationsDao;

    @Resource
    private MessageSource messageSource;

    @Resource
    private FookPlatformUsercollectionDao collectionDao;
    @Resource
    private ContextHolder contextHolder;
    @Resource
    private FookMacaupassUserDao fookMacaupassUserDao;
    @Resource
    private FookPlatformUserinfoDao fookPlatformUserinfoDao;

    @Resource
    private FookBusinessProductDao fookBusinessProductDao;

    @Resource
    private FookPlatformUsercollectionDao fookPlatformUsercollectionDao;

    @Resource
    private FookBusinessDao fookBusinessDao;

    @Resource
    private FookActiveZoneDao fookActiveZoneDao;

    @Override
    public MyCouponOtherResponse myCouponOther(MyCouponOtherCtx ctx) {
        MyCouponOtherRequest request = ctx.getRequest();
        MyCouponOtherResponse result = new MyCouponOtherResponse();
        MyCouponOtherQuery query = new MyCouponOtherQuery();
        int status = null != request.getStatus() ? request.getStatus() : 2;
        Calendar calendar = Calendar.getInstance();
        if (status == 3 || status == 4) {
            //退款、過期的為3個月
            calendar.add(Calendar.MONTH, -3);
        } else {
            //其他为1年
            calendar.add(Calendar.YEAR, -1);
        }
        // 获取一年前的日期
        Date dateLastYear = calendar.getTime();
        // 将日期格式化为指定格式
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        query.setDateLastYear(dateFormat.format(dateLastYear));
        query.setValidDate(dateFormat.format(new Date()));
        query.setStatus(status);
        query.setUserId(ctx.getUserId());
        query.setSellerId(Convert.toInt(request.getSellerid()));
        request.setPage(ObjectUtil.defaultIfNull(request.getPage(), 1));
        int page = request.getPage() > 0 ? request.getPage() : 1;
        int size = 5;
        int start = (page - 1) * size;
        int end = page * size;
        query.setStart(start);
        query.setEnd(end);
        List<MyCouponOther> orderCodes;
        try {
            orderCodes = this.fookPlatformOrdercodeDao.getMyCouponOther(query);
        } catch (Exception e) {
            log.error("查询未知异常", e);
            orderCodes = Collections.emptyList();
        }
        if (CollectionUtils.isNotEmpty(orderCodes)) {
            List<MyCouponOtherCnt> count;
            try {
                count = this.fookPlatformOrdercodeDao.getMyCouponCount(query);
            } catch (Exception e) {
                log.error("查询未知异常", e);
                count = Collections.emptyList();
            }

            List<Integer> orderIds = orderCodes.stream().map(MyCouponOther::getOrderid).collect(Collectors.toList());
            List<FookPlatformOrderinfo> orderInfos = this.fookPlatformOrderinfoDao.getOrderInfoByOrderId(orderIds);
            List<Integer> businessProductIds = orderInfos.stream().map(FookPlatformOrderinfo::getProdcutid).collect(Collectors.toList());
            //再查翻译表
            List<FookBusinessProductTranslations> businessProducts = this.fookBusinessProductTranslationsDao.getTranslationsByIds(businessProductIds, ctx.getLanguage());
            if (CollectionUtils.isNotEmpty(orderInfos) && CollectionUtils.isNotEmpty(businessProducts)) {
                orderInfos.stream().forEach(o -> {
                    FookBusinessProductTranslations productTranslations = businessProducts.stream().filter(p -> p.getBusinessProductId().equals(o.getProdcutid())).findFirst().orElse(null);
                    if (null != productTranslations) {
                        o.setTitleSnapshots(StringUtils.defaultIfBlank(productTranslations.getTTitle(), o.getTitleSnapshots()));
                        String tnc = StringUtils.defaultIfBlank(productTranslations.getTTnc(), o.getTnc());
                        o.setTnc(HtmlUtil.replaceNewLine(tnc));
                    }
                });
            }

            List<Integer> storeIds = Lists.newArrayList();
            List<Integer> productIds = Lists.newArrayList();
            for (MyCouponOther myCouponOther : orderCodes) {
                Integer shopid = myCouponOther.getShopid();
                if (null != shopid) {
                    storeIds.add(shopid);
                } else {
                    productIds.add(myCouponOther.getProdcutid());
                }
            }
            if (CollectionUtils.isNotEmpty(productIds)) {
                List<FookBusinessStoreProduct> storesIdByProductIds = this.fookBusinessStoreProductDao.getStoresIdByProductIds(productIds);
                orderCodes.forEach(c -> {
                    if (null == c.getShopid()) {
                        c.setShopid(storesIdByProductIds.stream().filter(s ->
                                Objects.equals(s.getProductid(), c.getProdcutid())).mapToInt(FookBusinessStoreProduct::getStoreid).findFirst().orElse(0));
                    }
                });
                for (FookBusinessStoreProduct storeProduct : storesIdByProductIds) {
                    storeIds.add(storeProduct.getStoreid());
                }
            }
            List<FookStores> stores = this.fookStoresDao.getStores(storeIds);
            List<MyCouponOtherResponse.SnatchListItem> snatchList = Lists.newArrayList();
            for (MyCouponOther orderCode : orderCodes) {
                MyCouponOtherResponse.SnatchListItem item = new MyCouponOtherResponse.SnatchListItem();
                item.setId(orderCode.getId());
                item.setOrderid(orderCode.getOrderid());
                FookPlatformOrderinfo orderInfo = orderInfos.stream().filter(o -> o.getOrderid().equals(orderCode.getOrderid())).findFirst().orElse(null);
                if (null != orderInfo) {
                    MyCouponOtherResponse.Orderinfo orderinfoVo = FookPlatformOrderinfoMapping.INSTANCE.getOrderInfo(orderInfo);
                    String img = OssUtil.initOssImage(orderInfo.getImg());
                    orderinfoVo.setImg(img);
                    item.setOrderinfo(orderinfoVo);
                    item.setVaildStartTime(DateUtil.format(orderInfo.getVaildStartTime(), DatePattern.NORM_DATE_PATTERN));
                    Date validEndTime = orderInfo.getVaildEndTime();
                    item.setVaildEndTime(DateUtil.format(validEndTime, DatePattern.NORM_DATE_PATTERN));
                    if (orderInfo.getType() == 9) {
                        item.setRedirectUrl(StrUtil.encodeURIComponent(ConfigUtils.getProperty("macaupass.REDIRECT_URI")));
                    }
                    Date weekTime = DateUtil.offsetWeek(new Date(), 1);
                    if (null != validEndTime && validEndTime.compareTo(weekTime) <= 0) {
                        String message = this.messageSource.getMessage("message.business.expiring_soon", null, LocaleContextHolder.getLocale());
                        item.setFastExpiration(message);
                    }
                }

                FookStores store = stores.stream().filter(s -> s.getId().equals(orderCode.getShopid())).findFirst().orElse(null);
                Integer storeEnable = 2;
                if (null != store) {
                    item.setStoreName(StoreUtil.extractStoreName(store.getName()));
                    String img = FookStoreMapping.INSTANCE.extractImg(store.getImg());
                    item.setStoreImg(img);
                    item.setStoreId(store.getId());
                    storeEnable = store.getEnable();
                }

                item.setRefundStatus(orderCode.getRefundStatus());
                Integer codeStatus = orderCode.getStatus();
                if (codeStatus == 1) {
                    if (orderCode.getRefundStatus() == 2) {
                        //推款中
                        codeStatus = 5;
                    } else if (null != orderInfo
                            && null != orderInfo.getVaildEndTime()
                            && orderInfo.getVaildEndTime().compareTo(new Date()) < 0) {
                        //已过期
                        codeStatus = 4;
                    }
                }
                String statusTips = "";
                switch (codeStatus) {
                    case 1:
                        statusTips = "message.basic.unused";
                        break;
                    case 2:
                        statusTips = "message.basic.used";
                        break;
                    case 3:
                        statusTips = "message.basic.refunded";
                        break;
                    case 4:
                        statusTips = "message.basic.expired";
                        break;
                    case 5:
                        statusTips = "message.basic.refunding";
                        break;
                    default:
                        // 其他情况
                        break;
                }
                item.setStatus(codeStatus);
                String statusMessage = this.messageSource.getMessage(statusTips, null, LocaleContextHolder.getLocale());
                item.setStatusTips(statusMessage);
                item.setPaymentTime(DateUtil.format(orderCode.getPaymentTime(), DatePattern.NORM_DATETIME_PATTERN));
                item.setSellerid(orderCode.getSellerid());
                count.stream().filter(c -> c.getSellerid().equals(orderCode.getSellerid())).findFirst().ifPresent(myCouponOtherCnt -> item.setPageCount(myCouponOtherCnt.getCount()));
                item.setPage(page);
                item.setSize(size);
                item.setStoreEnable(storeEnable);
                item.setUserTime(DateUtil.format(orderCode.getUserTime(), DatePattern.NORM_DATE_PATTERN));

                snatchList.add(item);
            }
            result.setSnatchList(snatchList);
        }
        return result;
    }

    @Override
    public MyCouponResponse myCoupon(MyCouponCtx ctx) {
        log.info("myCoupon ", JSON.toJSONString(ctx));
        MyCouponResponse result = new MyCouponResponse();
        String now = DateUtil.now();
        MyCouponRequest request = ctx.getRequest();
        Integer userId = ctx.getUserId();
        List<MyCouponOther> myCoupon;
        try {
            myCoupon = this.fookPlatformOrdercodeDao.getMyCoupon(userId, request.getStatus(), now);
        } catch (Exception e) {
            log.error("查询未知异常", e);
            myCoupon = Collections.emptyList();
        }
        if (myCoupon.isEmpty()) {
            log.info("我的优惠券为空");
            result.setSnatchList(Collections.emptyList());
            return result;
        }

        List<Integer> orderIds = myCoupon.stream().map(MyCouponOther::getOrderid).collect(Collectors.toList());
        List<FookPlatformOrderinfo> orderInfos = this.fookPlatformOrderinfoDao.getOrderInfoByOrderId(orderIds);
        
        // 批量查询订单数据用于构建couponDetailUrl
        Map<Integer, FookPlatformOrder> orderMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(orderIds)) {
            List<FookPlatformOrder> orders = fookPlatformOrderDao.selectBatchIds(orderIds);
            orderMap = orders.stream().collect(Collectors.toMap(FookPlatformOrder::getId, order -> order));
        }
        
        // 查询用户信息用于构建couponDetailUrl（只需查询一次）
        FookMacaupassUser user = fookMacaupassUserDao.selectOne(new LambdaQueryWrapper<FookMacaupassUser>()
                .eq(FookMacaupassUser::getUserId, userId)
                .last("limit 1")
        );
        List<Integer> businessProductIds = orderInfos.stream().map(FookPlatformOrderinfo::getProdcutid).collect(Collectors.toList());
        //再查翻译表
        List<FookBusinessProductTranslations> businessProducts = this.fookBusinessProductTranslationsDao.getTranslationsByIds(businessProductIds, ctx.getLanguage());
        if (CollectionUtils.isNotEmpty(orderInfos) && CollectionUtils.isNotEmpty(businessProducts)) {
            orderInfos.stream().forEach(o -> {
                FookBusinessProductTranslations productTranslations = businessProducts.stream().filter(p -> p.getBusinessProductId().equals(o.getProdcutid())).findFirst().orElse(null);
                if (null != productTranslations) {
                    o.setTitleSnapshots(StringUtils.defaultIfBlank(productTranslations.getTTitle(), o.getTitleSnapshots()));
                    String tnc = StringUtils.defaultIfBlank(productTranslations.getTTnc(), o.getTnc());
                    o.setTnc(HtmlUtil.replaceNewLine(tnc));
                }
            });
        }

        List<Integer> storeIds = Lists.newArrayList();
        List<Integer> productIds = Lists.newArrayList();
        for (MyCouponOther myCouponOther : myCoupon) {
            Integer shopid = myCouponOther.getShopid();
            if (null != shopid) {
                storeIds.add(shopid);
            } else {
                productIds.add(myCouponOther.getProdcutid());
            }
        }
        if (CollectionUtils.isNotEmpty(productIds)) {
            List<FookBusinessStoreProduct> storesIdByProductIds = this.fookBusinessStoreProductDao.getStoresIdByProductIds(productIds);
            myCoupon.forEach(c -> {
                if (null == c.getShopid()) {
                    c.setShopid(storesIdByProductIds.stream().filter(s ->
                            Objects.equals(s.getProductid(), c.getProdcutid())).mapToInt(FookBusinessStoreProduct::getStoreid).findFirst().orElse(0));
                }
            });
            for (FookBusinessStoreProduct storeProduct : storesIdByProductIds) {
                storeIds.add(storeProduct.getStoreid());
            }
        }
        List<FookStores> stores = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(storeIds)) {
            stores = this.fookStoresDao.getStores(storeIds);
        }
        List<MyCouponResponse.SnatchListItem> snatchList = Lists.newArrayList();
        Integer timeStatus = 0;
        for (MyCouponOther orderCode : myCoupon) {
            MyCouponResponse.SnatchListItem item = new MyCouponResponse.SnatchListItem();
            item.setId(orderCode.getId());
            item.setOrderid(orderCode.getOrderid());
            FookPlatformOrderinfo orderInfo = orderInfos.stream().filter(o -> o.getOrderid().equals(orderCode.getOrderid())).findFirst().orElse(null);
            if (null != orderInfo) {
                MyCouponResponse.Orderinfo orderinfoVo = FookPlatformOrderinfoMapping.INSTANCE.getMyCouponOrderInfo(orderInfo);
                String img = OssUtil.initOssImage(orderInfo.getImg());
                orderinfoVo.setImg(img);
                orderinfoVo.setImageSnapshots(OssUtil.initOssImage(orderInfo.getImageSnapshots()));
                item.setOrderinfo(orderinfoVo);
                Date vaildStartTime = orderInfo.getVaildStartTime();
                item.setVaildStartTime(DateUtil.format(vaildStartTime, DatePattern.NORM_DATE_PATTERN));
                item.setVaildStartTimeHis(DateUtil.format(vaildStartTime, DatePattern.NORM_DATETIME_PATTERN));
                if (null != vaildStartTime && vaildStartTime.compareTo(new Date()) > 0) {
                    timeStatus = 1;
                }
                Date validEndTime = orderInfo.getVaildEndTime();
                item.setVaildEndTime(DateUtil.format(validEndTime, DatePattern.NORM_DATE_PATTERN));
                item.setVaildEndTimeHis(DateUtil.format(validEndTime, DatePattern.NORM_DATETIME_PATTERN));
                if (orderInfo.getType() == 9) {
                    item.setRedirectUrl(StrUtil.encodeURIComponent(ConfigUtils.getProperty("macaupass.REDIRECT_URI")));
                }
                Date weekTime = DateUtil.offsetWeek(new Date(), 1);
                if (null != validEndTime && validEndTime.compareTo(weekTime) <= 0) {
                    String message = this.messageSource.getMessage("message.business.expiring_soon", null, LocaleContextHolder.getLocale());
                    item.setFastExpiration(message);
                }
            }

            Integer enableYes = 1;
            FookStores store = stores.stream().filter(s -> s.getId().equals(orderCode.getShopid()) && Objects.equals(enableYes, s.getEnable())).findFirst().orElse(null);
            if (null == store) {
                store = stores.stream().filter(s -> s.getId().equals(orderCode.getShopid())).findFirst().orElse(null);
            }
            Integer storeEnable = 0;
            if (null != store) {
                item.setStoreName(StoreUtil.extractStoreName(store.getName()));
                String img = FookStoreMapping.INSTANCE.extractImg(StringUtils.isNotBlank(store.getImgZip()) ? store.getImgZip() : store.getImg());
                item.setStoreImg(img);
                item.setStoreId(store.getId());
                storeEnable = store.getEnable();
            } else {
                item.setStoreName("無適用門店");
            }

            item.setRefundStatus(orderCode.getRefundStatus());
            Integer codeStatus = orderCode.getStatus();
            if (codeStatus == 1) {
                if (orderCode.getRefundStatus() == 2) {
                    //推款中
                    codeStatus = 5;
                } else if (null != orderInfo
                        && null != orderInfo.getVaildEndTime()
                        && orderInfo.getVaildEndTime().compareTo(new Date()) < 0) {
                    //已过期
                    codeStatus = 4;
                }
            }
            String statusTips = "";
            switch (codeStatus) {
                case 1:
                    statusTips = "message.basic.unused";
                    break;
                case 2:
                    statusTips = "message.basic.used";
                    break;
                case 3:
                    statusTips = "message.basic.refunded";
                    break;
                case 4:
                    statusTips = "message.basic.expired";
                    break;
                case 5:
                    statusTips = "message.basic.refunding";
                    break;
                default:
                    // 其他情况
                    break;
            }

            //php版本返回有两个status，经过确认线上使用的是未转换的status
            item.setStatus(orderCode.getStatus());
            String statusMessage = this.messageSource.getMessage(statusTips, null, LocaleContextHolder.getLocale());
            item.setStatusTips(statusMessage);
            item.setPaymentTime(DateUtil.format(orderCode.getPaymentTime(), DatePattern.NORM_DATETIME_PATTERN));
            item.setStoreEnable(storeEnable);
            item.setTimeStatus(timeStatus);

            // 只有当客户端来源为MPAY_SERVER时才设置couponDetailUrl
            if (ClientSourceEnum.isMPayServer(ctx.getClientSource())) {
                setCouponDetailUrl(item, orderCode, orderMap, user);
                log.debug("设置couponDetailUrl，客户端来源: {}", ctx.getClientSource());
            } else {
                log.debug("跳过设置couponDetailUrl，客户端来源: {}", ctx.getClientSource());
            }

            snatchList.add(item);
        }
        result.setSnatchList(snatchList);
        return result;
    }

    @Override
    public CollectionResponse saveCollection(CollectionRequest request, Integer userId) {
        CollectionResponse response = new CollectionResponse();
        FookPlatformUsercollection usercollection = new FookPlatformUsercollection();
        int collectCount = 0;
        String collectCountStr = "";
        int collect = 0;
        String message = "";
        usercollection.setUserid(userId);
        usercollection.setType(request.getType());
        usercollection.setCollectionid(request.getId());
        List<FookPlatformUsercollection> userCollections = collectionDao.selectForCollection(usercollection);
        usercollection.setEnable(1);
        if (CollectionUtils.isEmpty(userCollections)) {
            if (null == request.getIsCollect() || 1 == request.getIsCollect()) {
                if (!isExistsCollectionResource(request)) {
                    message = messageSource.getMessage("message.order.nodata", null, LocaleContextHolder.getLocale());
                    throw new BusinessException(Response.Code.BAD_REQUEST, message);
                }
                usercollection.setCreateTime(new Date());
                collectionDao.insertSelective(usercollection);
                log.info("收藏成功");
                collect = 1;
                message = messageSource.getMessage("message.business.collection_success", null, LocaleContextHolder.getLocale());
            } else if (0 == request.getIsCollect()) {
                log.info("取消收藏成功");
                message = messageSource.getMessage("message.business.collection_cancel", null, LocaleContextHolder.getLocale());
            }
        } else if (CollectionUtils.isNotEmpty(userCollections)) {
            // 直接删除用户的收藏，返回删除的行数
            if (null == request.getIsCollect() || 0 == request.getIsCollect()) {
                collectionDao.deleteByUsercollection(usercollection);
                message = messageSource.getMessage("message.business.collection_cancel", null, LocaleContextHolder.getLocale());
                log.info("取消收藏成功");
            } else {
                collect = 1;
                message = messageSource.getMessage("message.business.collection_success", null, LocaleContextHolder.getLocale());
                log.info("收藏成功");
            }
        }
        //查詢收藏人數
        collectCount = collectionDao.countCollection(usercollection);
        collectCountStr = String.valueOf(collectCount);
        response.setCollect(collect);
        response.setCollectCount(collectCountStr);
        contextHolder.setResponseMessage(message);
        return response;
    }

    /**
     * 收藏资源是否存在
     *
     * @param request
     * @return
     */
    private boolean isExistsCollectionResource(CollectionRequest request) {
        if (request.getType() == null) {
            return false;
        }
        if (request.getType() == CollectionType.PRODUCT.getCode()) {
            return fookBusinessProductDao.selectCount(new LambdaQueryWrapper<FookBusinessProduct>()
                    .eq(FookBusinessProduct::getId, request.getId())
            ) > 0;
        } else if (request.getType() == CollectionType.STORE.getCode()) {
            return fookStoresDao.selectCount(new LambdaQueryWrapper<FookStores>()
                    .eq(FookStores::getId, request.getId())
            ) > 0;
        }
        return false;
    }

    @Override
    public CouponsResponse getCoupons(CouponsCtx ctx) {
        log.info("getCoupons ", JSON.toJSONString(ctx));
        CouponsRequest request = ctx.getRequest();
        int userId = ctx.getUserId();
        String language = ctx.getLanguage();
        CouponsResponse result = new CouponsResponse();
        CouponsQuery query = new CouponsQuery();
        int status = null != request.getStatus() ? request.getStatus() : 2;
        Calendar calendar = Calendar.getInstance();
        if (status == 3 || status == 4) {
            //退款、過期的為3個月
            calendar.add(Calendar.MONTH, -3);
        } else {
            //其他为1年
            calendar.add(Calendar.YEAR, -1);
        }
        // 获取一年前的日期
        Date dateLastYear = calendar.getTime();
        // 将日期格式化为指定格式
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        query.setDateLastYear(dateFormat.format(dateLastYear));
        query.setValidDate(dateFormat.format(new Date()));
        query.setStatus(status);
        query.setUserId(userId);
        query.setStoreId(Convert.toInt(request.getGroup_id()));
        request.setPage(ObjectUtil.defaultIfNull(request.getPage(), 1));
        int page = request.getPage() > 0 ? request.getPage() : 1;
        int size = 5;
        int start = (page - 1) * size;
        int end = page * size;
        query.setStart(start);
        query.setEnd(end);
        List<Coupons> orderCodes = this.fookPlatformOrdercodeDao.coupons(query);
        if (CollectionUtils.isNotEmpty(orderCodes)) {
            List<CouponsCnt> count = this.fookPlatformOrdercodeDao.couponsCnt(query);

            List<Integer> orderIds = orderCodes.stream().map(Coupons::getOrderid).collect(Collectors.toList());
            List<FookPlatformOrderinfo> orderInfos = this.fookPlatformOrderinfoDao.getOrderInfoByOrderId(orderIds);

            // 批量查询订单数据用于构建couponDetailUrl
            Map<Integer, FookPlatformOrder> orderMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(orderIds)) {
                List<FookPlatformOrder> orders = fookPlatformOrderDao.selectBatchIds(orderIds);
                orderMap = orders.stream().collect(Collectors.toMap(FookPlatformOrder::getId, order -> order));
            }

            // 查询用户信息用于构建couponDetailUrl（只需查询一次）
            FookMacaupassUser user = fookMacaupassUserDao.selectOne(new LambdaQueryWrapper<FookMacaupassUser>()
                    .eq(FookMacaupassUser::getUserId, userId)
                    .last("limit 1")
            );

            List<Integer> businessProductIds = orderInfos.stream().map(FookPlatformOrderinfo::getProdcutid).collect(Collectors.toList());
            //再查翻译表
            List<FookBusinessProductTranslations> businessProducts = this.fookBusinessProductTranslationsDao.getTranslationsByIds(businessProductIds, language);
            if (CollectionUtils.isNotEmpty(orderInfos) && CollectionUtils.isNotEmpty(businessProducts)) {
                orderInfos.stream().forEach(o -> {
                    FookBusinessProductTranslations productTranslations = businessProducts.stream().filter(p -> p.getBusinessProductId().equals(o.getProdcutid())).findFirst().orElse(null);
                    if (null != productTranslations) {
                        o.setTitleSnapshots(StringUtils.defaultIfBlank(productTranslations.getTTitle(), o.getTitleSnapshots()));
                        String tnc = StringUtils.defaultIfBlank(productTranslations.getTTnc(), o.getTnc());
                        o.setTnc(HtmlUtil.replaceNewLine(tnc));
                    }
                });
            }

            List<Integer> storeIds = orderCodes.stream().map(Coupons::getStoreid).filter(Objects::nonNull).collect(Collectors.toList());
            List<FookStores> stores = Lists.newArrayList();
            if(CollectionUtils.isNotEmpty(storeIds)) {
                stores = this.fookStoresDao.getStores(storeIds);
            }
            List<CouponsResponse.SnatchGroup> groups = buildSnatchGroups(orderCodes, page, size, count, orderInfos, stores, orderMap, user, ctx);
            result.setSnatchList(groups);
        }
        return result;
    }

    private List<CouponsResponse.SnatchGroup> buildSnatchGroups(List<Coupons> orderCodes, int page, int size, List<CouponsCnt> count, List<FookPlatformOrderinfo> orderInfos, List<FookStores> stores, Map<Integer, FookPlatformOrder> orderMap, FookMacaupassUser user, CouponsCtx ctx) {
        Map<Integer, CouponsResponse.SnatchGroup> maps = Maps.newHashMap();
        Integer noStoreId = -1;
        for (Coupons orderCode : orderCodes) {
            Integer storeid = orderCode.getStoreid();
            FookStores store = null;
            if (null != storeid) {
                store = stores.stream().filter(s -> s.getId().equals(storeid)).findFirst().orElse(null);
            }
            CouponsResponse.SnatchGroup snatchGroup = null;
            if (null != store) {
                snatchGroup = maps.get(storeid);
            } else {
                snatchGroup = maps.get(noStoreId);
            }
            boolean isHead = false;
            if (null == snatchGroup) {
                snatchGroup = new CouponsResponse.SnatchGroup();
                if(null != store) {
                    snatchGroup.setGroupId(storeid);
                } else {
                    snatchGroup.setGroupId(null);
                }
                snatchGroup.setPage(page);
                snatchGroup.setSize(size);
                snatchGroup.setOrderCodes(Lists.newArrayList());
                if (null != store) {
                    snatchGroup.setPageCount(count.stream().filter(c -> null != c.getStoreid() && c.getStoreid().equals(storeid)).mapToInt(CouponsCnt::getCount).findFirst().orElse(0));
                    maps.put(storeid, snatchGroup);
                } else {
                    snatchGroup.setPageCount(count.stream().filter(c -> c.getStoreid() == null).mapToInt(CouponsCnt::getCount).findFirst().orElse(0));
                    maps.put(noStoreId, snatchGroup);
                }
                isHead = true;
            }
            List<CouponsResponse.SnatchListItem> snatchList = snatchGroup.getOrderCodes();

            CouponsResponse.SnatchListItem item = new CouponsResponse.SnatchListItem();
            item.setId(orderCode.getId());
            item.setOrderid(orderCode.getOrderid());
            FookPlatformOrderinfo orderInfo = orderInfos.stream().filter(o -> o.getOrderid().equals(orderCode.getOrderid())).findFirst().orElse(null);
            if (null != orderInfo) {
                CouponsResponse.Orderinfo orderinfoVo = FookPlatformOrderinfoMapping.INSTANCE.getOrderInfoFromCoupons(orderInfo);
                String img = OssUtil.initOssImage(orderInfo.getImg());
                orderinfoVo.setImg(img);
                item.setOrderinfo(orderinfoVo);
                item.setVaildStartTime(DateUtil.format(orderInfo.getVaildStartTime(), DatePattern.NORM_DATE_PATTERN));
                Date validEndTime = orderInfo.getVaildEndTime();
                item.setVaildEndTime(DateUtil.format(validEndTime, DatePattern.NORM_DATE_PATTERN));
                if (orderInfo.getType() == 9) {
                    item.setRedirectUrl(StrUtil.encodeURIComponent(ConfigUtils.getProperty("macaupass.REDIRECT_URI")));
                }
                Date weekTime = DateUtil.offsetWeek(new Date(), 1);
                if (null != validEndTime && validEndTime.compareTo(weekTime) <= 0) {
                    String message = this.messageSource.getMessage("message.business.expiring_soon", null, LocaleContextHolder.getLocale());
                    item.setFastExpiration(message);
                }
                if (isHead) {
                    snatchGroup.setGroupType(orderinfoVo.getType());

                }
            }

            Integer storeEnable = 0;
            if (null != store) {
                item.setStoreName(StoreUtil.extractStoreName(store.getName()));
                String img = FookStoreMapping.INSTANCE.extractImg(store.getImg());
                item.setStoreImg(img);
                item.setStoreId(store.getId());
                storeEnable = store.getEnable();
                if (isHead) {
                    snatchGroup.setGroupImg(img);
                    snatchGroup.setGroupName(item.getStoreName());
                }
            } else {
                item.setStoreName("無適用門店");
                item.setStoreId(null);
                storeEnable = 0;
                if (isHead) {
                    snatchGroup.setGroupImg("");
                    snatchGroup.setGroupName("無適用門店");
                }
            }

            item.setRefundStatus(orderCode.getRefundStatus());
            Integer codeStatus = orderCode.getStatus();
            if (codeStatus == 1) {
                if (orderCode.getRefundStatus() == 2) {
                    //退款中
                    codeStatus = 5;
                } else if (null != orderInfo
                        && null != orderInfo.getVaildEndTime()
                        && orderInfo.getVaildEndTime().compareTo(new Date()) < 0) {
                    //已过期
                    codeStatus = 4;
                }
            }
            String statusTips = "";
            switch (codeStatus) {
                case 1:
                    statusTips = "message.basic.unused";
                    break;
                case 2:
                    statusTips = "message.basic.used";
                    break;
                case 3:
                    statusTips = "message.basic.refunded";
                    break;
                case 4:
                    statusTips = "message.basic.expired";
                    break;
                case 5:
                    statusTips = "message.basic.refunding";
                    break;
                default:
                    // 其他情况
                    break;
            }

            if (isHead) {
                if (1 != storeEnable) {
                    snatchGroup.setGroupUri("");
                } else {
                    if (null != snatchGroup.getGroupType() && 5 == snatchGroup.getGroupType()) {
                        CouponsResponse.Orderinfo oi = ObjectUtil.defaultIfNull(item.getOrderinfo(), new CouponsResponse.Orderinfo());
                        snatchGroup.setGroupUri(Convert.toStr(oi.getProdcutid()));
                    } else {
                        snatchGroup.setGroupUri(Convert.toStr(item.getStoreId()));
                    }
                }
            }
            item.setStatus(codeStatus);
            String statusMessage = this.messageSource.getMessage(statusTips, null, LocaleContextHolder.getLocale());
            item.setStatusTips(statusMessage);
            item.setPaymentTime(DateUtil.format(orderCode.getPaymentTime(), DatePattern.NORM_DATETIME_PATTERN));
            item.setSellerid(orderCode.getSellerid());
            item.setStoreEnable(storeEnable);
            item.setUserTime(DateUtil.format(orderCode.getUserTime(), DatePattern.NORM_DATE_PATTERN));

            // 只有当客户端来源为MPAY_SERVER时才设置couponDetailUrl
            if (ClientSourceEnum.isMPayServer(ctx.getClientSource())) {
                setCouponDetailUrlForCoupons(item, orderCode, orderMap, user);
                log.debug("设置couponDetailUrl，客户端来源: {}", ctx.getClientSource());
            } else {
                log.debug("跳过设置couponDetailUrl，客户端来源: {}", ctx.getClientSource());
            }

            snatchList.add(item);
        }
        return new ArrayList<>(maps.values());
    }

    @Cacheable(value = CACHE_USER_INFO, key = "#customId", unless = "#result == null")
    @Override
    public UserInfoBo getUserInfoByCustomId(String customId) {
        FookMacaupassUser user = fookMacaupassUserDao.selectOne(new LambdaQueryWrapper<FookMacaupassUser>()
                .eq(FookMacaupassUser::getCustomid, customId)
                .last("limit 1")
        );
        if (user == null) {
            return null;
        }
        FookPlatformUserinfo userinfo = fookPlatformUserinfoDao.selectByPrimaryKey(user.getUserId());
        return new UserInfoBo()
                .setCustomId(customId)
                .setUserId(user.getUserId())
                .setPhone(user.getPhone())
                .setOpenid(user.getOpenid())
                .setPoint(user.getPoint())
                .setName(userinfo != null ? userinfo.getNickName() : "");
    }

    @Override
    public CollectionListResponse getCollectionList(CollectionListRequest request) {
        CollectionListResponse result = new CollectionListResponse();
        int userId = contextHolder.getAuthUserInfo().getUserId();
        Locale locale = contextHolder.getLocale();
        String language = locale.getLanguage();
        request.convertWsgToGsj();
        int type = 2;
        int page = request.getPage();
        int offset = PageUtil.getOffset(page, McoinMall.DEFAULT_PAGE_SIZE);
        int limit = McoinMall.DEFAULT_PAGE_SIZE;
        int productType = -1;
        int listType = request.getType();
        if (listType == 3) {
            productType = 12;
        }
        List<SearchProductResponse.SnatchListItem> items = Lists.newArrayList();
        List<FookPlatformUsercollection> collections = this.collectionDao.selectList(type, productType, userId, offset, limit);
        int dataCount = this.collectionDao.selectListCnt(type, productType, userId);
        List<Integer> productIds = collections.stream().map(FookPlatformUsercollection::getCollectionid).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(productIds)) {
            List<FookBusinessProduct> bps = this.fookBusinessProductDao.getBusinessProductByIds(productIds);
            Map<Integer, FookBusinessProduct> map = bps.stream().collect(Collectors.toMap(FookBusinessProduct::getId, x -> x));
            List<FookBusinessProduct> sortedBps = productIds.stream()
                    .map(map::get)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            List<UsercollectionCnt> countByIds = this.fookPlatformUsercollectionDao.getCountByIds(productIds);
            List<Integer> businessIds = sortedBps.stream().map(FookBusinessProduct::getBusinessid).collect(Collectors.toList());
            List<FookBusiness> businesses = this.fookBusinessDao.getByIds(businessIds);
            Map<Integer, ActiveZoneStoreBo> storeBoMap = this.fookActiveZoneDao.getActiveZoneStores(productIds);
            List<FookBusinessProductTranslations> businessProductTranslations = this.fookBusinessProductTranslationsDao.getTranslationsByIds(productIds, language);
            items = BusinessProductMapping.INSTANCE.getProductList(sortedBps, collections, countByIds, businesses, request.getLat(), request.getLot(), language, messageSource, storeBoMap, businessProductTranslations);
        }
        result.setSnatchList(BusinessProductMapping.INSTANCE.toProductList(items));
        Page rPage = new Page();
        rPage.setPerPage(limit);
        rPage.setTotal(dataCount);
        rPage.setLastPage(PageUtil.getPageNum(dataCount, limit));
        rPage.setCurrentPage(page);
        result.setPage(rPage);
        return result;
    }


    @Override
    public ApiCollectCntResponse getCollections(ApiCollectionCntRequest request, Integer userId) {
        ApiCollectCntResponse result = new ApiCollectCntResponse();
        List<FookBusinessProduct> products = fookBusinessProductDao.selectList(new LambdaQueryWrapper<FookBusinessProduct>()
                .eq(FookBusinessProduct::getGoodsId, request.getGoodsId()));
        if (CollectionUtils.isNotEmpty(products)) {
            List<Integer> productIds = products.stream().map(FookBusinessProduct::getId).collect(Collectors.toList());
            List<UsercollectionCnt> countByIds = collectionDao.getCountByIds(productIds);
            List<FookPlatformUsercollection> userCollections = Lists.newArrayList();
            if (null != userId) {
                userCollections = collectionDao.selectCollections(userId, productIds);
            }
            for (UsercollectionCnt countById : countByIds) {
                if (CollectionUtils.isNotEmpty(userCollections)) {
                    FookPlatformUsercollection usercollection = userCollections.stream().filter(x -> x.getCollectionid().equals(countById.getCollectionid())).findFirst().orElse(null);
                    if (null != usercollection) {
                        result.setCollect(usercollection.getEnable());
                    }
                }
                result.setCollectCount(String.valueOf(countById.getCnt()));
                Integer goodsId = products.stream().filter(p -> p.getId().equals(countById.getCollectionid())).map(FookBusinessProduct::getGoodsId).findFirst().orElse(null);
                result.setGoodsId(goodsId);
            }
        }
        return result;
    }

    /**
     * 通用的优惠券详情链接设置方法
     *
     * @param orderCode 订单核销码（MyCouponOther或Coupons，都继承自FookPlatformOrdercode）
     * @param orderMap 订单信息映射
     * @param user 用户信息
     * @param urlSetter 设置URL的函数式接口，用于处理不同响应类型的URL设置逻辑
     */
    private void setCouponDetailUrlGeneric(FookPlatformOrdercode orderCode,
                                           Map<Integer, FookPlatformOrder> orderMap,
                                           FookMacaupassUser user,
                                           java.util.function.Consumer<String> urlSetter) {
        try {
            // 从预查询的Map中获取订单信息
            FookPlatformOrder order = orderMap.get(orderCode.getOrderid());
            if (order == null) {
                log.warn("未查询到订单信息，orderId: {}", orderCode.getOrderid());
                return;
            }

            // 检查用户信息
            if (user == null) {
                log.warn("用户信息为空，无法构建couponDetailUrl，orderCodeId: {}", orderCode.getId());
                return;
            }

            // 使用工具类构建couponDetailUrl
            String detailUrl = CouponDetailUrlUtil.buildCouponDetailUrl(
                    orderCode.getId(), user.getCustomid(), order.getOrderNo());

            // 使用传入的函数设置URL
            if (detailUrl != null) {
                urlSetter.accept(detailUrl);
                log.debug("设置couponDetailUrl成功，orderCodeId: {}, url: {}", orderCode.getId(), detailUrl);
            }
        } catch (Exception e) {
            log.error("设置couponDetailUrl失败，orderCodeId: {}", orderCode.getId(), e);
        }
    }

    /**
     * 设置优惠券详情链接（用于MyCouponResponse）
     */
    private void setCouponDetailUrl(MyCouponResponse.SnatchListItem item, MyCouponOther orderCode,
                                    Map<Integer, FookPlatformOrder> orderMap, FookMacaupassUser user) {
        setCouponDetailUrlGeneric(orderCode, orderMap, user, detailUrl -> {
            // MyCouponResponse的URL设置到orderinfo中
            if (item.getOrderinfo() != null) {
                item.getOrderinfo().setCouponDetailUrl(detailUrl);
            }
        });
    }

    /**
     * 设置优惠券详情链接（用于CouponsResponse）
     */
    private void setCouponDetailUrlForCoupons(CouponsResponse.SnatchListItem item, Coupons orderCode,
                                              Map<Integer, FookPlatformOrder> orderMap, FookMacaupassUser user) {
        setCouponDetailUrlGeneric(orderCode, orderMap, user, detailUrl -> {
            // CouponsResponse的URL直接设置到item中
            item.setCouponDetailUrl(detailUrl);
        });
    }
}
