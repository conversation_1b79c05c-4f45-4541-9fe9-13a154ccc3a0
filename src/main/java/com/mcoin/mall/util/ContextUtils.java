package com.mcoin.mall.util;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import java.lang.annotation.Annotation;


/**
 * Spring上下文
 * <AUTHOR>
 */
@Component
public class ContextUtils {
	
	private static ApplicationContext application;

	@Autowired
	public ContextUtils(ApplicationContext applicationContext) {
		application = applicationContext;
	}

	public static Object getBean(String name){
		return application.getBean(name);
	}

	public static <T> T getBean(String name, Class<T> clazz){
		return application.getBean(name, clazz);
	}

	public static <T> T getBean(Class<T> clazz){
		return application.getBean(clazz);
	}

	public static String[] getBeans(){
		return application.getBeanDefinitionNames();
	}
	
	public static String[] getBeansForAnnotation(Class<? extends Annotation> clazz){
		return application.getBeanNamesForAnnotation(clazz);
	}

	
}
