package com.mcoin.mall.util;

import org.apache.commons.lang3.StringUtils;

import static java.lang.Double.parseDouble;
import static java.lang.Math.asin;
import static java.lang.Math.cos;

public class DistanceUtil {
    /**   equatorial radius **/
    private static final double TO_METERS = 6_371_008.7714D;

    /**
     * 計算公里
     * lat緯度
     * lng經度
     */
    public static double getDistance(String lng1, String lat1, String lng2, String lat2){
        if(StringUtils.isBlank(lng1)||StringUtils.isBlank(lat1)||StringUtils.isBlank(lng2)||StringUtils.isBlank(lat2)){
            return 0;
        }
        return getDistance(parseDouble(lng1), parseDouble(lat1), parseDouble(lng2), parseDouble(lat2));
    }

    /**
     * 计算公里
     * @param gcjLot1 gcjLot1  经度
     * @param gcjLat1 gcjLat1  纬度
     * @param gcjLot2  gcjLot2  经度
     * @param gcjLat2  gcjLat2  纬度
     * @return double 计算后的double值
     */
    public static double getDistance(double gcjLot1, double gcjLat1, double gcjLot2, double gcjLat2) {
        // 1.  计算坐标点距离，GCJ坐标,单位km
        double s = haversinMeters(gcjLat1, gcjLot1, gcjLat2, gcjLot2)/1000.0;
        if ( s < 1){
            // 2.  返回保留三位小数的距离值
            return Math.round(s * 1000.0) / 1000.0;
        } else {
            // 3.  返回保留三位小数的距离值
            return Math.round(s * 10.0) / 10.0;
        }
    }

    public static String getDistanceStr(Double distance) {
        if(distance == null || distance == 0){
            return "";
        }
        String distanceStr = "";
        if ( distance < 1){
            distanceStr = String.format("%.0f", distance * 1000) + "m";
        } else {
            distanceStr = String.format("%.1f", distance) + "km";
        }
        return distanceStr;
    }
    /**
     * Returns the Haversine distance in meters between two points specified in decimal degrees
     * (latitude/longitude). This works correctly even if the dateline is between the two points.
     *
     * <p>Error is at most 4E-1 (40cm) from the actual haversine distance, but is typically much
     * smaller for reasonable distances: around 1E-5 (0.01mm) for distances less than 1000km.
     *
     * @param lat1 Latitude of the first point.
     * @param lon1 Longitude of the first point.
     * @param lat2 Latitude of the second point.
     * @param lon2 Longitude of the second point.
     * @return distance in meters.
     */
    public static double haversinMeters(double lat1, double lon1, double lat2, double lon2) {
        return haversinMeters(haversinSortKey(lat1, lon1, lat2, lon2));
    }

    /**
     * Returns the Haversine distance in meters between two points given the previous result from
     * {@link #haversinSortKey(double, double, double, double)}
     *
     * @return distance in meters.
     */
    public static double haversinMeters(double sortKey) {
        return TO_METERS * 2 * asin(Math.min(1, Math.sqrt(sortKey * 0.5)));
    }

    /**
     * Returns a sort key for distance. This is less expensive to compute than {@link
     * #haversinMeters(double, double, double, double)}, but it always compares the same. This can be
     * converted into an actual distance with {@link #haversinMeters(double)}, which effectively does
     * the second half of the computation.
     */
    public static double haversinSortKey(double lat1, double lon1, double lat2, double lon2) {
        double x1 = Math.toRadians(lat1);
        double x2 = Math.toRadians(lat2);
        double h1 = 1 - cos(x1 - x2);
        double h2 = 1 - cos(Math.toRadians(lon1 - lon2));
        double h = h1 + cos(x1) * cos(x2) * h2;
        // clobber crazy precision so subsequent rounding does not create ties.
        return Double.longBitsToDouble(Double.doubleToRawLongBits(h) & 0xFFFFFFFFFFFFFFF8L);
    }
}
