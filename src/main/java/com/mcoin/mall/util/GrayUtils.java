package com.mcoin.mall.util;

public class GrayUtils {

    public static final String CLOSED = "0";
    public static final String GRAYING = "1";
    public static final String ALL = "2";

    public static String getGrayValue(String gV1,String value,String grayValue) {
        String gray = ConfigUtils.getProperty("mcoin.gray.switch", CLOSED);
        String appUrl = value;
        if ((GRAYING.equals(gray) && "true".equals(gV1))
                || ALL.equals(gray)) {
            appUrl = grayValue;
        }
        return appUrl;
    }
}
