package com.mcoin.mall.util;

import jodd.util.StringUtil;
import org.apache.commons.beanutils.locale.converters.DateLocaleConverter;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.Calendar;
import java.util.Date;
import java.util.Locale;

public class JodaTimeUtil {

    public static final String PATTERN = "yyyyMMddHHmmss";
    public static final String DEFAULT_PATTERN = "yyyy-MM-dd HH:mm:ss";
    public static final String YYYYMMDD = "yyyyMMdd";
    public static final String YYYYMMDDHHM = "yyyyMMddHHm";
    public final static String YYYY_MM_DD = "yyyy-MM-dd";
    public static final String HH_MM_SS = "HHmmss";
    public static final String HH_MM_SS2 = "HH:mm:ss";
    public static final String PATTERNFULL = "yyyyMMddHHmmssSSS";
    public final static String MCOIN_CN = "yyyy/MM/dd";
    public final static String MCOIN_EN = "jS F Y";
    public static final int FIRST_DAY = Calendar.MONDAY;


    public static Date parse(String date) {
        return parse(date, DEFAULT_PATTERN);
    }

    public static Date parse(String date, String pattern) {
        DateTimeFormatter format = DateTimeFormat.forPattern(pattern);
        DateTime dateTime = DateTime.parse(date, format);
        return dateTime.toDate();
    }

    /**
     * 格式“yyyyMMdd”,如20150101
     *
     * @return
     */
    public static String getDate() {
        return DateTime.now().toString(YYYYMMDD);
    }

    /**
     * 格式“yyyyMMdd”,如20150101
     *
     * @return
     */
    public static String getDate2() {
        return DateTime.now().toString(YYYY_MM_DD);
    }

    /**
     * 格式“HHmmss”,如120101
     *
     * @return
     */
    public static String getTime() {
        return DateTime.now().toString(HH_MM_SS);
    }

    /**
     * 格式“HHmmss”,如120101
     *
     * @return
     */
    public static String getTime2() {
        return DateTime.now().toString(HH_MM_SS2);
    }

    /**
     * 格式“yyyy-MM-dd HH:mm:ss”,如2015-01-01 12:30:24
     *
     * @return
     */
    public static String getStandardTime() {
        return DateTime.now().toString(DEFAULT_PATTERN);
    }

    /**
     * 格式“yyyyMMddHHmmss”,如20150101123024
     *
     * @return
     */
    public static String getFullTime() {
        return DateTime.now().toString(PATTERN);
    }

    /**
     * 格式“yyyyMMddHHmmssSSS”,如20150101123024001
     *
     * @return
     */
    public static String getFullTimeAll() {
        return DateTime.now().toString(PATTERNFULL);
    }

    /**
     * 格式化时间格式
     *
     * @param date 日期类型字符串
     * @return 格式化后日期类型字符串
     */
    public static String format(String date) {
        DateTimeFormatter format = DateTimeFormat.forPattern(PATTERN);
        DateTime dateTime = DateTime.parse(date, format);
        return dateTime.toString(DEFAULT_PATTERN);
    }

    public static Date parseForPattern(String date,String pattern) {
        if(StringUtil.isNotBlank(pattern)){
            DateTimeFormatter format = DateTimeFormat.forPattern(pattern);
            DateTime dateTime = DateTime.parse(date, format);
            return dateTime.toDate();
        }
        return new Date();
    }

    /**
     * 该方法如果传入的日期为空，则返回当前时间
     * @param date
     * @return
     */
    public static String format(Date date) {
        DateTime dateTime = new DateTime(date);
        return dateTime.toString(DEFAULT_PATTERN);
    }

    public static boolean ifGrownUp(String birthday, String pattern) throws ParseException {
        boolean result = false;
        SimpleDateFormat format = new SimpleDateFormat(pattern);
        Date date = format.parse(birthday);
        Calendar birthdayCalendar = Calendar.getInstance();
        birthdayCalendar.setTime(date);//生日

        Calendar cal = Calendar.getInstance();// 取当前日期。
        cal.add(Calendar.YEAR, -18);// 取当前日期的前一天.

        if (birthdayCalendar.before(cal)){
            result = true;
        }
        return result;

    }


    public static String getDelayTime(String deLay) {
        return getDelayLimitTime(deLay, 0);
    }

    /**
     * @param deLay m-分钟，h-小时，d-天，1c-当天
     * @param limit 限制最長延遲時間 (天)
     * @return(格式化錯誤，自動轉為90m分鐘)
     */
    public static String getDelayLimitTime(String deLay, Integer limit) {
        Calendar layTime = Calendar.getInstance();
        int deL = 0;
        int m_l = 999999999;
        int h_l = 999999999;
        int d_l = 999999999;
        if (limit != 0){
            //限制分鐘
            m_l = limit * 24 * 60;
            h_l = limit * 24;
            d_l = limit;
        }

        try {
            if (deLay.toLowerCase().contains("m")){
                //包含m的，為分鐘
                deL = Integer.parseInt(deLay.split("m")[0]);

                //小時大於15天，自動取第15天
                if (deL > m_l){
                    deL = m_l;
                }

                layTime.add(Calendar.MINUTE, deL);
            } else if (deLay.toLowerCase().contains("h")){
                //包含h的，為小時
                deL = Integer.parseInt(deLay.split("h")[0]);
                //分鐘大於15天，自動取第15天
                if (deL > h_l){
                    deL = h_l;
                }
                layTime.add(Calendar.HOUR, deL);
            } else if (deLay.toLowerCase().contains("d")){
                //包含h的，為小時
                deL = Integer.parseInt(deLay.split("d")[0]);
                //分鐘大於15天，自動取第15天
                if (deL > d_l){
                    deL = d_l;
                }
                layTime.add(Calendar.DAY_OF_MONTH, deL);
            } else if (deLay.toLowerCase().contains("c")){
                //包含c的，為當天23:59:59
                return getDate() + "235959";
            } else {
                //數據有誤，轉為默認90m
                layTime.add(Calendar.MINUTE, 90);
            }
            return (new SimpleDateFormat(PATTERN)).format(layTime.getTime());
        } catch (Exception e) {
            //格式化出錯
            return getDelayLimitTime("90m", 15);
        }


    }


    /**
     * 格式化日期 for String
     *
     * @param @param  args
     * @param @throws ParseException
     * @return void
     * @throws
     * @Title: main
     */
    public static String formatByString(String dateStr, String pattern, String toPattern) {
        String str = "";
        if (!StringUtils.isEmpty(dateStr)){
            Date date;
            try {
                date = new SimpleDateFormat(pattern).parse(dateStr);
            } catch (ParseException e) {
                return str;
            }
            str = new SimpleDateFormat(toPattern).format(date);
        }
        return (str);
    }

    /**
     * 校驗日期
     *
     * @param date
     * @param patterns 可校驗多種模式，yyyyMMdd,yyyyMM,yyyyMMdd HH:mm:ss
     * @return
     * @throws Exception
     */
    public static boolean validDate(String date, String... patterns) {
        boolean valid = false;
        DateLocaleConverter conv = new DateLocaleConverter();
        if (StringUtils.isEmpty(date)){
            valid = false;
        } else {
            for (String pattern : patterns) {
                try {
                    conv.convert(date, pattern);
                    valid = true;
                    break;
                } catch (Exception e) {
                    valid = false;
                }
            }
        }
        return valid;
    }

    /**
     * 查詢時間是否超時
     *
     * @param sourceDay
     * @param desDay
     * @param pattern
     * @param calType
     * @param timeout
     * @return
     * @throws ParseException
     */
    public static boolean isTimeout(String sourceDay, String desDay, String pattern, int calType, int timeout) throws ParseException {
        boolean result = false;
        SimpleDateFormat format = new SimpleDateFormat(pattern);
        Calendar sourceCalendar = Calendar.getInstance();
        sourceCalendar.setTime(format.parse(sourceDay));//源日期
        sourceCalendar.add(calType, timeout);

        Calendar desCalendar = Calendar.getInstance();
        desCalendar.setTime(format.parse(desDay));//目標日期


        if (sourceCalendar.before(desCalendar)){
            result = true;
        }
        return result;

    }


    /**
     * 获取当前年
     *
     * @return
     */
    public static String getDateYear() {
        return format(new Date(), "yyyy");
    }

    /**
     * 获取当前月
     *
     * @return
     */
    public static String getDateMonth() {
        return format(new Date(), "MM");
    }

    /**
     * 获取当前日
     *
     * @return
     */
    public static String getDateDay() {
        return format(new Date(), "dd");
    }


    /**
     * 获取当前日期,不含时间
     *
     * @return
     */
    public static String getNow() {
        return format(new Date(), YYYYMMDD);
    }

    /**
     * 获取当前日期时间：如2015-07-09 00:00:00
     *
     * @return
     */
    public static String getDateTime() {
        return format(new Date(), DEFAULT_PATTERN);
    }

    /**
     * 格式化日期时间
     *
     * @param date
     * @param pattern
     * @return
     */
    public static String format(Date date, String pattern) {
        String returnValue = "";
        if (date != null){
            SimpleDateFormat df = new SimpleDateFormat(pattern);
            returnValue = df.format(date);
        }
        return (returnValue);
    }


    /**
     * 取得某天相加(减)後的那一天
     *
     * @param nowDate
     * @param num     (可正可负)
     * @return
     * @throws ParseException
     * <AUTHOR>
    public static String getAnotherDate(String nowDate, int num)
            throws ParseException {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        Calendar c = Calendar.getInstance();
        c.setTime(sdf.parse(nowDate));
        c.add(Calendar.DATE, num);
        nowDate = sdf.format(c.getTime());
        return nowDate;
    }

    /**
     * 取得某天相加(减)後的那一天
     *
     * @param nowDate
     * @param num     (可正可负)
     * @return
     * @throws ParseException
     * <AUTHOR>
    public static Date getAnotherDate(Date nowDate, int num) {
        Calendar c = Calendar.getInstance();
        c.setTime(nowDate);
        c.add(Calendar.DATE, num);
        return c.getTime();
    }

    /**
     * 获取下周的日期
     *
     * @param nowDate
     * @param num     (可正可负)
     * @return
     * @throws ParseException
     * <AUTHOR>
     */
    public static String getAnotherWeek(String nowDate, int num)
            throws ParseException {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        Calendar c = Calendar.getInstance();
        c.setTime(sdf.parse(nowDate));
        c.add(Calendar.WEEK_OF_MONTH, num);
        nowDate = sdf.format(c.getTime());
        return nowDate;
    }

    /**
     * 获取下月的今天
     *
     * @param nowDate
     * @param num     (可正可负)
     * @return
     * @throws ParseException
     * <AUTHOR>
     */
    public static String getAnotherMonth(String nowDate, int num)
            throws ParseException {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        Calendar c = Calendar.getInstance();
        c.setTime(sdf.parse(nowDate));
        c.add(Calendar.MONTH, num);
        nowDate = sdf.format(c.getTime());
        return nowDate;
    }

    /**
     * 获取当前日期的YEAR年、MONTH月、DATE日、DAY_OF_WEEK星期
     *
     * @param str
     * @return
     * <AUTHOR>
     */
    public static int getsCurrentDate(String str) {
        Calendar cal = Calendar.getInstance();
        int getDate;
        if ("YEAR".equals(str)){
            // 年
            getDate = cal.get(Calendar.YEAR);
        } else if ("MONTH".equals(str)){
            // 月
            getDate = cal.get(Calendar.MONTH) + 1;
        } else if ("DATE".equals(str)){
            // 日
            getDate = cal.get(Calendar.DATE);
        } else if ("DAY_OF_WEEK".equals(str)){
            // 星期
            getDate = cal.get(Calendar.DAY_OF_WEEK) - 1;
        } else {
            getDate = -1;
        }
        return getDate;
    }

    /**
     * 获取本周周几日期
     *
     * @param weekNub
     * @return
     * <AUTHOR>
     */
    public static String getWeek(int weekNub) {
        Calendar calendar = Calendar.getInstance();
        while (calendar.get(Calendar.DAY_OF_WEEK) != FIRST_DAY) {
            calendar.add(Calendar.DATE, -1);
        }
        calendar.add(Calendar.DATE, weekNub - 1);
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
        return dateFormat.format(calendar.getTime());
    }


    /**
     * 返回時間戳
     *
     * @param timeStr
     * @param pattern 日期格式
     * @return
     * @throws ParseException
     */
    public static Long getTimestamp(String timeStr, String pattern)
            throws ParseException {
        Long time = new SimpleDateFormat(pattern).parse(timeStr).getTime();
        return time;
    }

    /**
     * @param nowDate      當前時間
     * @param pattern
     * @param calendarType
     * @param num
     * @return
     * @throws ParseException
     */
    public static String getConverDate(String nowDate, String pattern, int calendarType, int num)
            throws ParseException {
        SimpleDateFormat sdf = new SimpleDateFormat(pattern);
        Calendar c = Calendar.getInstance();
        c.setTime(sdf.parse(nowDate));
        c.add(calendarType, num);
        String newDate = sdf.format(c.getTime());
        return newDate;
    }

    /**
     * 秒转化为天小时分秒字符串
     *
     * @param seconds
     * @return String
     */
    public static String formatSeconds(long seconds) {
        String timeStr = seconds + "秒";
        if (seconds > 60){
            long second = seconds % 60;
            long min = seconds / 60;
            timeStr = min + "分" + second + "秒";
            if (min > 60){
                min = (seconds / 60) % 60;
                long hour = (seconds / 60) / 60;
                timeStr = hour + "小时" + min + "分" + second + "秒";
                if (hour > 24){
                    hour = ((seconds / 60) / 60) % 24;
                    long day = (((seconds / 60) / 60) / 24);
                    timeStr = day + "天" + hour + "小时" + min + "分" + second + "秒";
                }
            }
        }
        return timeStr;
    }

    /**
     * 获取两个日期之间的
     *
     * @param before
     * @param after
     * @return
     */
    public static double getDistanceOfTwoDate(Date before, Date after, String mode) {
        long beforeTime = before.getTime();
        long afterTime = after.getTime();
        if ("s".equals(mode)){
            return Double.parseDouble(String.valueOf((afterTime - beforeTime) / 1000));
        } else if ("m".equals(mode)){
            return Double.parseDouble(String.valueOf((afterTime - beforeTime) / (1000 * 60)));
        } else if ("h".equals(mode)){
            return Double.parseDouble(String.valueOf((afterTime - beforeTime) / (1000 * 60 * 60)));
        } else if ("d".equals(mode)){
            return Double.parseDouble(String.valueOf((afterTime - beforeTime) / (1000 * 60 * 60 * 24)));
        } else {
            return Double.parseDouble(String.valueOf(afterTime - beforeTime));
        }
    }

    /**
     * @param nowDate
     * @param num
     * @return
     */
    public static Date getAnotherMinute(Date nowDate, int num) {
        Calendar c = Calendar.getInstance();
        c.setTime(nowDate);
        c.add(Calendar.MINUTE, num);
        return c.getTime();
    }

    public static int getRemainSecondsOneDay(Date currentDate) {
        Calendar midnight = Calendar.getInstance();
        midnight.setTime(currentDate);
        midnight.add(Calendar.DAY_OF_MONTH, 1);
        midnight.set(Calendar.HOUR_OF_DAY, 0);
        midnight.set(Calendar.MINUTE, 0);
        midnight.set(Calendar.SECOND, 0);
        midnight.set(Calendar.MILLISECOND, 0);
        int seconds = (int) ((midnight.getTime().getTime() - currentDate.getTime()) / 1000);
        return seconds;
    }

    public static String format4Mcoin(Date date, String lang) {
        try {
            if (!McoinMall.LANG_EN.equals(lang)){
                return format(date, MCOIN_CN);
            }
            return getCurrentDateInSpecificFormat(date);
        } catch (Exception e) {
            return format(date, MCOIN_CN);
        }
    }

    public static String getCurrentDateInSpecificFormat(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        String dayNumberSuffix = getDayNumberSuffix(calendar.get(Calendar.DAY_OF_MONTH));
        DateFormat dateFormat = new SimpleDateFormat(" d'" + dayNumberSuffix + "' MMMM yyyy", Locale.ENGLISH);
        return dateFormat.format(calendar.getTime());
    }

    public static String getDayNumberSuffix(int day) {
        if (day >= 11 && day <= 13){
            return "th";
        }
        switch (day % 10) {
            case 1:
                return "st";
            case 2:
                return "nd";
            case 3:
                return "rd";
            default:
                return "th";
        }
    }
    public static String plusHours(int hour){
        return format(DateTime.now().plusHours(hour).toDate(), DEFAULT_PATTERN);
    }

    public static String plusHours(Date date, int hour){
        return format(plusHoursToDate(date, hour), DEFAULT_PATTERN);
    }
    public static String plusHours(Date date, int hour, String pattern){
        return format(plusHoursToDate(date, hour), pattern);
    }
    public static Date plusHoursToDate(Date date, int hour){
        return new DateTime(date).plusHours(hour).toDate();
    }

    public static String plusMinute(Date date, int minute){
        return format(plusMinuteToDate(date, minute), DEFAULT_PATTERN);
    }

    public static String plusSecond(Date date, int second){
        return format(plusSecondToDate(date, second), DEFAULT_PATTERN);
    }

    public static Date plusMinuteToDate(Date date, int minute){
        return new DateTime(date).plusMinutes(minute).toDate();
    }

    public static Date plusSecondToDate(Date date, int second){
        return new DateTime(date).plusSeconds(second).toDate();
    }

    public static String plusDay(Date date, int day){
        return format(new DateTime(date).plusDays(day).toDate(), YYYY_MM_DD);
    }
    public static String plusDayYYYYMMDD(Date date, int day){
        return format(new DateTime(date).plusDays(day).toDate(), YYYYMMDD);
    }

    public static Date plusDayToDate(Date date, int day){
        return new DateTime(date).plusDays(day).toDate();
    }

    public static String plusDay(String date, int day){
        return plusDay(date, day, YYYY_MM_DD);
    }

    public static String plusDay(String date, int day, String format){
        return format(DateTime.parse(date, DateTimeFormat.forPattern(format)).plusDays(day).toDate(), format);
    }

    public static String plusDay(Date date, int day, String format){
        return format(plusDayToDate(date, day), format);
    }

    public static Date plusDayToDate(String date, int day, String format){
        return DateTime.parse(date, DateTimeFormat.forPattern(format)).plusDays(day).toDate();
    }

    public static int compareWithToday(Date workDate){
        Date today = getDateWithoutTime();
        return workDate.compareTo(today);
    }

    public static Date plusMonthToDate(Date date, int month){
        return new DateTime(date).plusMonths(month).toDate();
    }

    public static Date getDateWithoutTime(Date date){
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MILLISECOND, 0);
        return cal.getTime();
    }

    public static Date getDateWithoutTime(){
        return getDateWithoutTime(new Date());
    }


    /**
     * 对日期的【小时】进行加/减
     *
     * @param String  日期
     * @param hours 小时数，负数为减
     * @return 加/减几小时后的日期
     */
    public static String addDateHours(int hours,String pattern) {
        DateTime dateTime = new DateTime(new Date());
        SimpleDateFormat df = new SimpleDateFormat(pattern);
        return df.format(dateTime.plusHours(hours).toDate());
    }
    /**
     * 对当前日期时间的【秒】进行加/减
     * @param String    日期
     * @param seconds 秒数，负数为减
     * @return 加/减几秒后的日期
     */
    public static Date addDateSeconds(int seconds) {
        DateTime dateTime = new DateTime(new Date());
        return dateTime.plusSeconds(seconds).toDate();
    }
    
    public static boolean isAfter(String beginTime,String endTime,String pattern){
        DateTimeFormatter fmt = DateTimeFormat.forPattern(pattern);
        Date beginDate =  fmt.parseLocalDateTime(beginTime).toDate();
        Date endDate =  fmt.parseLocalDateTime(endTime).toDate();
        return beginDate.after(endDate);
    }

    public static Date getMonthFirstDay(){
        SimpleDateFormat format = new SimpleDateFormat(YYYY_MM_DD);
        // 本月起始
        Calendar thisMonthFirstDateCal = Calendar.getInstance();
        thisMonthFirstDateCal.set(Calendar.DAY_OF_MONTH, 1);
        String thisMonthFirstTime = format.format(thisMonthFirstDateCal.getTime()) + " 00:00:00";
        return parse(thisMonthFirstTime);
    }

    public static Date getNextMonthFirstDay(){
        SimpleDateFormat format = new SimpleDateFormat(YYYY_MM_DD);
        Calendar lastMonthFirstDateCal = Calendar.getInstance();
        lastMonthFirstDateCal.add(Calendar.MONTH,+1);
        lastMonthFirstDateCal.set(Calendar.DAY_OF_MONTH, 1);
        String nextMonthFirstTime = format.format(lastMonthFirstDateCal.getTime()) + " 00:00:00";
        return parse(nextMonthFirstTime);
    }

    public static LocalDate toLocalDate(Date date){
        return date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
    }

    /**
     * 将LocalDate 转换成 Date
     * @param localDate
     * @return
     */
    public static Date toDate(LocalDate localDate){
        ZoneId zoneId = ZoneId.systemDefault();
        ZonedDateTime zdt = localDate.atStartOfDay(zoneId);
        return Date.from(zdt.toInstant());
    }

    /**
     * 返回时间秒，除秒的个位数
     * @param dateTime
     * @return
     */
    public static int getSecondsWithoutSecondsLastDigit(Date dateTime) {
        // 获取完整的秒数
        int fullSeconds = Integer.parseInt(format(dateTime, "ss"));
        // 去除秒的个位数
        return fullSeconds / 10 * 10;
    }


}
