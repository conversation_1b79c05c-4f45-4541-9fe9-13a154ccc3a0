package com.mcoin.mall.util;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;

public class MoneyUtil {

	public static BigDecimal divide(BigDecimal amount1, BigDecimal amount2) {
		int scale = 2;// 保留2位小数
		return amount1.divide(amount2, scale, RoundingMode.HALF_UP);
	}

	public static String format(BigDecimal amount) {
		DecimalFormat df = new DecimalFormat("##0.00");
		return df.format(amount);
	}

}
