package com.mcoin.mall.util;

import com.mcoin.mall.constant.IsAlipayPlus;
import com.mcoin.mall.vo.SettlementReportFileVo;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

import static com.mcoin.mall.util.JodaTimeUtil.*;

public class OssUtil {

    public static String initOssImage(String image) {
        if(StringUtils.isNotBlank(image)) {
            if(image.startsWith("http")){
                return image;
            } else {
                String prefixUrl = ConfigUtils.getProperty("macaupass.oss_prefixUrl");
                return prefixUrl + image;
            }
        }
        return "";
    }

    public static String getProductImg(String img){
        if (StringUtils.isBlank(img) || img.startsWith("fook-business-product")) {
            return img;
        }
        return initOssImage(img);
    }

    public static List<String> getProductImgs(String imgs){
        if (StringUtils.isBlank(imgs)) {
            return Collections.emptyList();
        }
        List<String> imgList = new ArrayList<>();
        for (String img: imgs.split(",")) {
            imgList.add(initOssImage(img));
        }
        return imgList;
    }


    public static String getStoreImg(String img){
        if (StringUtils.isBlank(img) || img.startsWith("fook-stores")) {
            return img;
        }
        return initOssImage(img);
    }

    public static SettlementReportFileVo getReportOssPath(Date startTime, Integer isA, String code){
        String yyyyMM = format(startTime, "yyyyMM");
        String downloadDate = "FH";
        int day = toLocalDate(startTime).getDayOfMonth();
        if (day > 15) {
            downloadDate = "SH";
        }
        if (IsAlipayPlus.YES.ordinal() == isA){
            downloadDate = "AH";
        }
        String ossPrefix = ConfigUtils.getProperty("report.oss.settlement.prefix", "");
        String ossPath = ossPrefix + "settlement/" + yyyyMM + "/" + yyyyMM + "_" + downloadDate + "/" + code;
        String filename = code + "_" + yyyyMM + "_" + downloadDate + day;
        SettlementReportFileVo fileVo = new SettlementReportFileVo();
        fileVo.setOssPath(ossPath);
        fileVo.setFilename(filename);
        return fileVo;
    }

    public static SettlementReportFileVo getOptionalReportOssPath(Date startTime, String code){
        String yyyyMM = format(startTime, "yyyyMM");
        String downloadDate = "FH";
        int day = toLocalDate(startTime).getDayOfMonth();
        if (day > 15) {
            downloadDate = "SH";
        }
        String ossPrefix = ConfigUtils.getProperty("report.oss.settlement.prefix", "");
        String ossPath = ossPrefix + "settlementoptional/" + yyyyMM + "/" + yyyyMM + "_" + downloadDate + "/" + code;
        String filename = code + "_" + yyyyMM + "_" + downloadDate + day;
        SettlementReportFileVo fileVo = new SettlementReportFileVo();
        fileVo.setOssPath(ossPath);
        fileVo.setFilename(filename);
        return fileVo;
    }

    public static String getTradingDataOssPath(){
        String ossPrefix = ConfigUtils.getProperty("report.oss.settlement.prefix", "");
        return ossPrefix + "trade";
    }
}
