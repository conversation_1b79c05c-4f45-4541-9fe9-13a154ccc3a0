package com.mcoin.mall.util;

import com.alibaba.csp.sentinel.slots.block.BlockException;
import com.mcoin.mall.exception.BusinessException;
import com.mcoin.mall.model.Response;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.Callable;

@Slf4j
public class SentinelUtils {


    public static <T> T handleBlockException(Callable<T> callable, T blockedReturn) throws BusinessException {
        try {
            return callable.call();
        } catch (BlockException e) {
            log.warn("触发限流异常", e);
            return blockedReturn;
        } catch (Exception e) {
            return throwException(e);
        }
    }

    private static <T> T throwException(Exception e) throws BusinessException {
        if (e instanceof BusinessException){
            throw (BusinessException)e;
        }
        throw new BusinessException(Response.Code.BAD_REQUEST, e.getMessage());
    }
}
