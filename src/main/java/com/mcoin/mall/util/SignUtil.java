package com.mcoin.mall.util;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.codec.Base64;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.HexUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.asymmetric.Sign;
import cn.hutool.crypto.asymmetric.SignAlgorithm;
import cn.hutool.crypto.digest.DigestUtil;
import cn.hutool.crypto.digest.MD5;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.mcoin.mall.model.api.ApiBaseRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

@Slf4j
public class SignUtil {

    public static String couponSign(String secret, Object bean) {
        Map<String, Object> beanMap = BeanUtil.beanToMap(bean);
        Map<String, Object> lowerCaseMap = new HashMap<>();
        for (Map.Entry<String, Object> entry : beanMap.entrySet()) {
            String lowercaseKey = entry.getKey().toLowerCase();
            Object value = entry.getValue();
            lowerCaseMap.put(lowercaseKey, value);
        }
        String parameterString = MapUtil.sortJoin(lowerCaseMap, "&", "=", true);
        log.info("加签源：{}", parameterString);
        String input = secret + "|" + parameterString;
        return DigestUtil.md5Hex(input);
    }

    public static Boolean checkCouponSign(String secretKey, ApiBaseRequest request) {
        if (StringUtils.isAnyBlank(secretKey, request.getSignature())) {
            return false;
        }
        String tmpSig = request.getSignature();
        request.setSignature(null);
        String secret = ConfigUtils.getProperty(secretKey);
        String validateSign = SignUtil.couponSign(secret, request);
        request.setSignature(tmpSig);
        return tmpSig.equals(validateSign);
    }

    public static String mPaySign(String priKey, String data) {
        return CipherUtils.generateSignature(priKey, data).toUpperCase();
    }


    public static boolean mPayVerifySign(String pubKey, String data) {
        JSONObject json = JSON.parseObject(data);
        String signature = json.getString("sign");
        if (StringUtils.isBlank(signature)) {
            return false;
        }
//        String signType = json.getString("sign_type");
        json.remove("sign");
        json.remove("sign_type");
        String sortData = JSONUtils.toOrderJSONString(json);
        log.info("签名报文排序：{}", sortData);
        return CipherUtils.checkSignatureWithRSA1(sortData, signature, pubKey);
    }


    /**
     * 门店核销密码
     *
     * @param pass
     * @return
     */
    public static String md5Pass(String pass) {
        if (StringUtils.isBlank(pass)) {
            return "";
        }
        String md5hex = MD5.create().digestHex(pass);
        return Base64.encode(HexUtil.decodeHex(md5hex));
    }

    public static Boolean checkSign(String secretKey, ApiBaseRequest request) {
        if (StringUtils.isAnyBlank(secretKey, request.getSignature())) {
            return false;
        }
        String secret = ConfigUtils.getProperty(secretKey);
        String tmpSig = request.getSignature();
        request.setSignature(null);
        boolean extracted = checkSign(secret, request, tmpSig);
        request.setSignature(tmpSig);
        return extracted;
    }

    public static boolean checkSign(String secret, Object request, String tmpSig) {
        Map<String, Object> beanMap = BeanUtil.beanToMap(request);
        String parameterString = MapUtil.sortJoin(beanMap, "&", "=", true);
        log.info("加签源：{}", parameterString);
        String input = secret + "|" + parameterString;
        String validateSign = DigestUtil.md5Hex(input);
        return tmpSig.equals(validateSign);
    }


    public static String taskSign(String params) {
        try {
            String secret = ConfigUtils.getProperty("macaupass.task.upload.secret");
            String body = JSON.toJSONString(params, SerializerFeature.MapSortField);
            Map<String, Object> signMap = JSON.parseObject(params, new TypeReference<Map<String, Object>>() {
            });
            signMap.put("body", body);
            //剔除sign字段
            signMap.remove("sign");
            String signSource = MapUtil.sortJoin(signMap, "&", "=", true);
            log.info("簽名源:{}", signSource);
            Sign sign = SecureUtil.sign(SignAlgorithm.SHA256withRSA, secret, null);
            String encryptBase64 = Base64.encode(sign.sign(signSource.getBytes(StandardCharsets.UTF_8)));
            log.info("簽名:{}", encryptBase64);
            return encryptBase64;
        } catch (Exception e) {
            log.error("簽名失敗：", e);
        }
        return null;
    }

    public static String phpSign(String secret, Object bean) {
        StringTrimUtil.trimStringFields(bean);
        Map<String, Object> beanMap = BeanUtil.beanToMap(bean);
        String parameterString = HttpBuildQuery.httpBuildQuery(beanMap);
        log.info("加签源：{}", parameterString);
        String input = secret + "|" + parameterString;
        return DigestUtil.md5Hex(input);
    }
}
