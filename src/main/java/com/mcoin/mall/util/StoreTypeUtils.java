package com.mcoin.mall.util;

import com.mcoin.mall.bo.StoreTypeProductsBo;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * @see SnapUpUtils
 */
public class StoreTypeUtils {

    public static int getPoint(StoreTypeProductsBo item){
        int mpay_val = item.getPointRatio();
        int onlyPoint = Integer.parseInt(item.getOnlyPoint());
        int minPoint = item.getMinPoint() == null ? mpay_val/2: Integer.parseInt(item.getMinPoint());
        return onlyPoint == 1 ? (new BigDecimal(item.getPrice()).multiply(BigDecimal.valueOf(mpay_val))).intValue() :
                (onlyPoint == 2 ? 0 : (minPoint > 0 ? minPoint : mpay_val/2));
    }

    public static BigDecimal getPreferential(StoreTypeProductsBo item) {
        int mpay_val = item.getPointRatio();
        int onlyPoint = Integer.parseInt(item.getOnlyPoint());
        int minPoint = item.getMinPoint() == null ? mpay_val/2: Integer.parseInt(item.getMinPoint());
        BigDecimal price = new BigDecimal(item.getPrice());
        return onlyPoint == 1 ? BigDecimal.ZERO : (onlyPoint == 2 ? price :
                (price.subtract(BigDecimal.valueOf(minPoint > 0 ? minPoint :
                        (mpay_val / 2)).divide(BigDecimal.valueOf(mpay_val), 2, RoundingMode.HALF_UP))));
    }
}
