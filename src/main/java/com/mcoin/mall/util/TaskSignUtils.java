package com.mcoin.mall.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.alipay.api.AlipayApiException;
import com.alipay.api.AlipayConstants;
import com.alipay.api.internal.util.AlipaySignature;
import com.mcoin.mall.client.model.SignReqDto;
import com.mcoin.mall.client.model.SignResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.security.SecureRandom;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;


/**
 * 互动中心系统验签
 */
@Slf4j
public class TaskSignUtils {

    private static String requestAppId;
    private static String requestPubkey;
    private static String requestPrikey;
    private static String responsePubkey;
    private static String responsePrikey;
    private static SecureRandom random = new SecureRandom();

    static{
        requestAppId = ConfigUtils.getProperty("mcoin.api.request.appid");
        requestPubkey = ConfigUtils.getProperty("mcoin.api.request.pub.key");
        requestPrikey = ConfigUtils.getProperty("mcoin.api.request.pri.key");
        responsePubkey = ConfigUtils.getProperty("mcoin.api.response.pub.key");
        responsePrikey = ConfigUtils.getProperty("mcoin.api.response.pri.key");
    }
    /**
     * 验证请求签名
     * */
    public static boolean verifyRequest(SignReqDto request) {
        boolean verifySign = false;
        try {
            //使用商戶接入的公鑰驗簽
            String publicKey = requestPubkey;
            StringBuilder sb = new StringBuilder();
            sb.append("appId=").append(request.getAppId()).append("&");
            String bodyStr = JSON.toJSONString(request.getBody(), new SerializerFeature[]{SerializerFeature.MapSortField});
            log.info("body[{}]",bodyStr);
            sb.append("body=").append(bodyStr).append("&");
            sb.append("nonceStr=").append(request.getNonceStr()).append("&");
            sb.append("timestamp=").append(request.getTimestamp());

            String sign = request.getSign();
            log.info("加簽源:{}",sb.toString());
            verifySign = AlipaySignature.verify(sb.toString(), sign, publicKey, AlipayConstants.CHARSET_UTF8, AlipayConstants.SIGN_TYPE_RSA2);
            return verifySign;
        } catch (Exception e) {
            log.error("請求驗簽失敗"+e);
            return verifySign;
        }
    }
    /**
     * 请求前加签
     * */
    public static String signRequest(Map<String, Object> body) throws AlipayApiException, Exception {
        String result = JSON.toJSONString(rsaSign(body));
        return result;
    }

    /**
     * 请求前加签
     * */
    public static String signRequestToken(Map<String, Object> body,String token) throws AlipayApiException, Exception {
        String result = JSON.toJSONString(rsaSignToken(body,token));
        return result;
    }

    public static JSONObject signRequestToJson(Map<String, Object> body) throws AlipayApiException, Exception {
        JSONObject json = JSONObject.parseObject(signRequest(body));
        return json;
    }

    public static SignReqDto signRequestToSignReqDto(Map<String, Object> body) throws AlipayApiException, Exception {
        return rsaSign(body);
    }

    private static SignReqDto rsaSign(Map<String, Object> body) throws AlipayApiException, Exception {
        String nonceStr = getNonceStr();
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String timestamp = df.format(new Date());
        body = body == null ? new HashMap() : body;
        String bodyStr = JSON.toJSONString(body, new SerializerFeature[]{SerializerFeature.MapSortField});
        SignReqDto req = new SignReqDto(requestAppId, null, body, nonceStr, timestamp);
        StringBuilder sb = new StringBuilder();
        sb.append("appId=").append(requestAppId).append("&");
        sb.append("body=").append(bodyStr).append("&");
        sb.append("nonceStr=").append(nonceStr).append("&");
        sb.append("timestamp=").append(timestamp);

        log.info("签名源:" + sb.toString());
        String sign = AlipaySignature.rsaSign(sb.toString(), requestPrikey, AlipayConstants.CHARSET_UTF8, AlipayConstants.SIGN_TYPE_RSA2);
        log.info("签名值:" + sign);
        req.setSign(sign);
        return req;
    }

    private static SignReqDto rsaSignToken(Map<String, Object> body,String token) throws AlipayApiException, Exception {
        String nonceStr = getNonceStr();
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String timestamp = df.format(new Date());
        body = body == null ? new HashMap() : body;
        String bodyStr = JSON.toJSONString(body, new SerializerFeature[]{SerializerFeature.MapSortField});
        SignReqDto req = new SignReqDto(requestAppId, token, body, nonceStr, timestamp);
        StringBuilder sb = new StringBuilder();
        sb.append("appId=").append(requestAppId).append("&");
        sb.append("body=").append(bodyStr).append("&");
        sb.append("nonceStr=").append(nonceStr).append("&");
        sb.append("timestamp=").append(timestamp);
        if (!StringUtils.isEmpty(token)) {
            sb.append("&").append("token=").append(token);
        }

        log.info("签名源:" + sb.toString());
        String sign = AlipaySignature.rsaSign(sb.toString(), requestPrikey, AlipayConstants.CHARSET_UTF8, AlipayConstants.SIGN_TYPE_RSA2);
        log.info("签名值:" + sign);
        req.setSign(sign);
        return req;
    }
    /**
     * 返回请求时对返回结果进行签名
     * */
    public static void signResponse(SignResponse response) {
        try {
            // 用私鑰加密
            StringBuilder sb = new StringBuilder();
            sb.append("code=").append(response.getCode()).append("&");
            if (response.getData() != null) {
                String result = JSON.toJSONString(response.getData(), SerializerFeature.SortField, SerializerFeature.WriteDateUseDateFormat);
                sb.append("data=").append(result).append("&");
            }
            sb.append("msg=").append(response.getMsg());
            String sign;
            try {
                log.info("签名源:" + sb.toString());
                sign = AlipaySignature.rsaSign(sb.toString(), responsePrikey,AlipayConstants.CHARSET_UTF8, AlipayConstants.SIGN_TYPE_RSA2);
                log.info("签名值:" + sign);
            } catch (AlipayApiException e) {
                log.error("響應加簽失敗", e);
                sign = null;
            }
            response.setSign(sign);
        } catch (Exception e) {
            log.error("加簽失敗", e);
        }
    }
    /**
     * 对请求返回结果进行验签
     * */
    public static boolean verifSignResponse(String signResponse) {
        JSONObject responseObj = null;

        try {
            responseObj = JSONObject.parseObject(signResponse);
            String sign = responseObj.getString("sign");
            String code = responseObj.getString("code");
            String msg = responseObj.getString("msg");
            Object data = responseObj.get("data");
            return verifResponse(sign, code, msg, data);
        } catch (Exception var7) {
            var7.printStackTrace();
            return false;
        }
    }
    /**
     * 对请求返回结果进行验签
     * */
    public static boolean verifSignResponse(JSONObject sign) {
        String signStr = sign.getString("sign");
        String code = sign.getString("code");
        String msg = sign.getString("msg");
        Object data = sign.get("data");
        return verifResponse(signStr, code, msg, data);
    }
    /**
     * 对请求返回结果进行验签
     * */
    public static boolean verifSignResponse(Map<String, Object> sign) {
        String signStr = String.valueOf(sign.get("sign"));
        String code = String.valueOf(sign.get("code"));
        String msg = String.valueOf(sign.get("msg"));
        Object data = sign.get("data");
        return verifResponse(signStr, code, msg, data);
    }

    public static boolean verifSignResponse(String sign, String code, String msg, Object data) {
        return verifResponse(sign, code, msg, data);
    }

    private static boolean verifResponse(String sign, String code, String msg, Object data) {
        StringBuilder sb = new StringBuilder();
        sb.append("code=").append(code).append("&");
        if (data != null) {
            String result = JSON.toJSONString(data, new SerializerFeature[]{SerializerFeature.MapSortField});
            sb.append("data=").append(result).append("&");
        }
        sb.append("msg=").append(msg);

        try {
            log.info("签名源:" + sb.toString());
            return AlipaySignature.verify(sb.toString(), sign, responsePubkey, AlipayConstants.CHARSET_UTF8, AlipayConstants.SIGN_TYPE_RSA2);
        } catch (AlipayApiException var7) {
            var7.printStackTrace();
            return false;
        }
    }

    public static String getNonceStr() {
        int len = random.nextInt(10) + 1;
        StringBuffer s = new StringBuffer();
        for(int i = 0; i < len; ++i) {
            s.append("0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz".charAt(random.nextInt("0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz".length())));
        }
        return s.toString();
    }
}
