package com.mcoin.mall.util;

import com.mcoin.mall.constant.Numbers;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class TimeIntervalUtil {

    private static final int SECONDS_TO_MILLIS = 1000;
    private static final int MINUTES_TO_SECONDS = 60;
    private static final int HOURS_TO_MINUTES = 60;
    private static final int DAY_TO_HOURS = 24;
    private static final int HOUR_TO_MILLIS = HOURS_TO_MINUTES * MINUTES_TO_SECONDS * SECONDS_TO_MILLIS;
    private static final int MINUTE_TO_MILLIS = MINUTES_TO_SECONDS * SECONDS_TO_MILLIS;
    private static final int DAY_TO_MILLIS = DAY_TO_HOURS * HOUR_TO_MILLIS;

    private static void checkNumericPart(String interval, String unitName) throws NumberFormatException {
        if (interval.length() == Numbers.ONE.getIntValue()) {
            throw new NumberFormatException("Numeric part is missing for " + unitName);
        }
    }

    public static int parseIntervalToMillis(String interval) {
        int delayMs = Numbers.ZERO.getIntValue();
        if (interval == null || interval.isEmpty()) {
            return delayMs;
        }

        try {
            if (interval.endsWith("m")) {
                checkNumericPart(interval, "minutes");
                delayMs = Integer.parseInt(interval.substring(Numbers.ZERO.getIntValue(), interval.length() - Numbers.ONE.getIntValue())) * MINUTE_TO_MILLIS;
            } else if (interval.endsWith("h")) {
                checkNumericPart(interval, "hours");
                delayMs = Integer.parseInt(interval.substring(Numbers.ZERO.getIntValue(), interval.length() - Numbers.ONE.getIntValue())) * HOUR_TO_MILLIS;
            } else if (interval.endsWith("d")) {
                checkNumericPart(interval, "days");
                delayMs = Integer.parseInt(interval.substring(Numbers.ZERO.getIntValue(), interval.length() - Numbers.ONE.getIntValue())) * DAY_TO_MILLIS;
            } else if (interval.endsWith("s")) {
                checkNumericPart(interval, "seconds");
                delayMs = Integer.parseInt(interval.substring(Numbers.ZERO.getIntValue(), interval.length() - Numbers.ONE.getIntValue())) * SECONDS_TO_MILLIS;
            } else {
                // Assuming an interval without a suffix is in milliseconds
                delayMs = Integer.parseInt(interval);
            }
        } catch (NumberFormatException e) {
            // Log error or throw exception if format is invalid
            log.error("Invalid time interval format: {}", interval);
            return Numbers.ZERO.getIntValue(); // Return 0 for any parsing error
        }
        return delayMs;
    }
} 