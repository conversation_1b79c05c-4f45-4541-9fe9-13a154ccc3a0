package com.mcoin.mall.util;

import com.mcoin.mall.constant.SettlementFileSuffixTypeEnum;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.Calendar;
import java.util.Date;

/**
 * 日期通用类
 *
 * <AUTHOR>
 */
public class TimeUtils {

    /**
     * 获取前一天的0点日期
     *
     * @param requestDate request data
     * @return Date date
     */
    public static Date getPreviousDayStartTime(Date requestDate) {
        LocalDateTime settlementLocalDate = LocalDateTime.ofInstant(requestDate.toInstant(), ZoneId.systemDefault());
        LocalDateTime startTime = settlementLocalDate.minusDays(1).truncatedTo(ChronoUnit.DAYS);
        DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern(JodaTimeUtil.DEFAULT_PATTERN);
        // 格式化 LocalDateTime 为字符串
        String startDateString = startTime.format(dateFormatter);
        return JodaTimeUtil.parse(startDateString, JodaTimeUtil.DEFAULT_PATTERN);
    }

    /**
     * 获取前一天的23：59：59日期
     *
     * @param requestDate request data
     * @return Date date
     */
    public static Date getPreviousDayEndTime(Date requestDate) {
        LocalDateTime settlementLocalDate = LocalDateTime.ofInstant(requestDate.toInstant(), ZoneId.systemDefault());
        LocalDateTime previousDayDateTime = settlementLocalDate.minusDays(1).withHour(23).withMinute(59).withSecond(59);
        DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern(JodaTimeUtil.DEFAULT_PATTERN);
        // 格式化 LocalDateTime 为字符串
        String endDateString = previousDayDateTime.format(dateFormatter);
        return JodaTimeUtil.parse(endDateString, JodaTimeUtil.DEFAULT_PATTERN);
    }

    /**
     * 获取当月上半月开始时间
     * 举例： requestDate: 2024-05-01
     * 返回时间2024-05-01 00:00:00
     *
     * @param requestDate request data
     * @return Date date
     */
    public static Date getCurrentMonthFirstHalfStartTime(Date requestDate) {
        LocalDateTime settlementLocalDate = LocalDateTime.ofInstant(requestDate.toInstant(), ZoneId.systemDefault());
        LocalDateTime firstHalfMonthTime = settlementLocalDate.withDayOfMonth(1).truncatedTo(ChronoUnit.DAYS);
        // 格式化 LocalDateTime 为字符串
        DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern(JodaTimeUtil.DEFAULT_PATTERN);
        String startDateString = firstHalfMonthTime.format(dateFormatter);
        return JodaTimeUtil.parse(startDateString, JodaTimeUtil.DEFAULT_PATTERN);
    }

    /**
     * 获取当月上半月结束结束时间
     * 举例： requestDate: 2024-05-01
     * 返回时间2024-05-15 23:59:59
     *
     * @param requestDate request data
     * @return Date date
     */
    public static Date getCurrentMonthFirstHalfEndTime(Date requestDate) {
        LocalDateTime settlementLocalDate = LocalDateTime.ofInstant(requestDate.toInstant(), ZoneId.systemDefault());
        LocalDateTime firstHalfMonthEndTime = settlementLocalDate.withDayOfMonth(15).with(LocalTime.MAX);
        // 格式化 LocalDateTime 为字符串
        DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern(JodaTimeUtil.DEFAULT_PATTERN);
        String endDateString = firstHalfMonthEndTime.format(dateFormatter);
        return JodaTimeUtil.parse(endDateString, JodaTimeUtil.DEFAULT_PATTERN);
    }

    /**
     * 获取上一月下半月开始时间
     * 举例： requestDate: 2024-05-01
     * 返回时间2024-04-16 00:00:00
     *
     * @param requestDate request data
     * @return Date date
     */
    public static Date getPreviousMonthSecondHalfStartTime(Date requestDate) {
        LocalDateTime settlementLocalDate = LocalDateTime.ofInstant(requestDate.toInstant(), ZoneId.systemDefault());
        LocalDateTime secondHalfMonthStartTime = settlementLocalDate.minusMonths(1).withDayOfMonth(16).truncatedTo(ChronoUnit.DAYS);
        // 格式化 LocalDateTime 为字符串
        DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern(JodaTimeUtil.DEFAULT_PATTERN);
        String startDateString = secondHalfMonthStartTime.format(dateFormatter);
        return JodaTimeUtil.parse(startDateString, JodaTimeUtil.DEFAULT_PATTERN);
    }

    /**
     * 获取上一月结束时间
     * 举例： requestDate: 2024-05-01
     * 返回时间2024-04-30 23:59:59
     *
     * @param requestDate request data
     * @return Date date
     */
    public static Date getPreviousMonthEndTime(Date requestDate) {
        LocalDateTime settlementLocalDate = LocalDateTime.ofInstant(requestDate.toInstant(), ZoneId.systemDefault());
        LocalDateTime secondHalfMonthEndTime = settlementLocalDate.withDayOfMonth(1).truncatedTo(ChronoUnit.DAYS).minusSeconds(1);
        // 格式化 LocalDateTime 为字符串
        DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern(JodaTimeUtil.DEFAULT_PATTERN);
        String endDateString = secondHalfMonthEndTime.format(dateFormatter);
        return JodaTimeUtil.parse(endDateString, JodaTimeUtil.DEFAULT_PATTERN);
    }

    /**
     * 获取上一月开始时间
     * 举例： requestDate: 2024-05-01
     * 返回时间2024-04-30 23:59:59
     *
     * @param requestDate request data
     * @return Date date
     */
    public static Date getPreviousMonthStartTime(Date requestDate) {
        LocalDateTime settlementLocalDate = LocalDateTime.ofInstant(requestDate.toInstant(), ZoneId.systemDefault());
        LocalDateTime secondHalfMonthEndTime = settlementLocalDate.minusMonths(1).withDayOfMonth(1).with(LocalTime.MIN);
        // 格式化 LocalDateTime 为字符串
        DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern(JodaTimeUtil.DEFAULT_PATTERN);
        String endDateString = secondHalfMonthEndTime.format(dateFormatter);
        return JodaTimeUtil.parse(endDateString, JodaTimeUtil.DEFAULT_PATTERN);
    }

    /**
     * 获取当月上半月结描述
     *
     * @param requestDate request data
     * @return Date desc
     */
    public static String getFirstHalfMonthDesc(Date requestDate) {
        LocalDateTime settlementLocalDate = LocalDateTime.ofInstant(requestDate.toInstant(), ZoneId.systemDefault());
        return StringUtils.join(settlementLocalDate.getMonthValue() < 10 ? StringUtils.join("0"
                , settlementLocalDate.getMonthValue()) : settlementLocalDate.getMonthValue()
                , SettlementFileSuffixTypeEnum.FIRST_HALF_MONTH.getDescription())
                ;
    }

    /**
     * 获取上月下半月结描述
     *
     * @param requestDate request data
     * @return Date desc
     */
    public static String getPreviousMonthSecondHalfDesc(Date requestDate) {
        LocalDateTime settlementLocalDate = LocalDateTime.ofInstant(requestDate.toInstant(), ZoneId.systemDefault());
        LocalDateTime secondHalfMonthEndTime = settlementLocalDate.withDayOfMonth(1).truncatedTo(ChronoUnit.DAYS).minusSeconds(1);
        return StringUtils.join(secondHalfMonthEndTime.getMonthValue() < 10 ? StringUtils.join("0"
                , secondHalfMonthEndTime.getMonthValue()) : secondHalfMonthEndTime.getMonthValue()
                , SettlementFileSuffixTypeEnum.SECOND_HALF_MONTH.getDescription());
    }

    /**
     * 获取上月月结描述
     *
     * @param requestDate request data
     * @return Date desc
     */
    public static String getPreviousMonthDesc(Date requestDate) {
        LocalDateTime settlementLocalDate = LocalDateTime.ofInstant(requestDate.toInstant(), ZoneId.systemDefault());
        LocalDateTime secondHalfMonthEndTime = settlementLocalDate.withDayOfMonth(1).truncatedTo(ChronoUnit.DAYS).minusSeconds(1);
        return StringUtils.join(secondHalfMonthEndTime.getMonthValue() < 10 ? StringUtils.join("0"
                , secondHalfMonthEndTime.getMonthValue()) : secondHalfMonthEndTime.getMonthValue()
                , SettlementFileSuffixTypeEnum.MONTHLY.getDescription());
    }


    /**
     * 获取本年开始时间
     * 举例： requestDate: 2024-07-01
     * 返回时间2024-01-01 00:00:00
     *
     * @param requestDate request data
     * @return Date date
     */
    public static Date getCurrentYearStartTime(Date requestDate) {
        LocalDateTime settlementLocalDate = LocalDateTime.ofInstant(requestDate.toInstant(), ZoneId.systemDefault());
        LocalDateTime firstHalfMonthTime = settlementLocalDate.withMonth(1).withDayOfMonth(1).truncatedTo(ChronoUnit.DAYS);
        // 格式化 LocalDateTime 为字符串
        DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern(JodaTimeUtil.DEFAULT_PATTERN);
        String startDateString = firstHalfMonthTime.format(dateFormatter);
        return JodaTimeUtil.parse(startDateString, JodaTimeUtil.DEFAULT_PATTERN);
    }

    /**
     * 获取本年上半年结束时间
     * 举例： requestDate: 2024-07-01
     * 返回时间2024-06-30 23:59:59
     *
     * @param requestDate request data
     * @return Date date
     */
    public static Date getCurrentYearFirstHalfEndTime(Date requestDate) {
        LocalDateTime settlementLocalDate = LocalDateTime.ofInstant(requestDate.toInstant(), ZoneId.systemDefault());
        LocalDateTime firstHalfMonthTime = settlementLocalDate.withMonth(7).withDayOfMonth(1).truncatedTo(ChronoUnit.DAYS).minusSeconds(1);;
        // 格式化 LocalDateTime 为字符串
        DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern(JodaTimeUtil.DEFAULT_PATTERN);
        String startDateString = firstHalfMonthTime.format(dateFormatter);
        return JodaTimeUtil.parse(startDateString, JodaTimeUtil.DEFAULT_PATTERN);
    }

    /**
     * 获取上年开始时间
     * 举例： requestDate: 2025-01-01
     * 返回时间2024-01-01 00:00:00
     * @param requestDate request data
     * @return Date date
     */
    public static Date getPreviousYearStartTime(Date requestDate) {
        LocalDateTime settlementLocalDate = LocalDateTime.ofInstant(requestDate.toInstant(), ZoneId.systemDefault());
        LocalDateTime firstHalfMonthTime = settlementLocalDate.minusYears(1).withDayOfMonth(1).withDayOfYear(1).truncatedTo(ChronoUnit.DAYS);
        // 格式化 LocalDateTime 为字符串
        DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern(JodaTimeUtil.DEFAULT_PATTERN);
        String startDateString = firstHalfMonthTime.format(dateFormatter);
        return JodaTimeUtil.parse(startDateString, JodaTimeUtil.DEFAULT_PATTERN);
    }

    /**
     * 获取上年下半年开始时间
     * 举例： requestDate: 2025-01-01
     * 返回时间2024-07-01 00:00:00
     * @param requestDate request data
     * @return Date date
     */
    public static Date getPreviousYearSecondHalfStartTime(Date requestDate) {
        LocalDateTime settlementLocalDate = LocalDateTime.ofInstant(requestDate.toInstant(), ZoneId.systemDefault());
        LocalDateTime firstHalfMonthTime = settlementLocalDate.minusYears(1).withMonth(7).withDayOfMonth(1).truncatedTo(ChronoUnit.DAYS);
        // 格式化 LocalDateTime 为字符串
        DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern(JodaTimeUtil.DEFAULT_PATTERN);
        String startDateString = firstHalfMonthTime.format(dateFormatter);
        return JodaTimeUtil.parse(startDateString, JodaTimeUtil.DEFAULT_PATTERN);
    }

    /**
     * 获取上年结束时间
     * 举例： requestDate: 2025-01-01
     * 返回时间2024-12-31 23:59:59
     *
     * @param requestDate request data
     * @return Date date
     */
    public static Date getPreviousYearEndTime(Date requestDate) {
        LocalDateTime settlementLocalDate = LocalDateTime.ofInstant(requestDate.toInstant(), ZoneId.systemDefault());
        LocalDateTime firstHalfMonthTime = settlementLocalDate.withMonth(1).withDayOfMonth(1).truncatedTo(ChronoUnit.DAYS).minusSeconds(1);
        // 格式化 LocalDateTime 为字符串
        DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern(JodaTimeUtil.DEFAULT_PATTERN);
        String startDateString = firstHalfMonthTime.format(dateFormatter);
        return JodaTimeUtil.parse(startDateString, JodaTimeUtil.DEFAULT_PATTERN);
    }

}
