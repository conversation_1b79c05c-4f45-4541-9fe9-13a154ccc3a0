package com.mcoin.mall.util;

import lombok.extern.slf4j.Slf4j;
import net.lingala.zip4j.ZipFile;
import net.lingala.zip4j.model.ZipParameters;
import net.lingala.zip4j.model.enums.AesKeyStrength;
import net.lingala.zip4j.model.enums.EncryptionMethod;

import java.io.*;
import java.nio.file.Files;
import java.util.List;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;
import java.util.zip.ZipOutputStream;

@Slf4j
public abstract class ZipUtils {

    public static void compress(File zipFile, List<File> srcFiles) throws IOException {
        try (OutputStream out = Files.newOutputStream(zipFile.toPath());
             ZipOutputStream zipOut = new ZipOutputStream(out)) {
            for (File srcFile : srcFiles) {
                if (!srcFile.isFile()) {
                    continue;
                }
                try (InputStream in = Files.newInputStream(srcFile.toPath())) {
                    ZipEntry zipEntry = new ZipEntry(srcFile.getName());
                    zipOut.putNextEntry(zipEntry);
                    int len;
                    byte[] buf = new byte[2048];
                    while ((len = in.read(buf)) >= 0) {
                        zipOut.write(buf, 0, len);
                    }
                }
            }
        }
    }

    /**
     * 解压缩ZIP文件
     * @param zipFile ZIP文件
     * @param destDir 目标路径
     */
    public static void decompress(File zipFile, File destDir) {
        byte[] buffer = new byte[1024];
        try (ZipInputStream zis = new ZipInputStream(new FileInputStream(zipFile))) {
            ZipEntry entry = zis.getNextEntry();
            while (entry != null) {
                File file = new File(destDir, entry.getName());
                if (entry.isDirectory()) {
                    file.mkdirs();
                } else {
                    File parent = file.getParentFile();
                    if (!parent.exists()) {
                        parent.mkdirs();
                    }
                    try (FileOutputStream fos = new FileOutputStream(file)) {
                        int len;
                        while ((len = zis.read(buffer)) > 0) {
                            fos.write(buffer, 0, len);
                        }
                    }
                }
                entry = zis.getNextEntry();
            }
        } catch (IOException e) {
            log.error(String.format("解压缩文件失败，文件名：%s", zipFile.getName()), e);
        }
    }


    public static void compress(File zipFile, List<File> srcFiles, String password) throws IOException {
        ZipParameters zipParameters = new ZipParameters();
        zipParameters.setEncryptFiles(true);
        zipParameters.setEncryptionMethod(EncryptionMethod.AES);
        // Below line is optional. AES 256 is used by default.
        // You can override it to use AES 128. AES 192 is supported only for extracting.
        zipParameters.setAesKeyStrength(AesKeyStrength.KEY_STRENGTH_256);
        try (ZipFile zipFilePassword = new ZipFile(zipFile, password.toCharArray())) {
            zipFilePassword.addFiles(srcFiles, zipParameters);
        }
    }
}
