package com.mcoin.mall.service.user.impl;

import com.mcoin.mall.bean.*;
import com.mcoin.mall.component.ContextHolder;
import com.mcoin.mall.dao.*;
import com.mcoin.mall.model.CouponsCtx;
import com.mcoin.mall.model.CouponsRequest;
import com.mcoin.mall.model.CouponsResponse;
import com.mcoin.mall.model.api.ApiCollectionCntRequest;
import com.mcoin.mall.security.UserInfo;
import com.mcoin.mall.util.ConfigUtils;
import com.mcoin.mall.util.JsonTestUtils;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.slf4j.Logger;
import org.springframework.context.MessageSource;

import java.util.Calendar;
import java.util.Collections;
import java.util.GregorianCalendar;
import java.util.Locale;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

class UserServiceImplTest {
    @Mock
    FookPlatformOrdercodeDao fookPlatformOrdercodeDao;
    @Mock
    FookPlatformOrderinfoDao fookPlatformOrderinfoDao;
    @Mock
    FookStoresDao fookStoresDao;
    @Mock
    FookBusinessStoreProductDao fookBusinessStoreProductDao;
    @Mock
    FookBusinessProductTranslationsDao fookBusinessProductTranslationsDao;
    @Mock
    MessageSource messageSource;
    @Mock
    FookPlatformUsercollectionDao collectionDao;
    @Mock
    ContextHolder contextHolder;
    @Mock
    Logger log;
    @InjectMocks
    UserServiceImpl userServiceImpl;
    @Mock
    FookPlatformUsercollectionDao fookPlatformUsercollectionDao;
    @Mock
    FookBusinessProductDao fookBusinessProductDao;
    
    private MockedStatic<ConfigUtils> mockedStatic;

    @AfterEach
    void cleanup() {
        if (mockedStatic != null) {
            mockedStatic.close();
        }
    }

    @BeforeEach
    void setup() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testCoupons() {
        // given
        mockedStatic = Mockito.mockStatic(ConfigUtils.class);
        mockedStatic.when(() -> ConfigUtils.getProperty(anyString())).thenReturn("http://127.0.0.1/");
        when(fookPlatformOrdercodeDao.coupons(any())).thenReturn(
                Collections.singletonList(JsonTestUtils.getObjectFromJson("json/coupons.json", "singleCoupon", Coupons.class))
        );
        
        CouponsCnt cnt = new CouponsCnt();
        cnt.setCount(10);
        cnt.setStoreid(1);
        when(fookPlatformOrdercodeDao.couponsCnt(any())).thenReturn(Collections.singletonList(cnt));
        
        when(fookPlatformOrderinfoDao.getOrderInfoByOrderId(any())).thenReturn(
                Collections.singletonList(JsonTestUtils.getObjectFromJson("json/orderinfo.json", "singleOrderInfo", FookPlatformOrderinfo.class))
        );
        
        when(fookStoresDao.getStores(any())).thenReturn(
                Collections.singletonList(JsonTestUtils.getObjectFromJson("json/stores.json", "singleStores", FookStores.class))
        );
        
        when(fookBusinessProductTranslationsDao.getTranslationsByIds(any(), anyString())).thenReturn(
                Collections.singletonList(JsonTestUtils.getObjectFromJson("json/businessProductTranslations.json", 
                        "businessProductTl", FookBusinessProductTranslations.class))
        );
        
        when(contextHolder.getLocale()).thenReturn(new Locale("en", "US"));
        
        UserInfo userInfo = new UserInfo();
        userInfo.setUserId(0);
        when(contextHolder.getAuthUserInfo()).thenReturn(userInfo);

        // when
        CouponsCtx ctx = new CouponsCtx();
        ctx.setRequest(new CouponsRequest());
        ctx.setUserId(0);
        ctx.setLanguage("zh");
        ctx.setClientSource("MPAY_SERVER");
        CouponsResponse result = userServiceImpl.getCoupons(ctx);
        
        // then
        assertTrue(result.getSnatchList().size() > 0);
        assertTrue(result.getSnatchList().get(0).getOrderCodes().size() > 0);
    }

    @ParameterizedTest
    @MethodSource("provideCollectionTestData")
    void testGetCollections(ApiCollectionCntRequest request, int userId, int expectedResult) {
        // given
        mockedStatic = Mockito.mockStatic(ConfigUtils.class);
        mockedStatic.when(() -> ConfigUtils.getProperty(anyString())).thenReturn("http://127.0.0.1/");
        
        UsercollectionCnt collectionCnt = new UsercollectionCnt();
        collectionCnt.setCollectionid(1);
        collectionCnt.setCnt(1);
        when(collectionDao.getCountByIds(any())).thenReturn(Collections.singletonList(collectionCnt));
        
        FookPlatformUsercollection collection = new FookPlatformUsercollection();
        collection.setId(0);
        collection.setUserid(0);
        collection.setCollectionid(0);
        collection.setType(1);
        collection.setCreateTime(new GregorianCalendar(2024, Calendar.MAY, 14, 16, 36).getTime());
        collection.setEnable(0);
        when(collectionDao.selectCollections(anyInt(), any())).thenReturn(Collections.singletonList(collection));

        UsercollectionCnt usercollectionCnt = new UsercollectionCnt();
        usercollectionCnt.setCollectionid(0);
        usercollectionCnt.setCnt(0);
        when(fookPlatformUsercollectionDao.getCountByIds(any())).thenReturn(Collections.singletonList(usercollectionCnt));
        
        FookPlatformUsercollection platformUsercollection = new FookPlatformUsercollection();
        platformUsercollection.setId(0);
        platformUsercollection.setUserid(0);
        platformUsercollection.setCollectionid(0);
        platformUsercollection.setType(0);
        platformUsercollection.setCreateTime(new GregorianCalendar(2024, Calendar.MAY, 14, 16, 36).getTime());
        platformUsercollection.setEnable(0);
        when(fookPlatformUsercollectionDao.selectCollections(anyInt(), any())).thenReturn(
                Collections.singletonList(platformUsercollection)
        );
        
        when(fookBusinessProductDao.selectList(any())).thenReturn(
                Collections.singletonList(JsonTestUtils.getObjectFromJson("json/businessProduct.json", "singleBp", FookBusinessProduct.class))
        );

        // when & then
        assertEquals(expectedResult, userServiceImpl.getCollections(request, userId).getGoodsId());
    }

    private static Stream<Arguments> provideCollectionTestData() {
        return Stream.of(
                Arguments.of(getApiCollectionCntRequest(), 0, 1)
        );
    }

    private static ApiCollectionCntRequest getApiCollectionCntRequest() {
        ApiCollectionCntRequest req = new ApiCollectionCntRequest();
        req.setGoodsId(0);
        return req;
    }
} 