-- 清理测试数据
DELETE FROM fook_platform_ordercode WHERE id IN (20001, 20002, 20003, 20004, 20005, 20006);
DELETE FROM fook_platform_orderinfo WHERE orderid IN (20001, 20002, 20003, 20004, 20005, 20006);
DELETE FROM fook_platform_order WHERE id IN (20001, 20002, 20003, 20004, 20005, 20006);
DELETE FROM fook_business_product WHERE id IN (20001, 20002, 20003);
DELETE FROM fook_stores WHERE id IN (20001, 20002);
DELETE FROM fook_macaupass_user WHERE user_id IN (20001, 20002);
DELETE FROM fook_platform_userinfo WHERE id IN (20001, 20002);

-- 插入测试用户数据
INSERT INTO fook_macaupass_user (user_id, customid, phone, openid, point, create_time, update_time) VALUES
(20001, 'TEST_CUSTOM_ID_001', '***********', 'TEST_OPENID_001', 1000, NOW(), NOW()),
(20002, 'TEST_CUSTOM_ID_002', '***********', 'TEST_OPENID_002', 500, NOW(), NOW());

INSERT INTO fook_platform_userinfo (id, nick_name, create_time) VALUES
(20001, '测试用户001', NOW()),
(20002, '测试用户002', NOW());

-- 插入测试商店数据
INSERT INTO fook_stores (id, name, img, address, enable, created_at, updated_at) VALUES
(20001, '测试商店001', 'test_store_001.jpg', '澳门测试地址001', 1, NOW(), NOW()),
(20002, '测试商店002', 'test_store_002.jpg', '澳门测试地址002', 1, NOW(), NOW());

-- 插入测试商品数据
INSERT INTO fook_business_product (id, goods_id, businessid, title, `desc`, price, retail_price, stock, sales, actual_sales,
                                   limited_number, is_allow_refund, shelf_status, img, type) VALUES
(20001, 'GOODS_001', 1, '测试优惠券001', '测试优惠券描述001', 50.00, 100.00, 100, 10, 10,
 5, 1, 1, 'test_coupon_001.jpg', 1),
(20002, 'GOODS_002', 1, '测试优惠券002', '测试优惠券描述002', 30.00, 60.00, 50, 5, 5,
 3, 1, 1, 'test_coupon_002.jpg', 2),
(20003, 'GOODS_003', 1, '测试优惠券003', '测试优惠券描述003', 80.00, 150.00, 200, 20, 20,
 10, 1, 1, 'test_coupon_003.jpg', 3);

-- 插入测试订单数据
INSERT INTO fook_platform_order (id, order_no, userid, status, refund_status, total_amount, payment_amount, create_time, payment_time) VALUES
(20001, 'TEST_ORDER_NO_001', 20001, 2, 1, 50.00, 50.00, NOW(), NOW()),
(20002, 'TEST_ORDER_NO_002', 20001, 2, 1, 30.00, 30.00, NOW(), NOW()),
(20003, 'TEST_ORDER_NO_003', 20001, 2, 1, 80.00, 80.00, NOW(), NOW()),
(20004, 'TEST_ORDER_NO_004', 20002, 2, 1, 50.00, 50.00, NOW(), NOW()),
(20005, 'TEST_ORDER_NO_005', 20001, 4, 3, 50.00, 50.00, NOW(), NOW()),
(20006, 'TEST_ORDER_NO_006', 20001, 2, 1, 40.00, 40.00, NOW(), NOW());  -- 过期优惠券的订单

-- 插入测试订单信息数据
INSERT INTO fook_platform_orderinfo (id, orderid, prodcutid, number, product_price, order_amount, img, title_snapshots, desc_snapshots,
                                      details_snapshots, retail_price, tnc, type, vaild_start_time, vaild_end_time, is_weekend, no_useday,
                                      mpayintegral) VALUES
(20001, 20001, 20001, 1, 50.00, 50.00, 'test_coupon_001.jpg', '测试优惠券001', '测试优惠券描述001', '详细描述001', 100.00, '使用条款001', 1,
 DATE_ADD(NOW(), INTERVAL -1 DAY), DATE_ADD(NOW(), INTERVAL 30 DAY), 0, NULL, 0),
(20002, 20002, 20002, 1, 30.00, 30.00, 'test_coupon_002.jpg', '测试优惠券002', '测试优惠券描述002', '详细描述002', 60.00, '使用条款002', 2,
 DATE_ADD(NOW(), INTERVAL -1 DAY), DATE_ADD(NOW(), INTERVAL 30 DAY), 0, NULL, 0),
(20003, 20003, 20003, 1, 80.00, 80.00, 'test_coupon_003.jpg', '测试优惠券003', '测试优惠券描述003', '详细描述003', 150.00, '使用条款003', 3,
 DATE_ADD(NOW(), INTERVAL -1 DAY), DATE_ADD(NOW(), INTERVAL 30 DAY), 0, NULL, 0),
(20004, 20004, 20001, 1, 50.00, 50.00, 'test_coupon_001.jpg', '测试优惠券001', '测试优惠券描述001', '详细描述001', 100.00, '使用条款001', 1,
 DATE_ADD(NOW(), INTERVAL -1 DAY), DATE_ADD(NOW(), INTERVAL 30 DAY), 0, NULL, 0),
(20005, 20005, 20001, 1, 50.00, 50.00, 'test_coupon_001.jpg', '测试优惠券001', '测试优惠券描述001', '详细描述001', 100.00, '使用条款001', 1,
 DATE_ADD(NOW(), INTERVAL -1 DAY), DATE_ADD(NOW(), INTERVAL 30 DAY), 0, NULL, 0),
(20006, 20006, 20001, 1, 40.00, 40.00, 'test_coupon_001.jpg', '测试过期优惠券', '测试过期优惠券描述', '详细描述006', 80.00, '使用条款006', 1,
 DATE_ADD(NOW(), INTERVAL -10 DAY), DATE_ADD(NOW(), INTERVAL -1 DAY), 0, NULL, 0);  -- 已过期的优惠券

-- 插入测试订单码数据
INSERT INTO fook_platform_ordercode (id, orderid, code, status, refund_status, shopid, orderinfo_id) VALUES
(20001, 20001, 'TEST_CODE_001', 1, 1, 20001, 20001),  -- 未使用
(20002, 20002, 'TEST_CODE_002', 2, 1, 20002, 20002),  -- 已使用
(20003, 20003, 'TEST_CODE_003', 1, 1, 20001, 20003),  -- 未使用
(20004, 20004, 'TEST_CODE_004', 1, 1, 20001, 20004),  -- 未使用 (其他用户)
(20005, 20005, 'TEST_CODE_005', 1, 1, 20001, 20005),  -- 未使用
(20006, 20006, 'TEST_CODE_006', 1, 1, 20001, 20006);  -- 未使用但已过期
